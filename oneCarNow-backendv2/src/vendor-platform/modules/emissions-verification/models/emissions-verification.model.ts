import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@/vendor-platform/db';

export type HologramType = '00' | '0' | '1' | '2';
export type VerificationStatus = 'pending_customer' | 'completed' | 'expired' | 'cancelled';

export interface IEmissionsVerification extends Document {
  _id: mongoose.Types.ObjectId;
  vehiclePlate: string;
  vehicleId?: mongoose.Types.ObjectId; // Referencia al vehículo en el sistema principal
  verificationCenterId: mongoose.Types.ObjectId;
  verificationCenterCode: string; // Código del verificentro para búsquedas rápidas

  // Información del proceso
  status: VerificationStatus;
  verificationDate: Date;
  nextVerificationDate: Date;
  uniqueLink: string; // Link único para que el cliente complete el proceso

  // Evidencias del vendor
  vendorEvidence: {
    vehiclePhoto: string; // URL de la foto del vehículo durante verificación
    circulationCard: string; // URL del TDC
    uploadedAt: Date;
    uploadedBy: mongoose.Types.ObjectId; // Usuario vendor que subió
  };

  // Evidencias del cliente
  customerEvidence?: {
    verificationCertificate?: string; // URL del certificado
    hologramType?: HologramType;
    hologramPhoto?: string; // URL de la foto del holograma
    isExempt?: boolean; // Si obtuvo holograma 00
    uploadedAt?: Date;
    customerNotes?: string;
  };

  // Cálculos automáticos
  calculatedNextDate: Date;
  exemptUntil?: Date; // Para vehículos híbridos/eléctricos o holograma 00

  // Metadatos
  createdBy: mongoose.Types.ObjectId; // Usuario que registró la verificación
  remindersSent: number; // Contador de recordatorios enviados
  lastReminderSent?: Date;

  createdAt: Date;
  updatedAt: Date;
}

const EmissionsVerificationSchema = new Schema<IEmissionsVerification>(
  {
    vehiclePlate: { type: String, required: true, uppercase: true },
    vehicleId: { type: Schema.Types.ObjectId, ref: 'StockVehicle' },
    verificationCenterId: { type: Schema.Types.ObjectId, ref: 'VerificationCenter', required: true },
    verificationCenterCode: { type: String, required: true },

    status: {
      type: String,
      enum: ['pending_customer', 'completed', 'expired', 'cancelled'],
      default: 'pending_customer',
    },
    verificationDate: { type: Date, required: true },
    nextVerificationDate: { type: Date },
    uniqueLink: { type: String, required: true, unique: true },

    vendorEvidence: {
      vehiclePhoto: { type: String, required: true },
      circulationCard: { type: String, required: true },
      uploadedAt: { type: Date, default: Date.now },
      uploadedBy: { type: Schema.Types.ObjectId, ref: 'Users', required: true },
    },

    customerEvidence: {
      verificationCertificate: { type: String },
      hologramType: {
        type: String,
        enum: ['00', '0', '1', '2'],
      },
      hologramPhoto: { type: String },
      isExempt: { type: Boolean, default: false },
      uploadedAt: { type: Date },
      customerNotes: { type: String },
    },

    calculatedNextDate: { type: Date, required: true },
    exemptUntil: { type: Date },

    createdBy: { type: Schema.Types.ObjectId, ref: 'Users', required: true },
    remindersSent: { type: Number, default: 0 },
    lastReminderSent: { type: Date },
  },
  {
    timestamps: true,
  }
);

// Índices para optimizar consultas
EmissionsVerificationSchema.index({ vehiclePlate: 1, verificationDate: -1 });
EmissionsVerificationSchema.index({ uniqueLink: 1 }, { unique: true });
EmissionsVerificationSchema.index({ status: 1, nextVerificationDate: 1 });
EmissionsVerificationSchema.index({ verificationCenterId: 1, verificationDate: -1 });
EmissionsVerificationSchema.index({ vehicleId: 1, verificationDate: -1 });

// Virtual para obtener información del centro de verificación
EmissionsVerificationSchema.virtual('verificationCenter', {
  ref: 'VerificationCenter',
  localField: 'verificationCenterId',
  foreignField: '_id',
  justOne: true,
});

EmissionsVerificationSchema.set('toObject', { virtuals: true });
EmissionsVerificationSchema.set('toJSON', { virtuals: true });

export const EmissionsVerification = vendorDB.model<IEmissionsVerification>(
  'EmissionsVerification',
  EmissionsVerificationSchema
);
