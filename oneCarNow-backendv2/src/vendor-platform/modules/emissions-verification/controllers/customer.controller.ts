import { Request, Response } from 'express';
import { EmissionsVerificationService } from '../services/emissions-verification.service';

export class CustomerVerificationController {
  /**
   * Obtener información de verificación por link único
   */
  static async getVerificationInfo(req: Request, res: Response) {
    try {
      const { uniqueLink } = req.params;

      if (!uniqueLink) {
        return res.status(400).json({
          success: false,
          message: 'Link único requerido',
        });
      }

      const verification = await EmissionsVerificationService.getVerificationByLink(uniqueLink);

      return res.json({
        success: true,
        data: verification,
      });
    } catch (error) {
      return res.status(404).json({
        success: false,
        message: error instanceof Error ? error.message : 'Ocurrió un error desconocido',
      });
    }
  }

  /**
   * Subir evidencias del customer
   */
  static async uploadEvidence(req: Request, res: Response) {
    try {
      const { uniqueLink } = req.params;
      const { verificationCertificate, hologramType, hologramPhoto, isExempt, customerNotes } = req.body;

      // Validaciones básicas
      if (!verificationCertificate && !hologramPhoto) {
        return res.status(400).json({
          success: false,
          message: 'Se requiere al menos el certificado de verificación o la foto del holograma',
        });
      }

      const verification = await EmissionsVerificationService.updateCustomerEvidence(uniqueLink, {
        verificationCertificate,
        hologramType,
        hologramPhoto,
        isExempt,
        customerNotes,
      });

      return res.json({
        success: true,
        message: 'Evidencias subidas exitosamente',
        data: verification,
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Ocurrió un error desconocido',
      });
    }
  }

  /**
   * Obtener opciones de hologramas disponibles
   */
  static async getHologramOptions(req: Request, res: Response) {
    try {
      const hologramOptions = [
        {
          type: '00',
          name: 'Holograma 00',
          description: 'Exento por 2 años',
          color: 'Doble Cero',
          exemptionPeriod: '2 años',
        },
        {
          type: '0',
          name: 'Holograma 0',
          description: 'Verificación cada 6 meses',
          color: 'Verde',
          exemptionPeriod: null,
        },
        {
          type: '1',
          name: 'Holograma 1',
          description: 'Verificación cada 6 meses',
          color: 'Amarillo',
          exemptionPeriod: null,
        },
        {
          type: '2',
          name: 'Holograma 2',
          description: 'Verificación cada 6 meses',
          color: 'Rosa',
          exemptionPeriod: null,
        },
      ];

      res.json({
        success: true,
        data: hologramOptions,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Ocurrió un error desconocido',
      });
    }
  }

  /**
   * Validar si el proceso está completo
   */
  static async validateCompletion(req: Request, res: Response) {
    try {
      const { uniqueLink } = req.params;

      const verification = await EmissionsVerificationService.getVerificationByLink(uniqueLink);

      const isComplete =
        verification.status === 'completed' &&
        verification.customerEvidence?.verificationCertificate &&
        verification.customerEvidence?.hologramPhoto;

      res.json({
        success: true,
        data: {
          isComplete,
          status: verification.status,
          missingFields: {
            certificate: !verification.customerEvidence?.verificationCertificate,
            hologramPhoto: !verification.customerEvidence?.hologramPhoto,
            hologramType: !verification.customerEvidence?.hologramType,
          },
          nextVerificationDate: verification.nextVerificationDate,
        },
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Ocurrió un error desconocido',
      });
    }
  }

  /**
   * Obtener información sobre la próxima verificación
   */
  static async getNextVerificationInfo(req: Request, res: Response) {
    try {
      const { uniqueLink } = req.params;

      const verification = await EmissionsVerificationService.getVerificationByLink(uniqueLink);

      if (verification.status !== 'completed') {
        return res.status(400).json({
          success: false,
          message:
            'La verificación debe estar completada para obtener información de la próxima verificación',
        });
      }

      const today = new Date();
      const nextDate = verification.nextVerificationDate;
      const timeDiff = nextDate.getTime() - today.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

      let status = 'upcoming';
      if (daysDiff < 0) {
        status = 'overdue';
      } else if (daysDiff <= 30) {
        status = 'due_soon';
      }

      return res.json({
        success: true,
        data: {
          nextVerificationDate: nextDate,
          daysRemaining: daysDiff,
          status,
          isExempt: !!verification.exemptUntil,
          exemptUntil: verification.exemptUntil,
          hologramType: verification.customerEvidence?.hologramType,
        },
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Ocurrió un error desconocido',
      });
    }
  }
}
