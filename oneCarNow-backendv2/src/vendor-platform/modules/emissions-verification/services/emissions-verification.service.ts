/* eslint-disable max-params */
import { v4 as uuidv4 } from 'uuid';
import { EmissionsVerification, IEmissionsVerification } from '../models/emissions-verification.model';
import { VerificationCenter } from '../models/verification-center.model';
import { VerificationLogicService } from './verification-logic.service';
import StockVehicleModel from '@/models/StockVehicleSchema';

export interface CreateVerificationData {
  vehiclePlate: string;
  verificationCenterId: string;
  verificationDate: Date;
  vehiclePhoto: string;
  circulationCard: string;
  createdBy: string;
}

export interface UpdateCustomerEvidenceData {
  verificationCertificate?: string;
  hologramType?: '00' | '0' | '1' | '2';
  hologramPhoto?: string;
  isExempt?: boolean;
  customerNotes?: string;
}

export class EmissionsVerificationService {
  /**
   * Busca vehículo por placa en la base de datos interna
   */
  static async searchVehicleByPlate(plate: string) {
    try {
      const cleanPlate = plate.replace(/[^A-Z0-9]/g, '').toUpperCase();
      const vehicle = await StockVehicleModel.findOne({
        'carPlates.plates': { $regex: new RegExp(cleanPlate, 'i') },
      }).select('carPlates brand model year color vin isElectric registrationDate');

      return vehicle;
    } catch (error) {
      const err = error as any;
      throw new Error(`Error al buscar vehículo: ${err.message}`);
    }
  }

  /**
   * Obtiene centros de verificación por estado
   */
  static async getVerificationCentersByState(state: string) {
    try {
      return await VerificationCenter.find({
        state: state.toUpperCase(),
        isActive: true,
      }).sort({ name: 1 });
    } catch (error) {
      const err = error as any;
      throw new Error(`Error al obtener centros de verificación: ${err.message}`);
    }
  }

  /**
   * Registra una nueva verificación (para vendors)
   */
  static async createVerification(data: CreateVerificationData): Promise<IEmissionsVerification> {
    try {
      // Validar que existe el centro de verificación
      const verificationCenter = await VerificationCenter.findById(data.verificationCenterId);
      if (!verificationCenter) {
        throw new Error('Centro de verificación no encontrado');
      }

      // Buscar el vehículo en el sistema
      const vehicle = await this.searchVehicleByPlate(data.vehiclePlate);

      // Generar link único
      const uniqueLink = uuidv4();

      // Calcular fecha de próxima verificación
      const isHybridOrElectric = vehicle?.isElectric || false;
      const calculationResult = VerificationLogicService.calculateNextVerificationDate(
        data.vehiclePlate,
        data.verificationDate,
        undefined, // Sin holograma aún
        isHybridOrElectric,
        undefined
      );

      const verification = new EmissionsVerification({
        vehiclePlate: data.vehiclePlate.toUpperCase(),
        vehicleId: vehicle?._id,
        verificationCenterId: data.verificationCenterId,
        verificationCenterCode: verificationCenter.code,
        verificationDate: data.verificationDate,
        nextVerificationDate: calculationResult.nextVerificationDate,
        calculatedNextDate: calculationResult.nextVerificationDate,
        exemptUntil: calculationResult.exemptUntil,
        uniqueLink,
        vendorEvidence: {
          vehiclePhoto: data.vehiclePhoto,
          circulationCard: data.circulationCard,
          uploadedAt: new Date(),
          uploadedBy: data.createdBy,
        },
        createdBy: data.createdBy,
        status: 'pending_customer',
      });

      await verification.save();
      return verification;
    } catch (error) {
      const err = error as any;
      throw new Error(`Error al crear verificación: ${err.message}`);
    }
  }

  /**
   * Obtiene verificación por link único (para customers)
   */
  static async getVerificationByLink(uniqueLink: string) {
    try {
      const verification = await EmissionsVerification.findOne({ uniqueLink }).populate(
        'verificationCenter',
        'name code location'
      );

      if (!verification) {
        throw new Error('Verificación no encontrada');
      }

      return verification;
    } catch (error) {
      const err = error as any;
      throw new Error(`Error al obtener verificación: ${err.message}`);
    }
  }

  /**
   * Actualiza evidencias del customer
   */
  static async updateCustomerEvidence(
    uniqueLink: string,
    data: UpdateCustomerEvidenceData
  ): Promise<IEmissionsVerification> {
    try {
      const verification = await EmissionsVerification.findOne({ uniqueLink });
      if (!verification) {
        throw new Error('Verificación no encontrada');
      }

      if (verification.status === 'completed') {
        throw new Error('Esta verificación ya ha sido completada');
      }

      // Actualizar evidencias del customer
      verification.customerEvidence = {
        ...verification.customerEvidence,
        ...data,
        uploadedAt: new Date(),
      };

      // Si se proporciona el tipo de holograma, recalcular fecha
      if (data.hologramType) {
        const vehicle = verification.vehicleId
          ? await StockVehicleModel.findById(verification.vehicleId)
          : null;

        const isHybridOrElectric = vehicle?.isElectric || false;
        const calculationResult = VerificationLogicService.calculateNextVerificationDate(
          verification.vehiclePlate,
          verification.verificationDate,
          data.hologramType,
          isHybridOrElectric,
          vehicle?.get('registrationDate')
        );

        verification.nextVerificationDate = calculationResult.nextVerificationDate;
        verification.calculatedNextDate = calculationResult.nextVerificationDate;
        verification.exemptUntil = calculationResult.exemptUntil;
      }

      // Marcar como completado si se subieron todas las evidencias requeridas
      if (data.verificationCertificate && data.hologramPhoto) {
        verification.status = 'completed';
      }

      await verification.save();
      return verification;
    } catch (error) {
      const err = error as any;
      throw new Error(`Error al actualizar evidencias: ${err.message}`);
    }
  }

  /**
   * Obtiene historial de verificaciones por vehículo
   */
  static async getVehicleVerificationHistory(vehiclePlate: string) {
    try {
      const cleanPlate = vehiclePlate.replace(/[^A-Z0-9]/g, '').toUpperCase();
      return await EmissionsVerification.find({ vehiclePlate: cleanPlate })
        .populate('verificationCenter', 'name code location')
        .sort({ verificationDate: -1 });
    } catch (error) {
      const err = error as any;
      throw new Error(`Error al obtener historial: ${err.message}`);
    }
  }

  /**
   * Obtiene verificaciones por centro
   */
  static async getVerificationsByCenter(
    verificationCenterId: string,
    page: number = 1,
    limit: number = 20,
    status?: string
  ) {
    try {
      const query: any = { verificationCenterId };
      if (status) {
        query.status = status;
      }

      const skip = (page - 1) * limit;

      const [verifications, total] = await Promise.all([
        EmissionsVerification.find(query)
          .populate('verificationCenter', 'name code')
          .sort({ verificationDate: -1 })
          .skip(skip)
          .limit(limit),
        EmissionsVerification.countDocuments(query),
      ]);

      return {
        verifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      const err = error as any;
      throw new Error(`Error al obtener verificaciones: ${err.message}`);
    }
  }

  /**
   * Obtiene verificaciones que necesitan recordatorio
   */
  static async getVerificationsNeedingReminder(daysThreshold: number = 30) {
    try {
      const thresholdDate = new Date();
      thresholdDate.setDate(thresholdDate.getDate() + daysThreshold);

      return await EmissionsVerification.find({
        status: 'completed',
        nextVerificationDate: { $lte: thresholdDate },
        $or: [
          { lastReminderSent: { $exists: false } },
          {
            lastReminderSent: {
              $lte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Última vez hace más de 7 días
            },
          },
        ],
      }).populate('verificationCenter', 'name code location');
    } catch (error) {
      const err = error as any;
      throw new Error(`Error al obtener verificaciones para recordatorio: ${err.message}`);
    }
  }

  /**
   * Marca recordatorio como enviado
   */
  static async markReminderSent(verificationId: string) {
    try {
      await EmissionsVerification.findByIdAndUpdate(verificationId, {
        $inc: { remindersSent: 1 },
        lastReminderSent: new Date(),
      });
    } catch (error) {
      const err = error as any;
      throw new Error(`Error al marcar recordatorio: ${err.message}`);
    }
  }
}
