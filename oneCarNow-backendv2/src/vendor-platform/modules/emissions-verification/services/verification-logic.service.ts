/* eslint-disable max-params */
import { HologramType } from '../models/emissions-verification.model';

export interface VerificationRule {
  lastDigit: number[];
  color: string;
  months: number[];
}

export interface CalculationResult {
  nextVerificationDate: Date;
  exemptUntil?: Date;
  verificationMonths: number[];
  hologramColor: string;
}

export class VerificationLogicService {
  private static readonly VERIFICATION_RULES: VerificationRule[] = [
    { lastDigit: [5, 6], color: 'Yellow', months: [1, 2, 7, 8] }, // Jan-Feb / Jul-Aug
    { lastDigit: [7, 8], color: 'Pink', months: [2, 3, 8, 9] }, // Feb-Mar / Aug-Sep
    { lastDigit: [3, 4], color: 'Red', months: [3, 4, 9, 10] }, // Mar-Apr / Sep-Oct
    { lastDigit: [1, 2], color: 'Green', months: [4, 5, 10, 11] }, // Apr-May / Oct-Nov
    { lastDigit: [9, 0], color: 'Blue', months: [5, 6, 11, 12] }, // May-Jun / Nov-Dec
  ];

  /**
   * Obtiene el último dígito de una placa
   */
  static getLastDigit(plate: string): number {
    const cleanPlate = plate.replace(/[^A-Z0-9]/g, '');
    const lastChar = cleanPlate.slice(-1);
    return parseInt(lastChar) || 0;
  }

  /**
   * Obtiene la regla de verificación basada en el último dígito de la placa
   */
  static getVerificationRule(plate: string): VerificationRule {
    const lastDigit = this.getLastDigit(plate);
    const rule = this.VERIFICATION_RULES.find((r) => r.lastDigit.includes(lastDigit));
    if (!rule) {
      throw new Error(`No se encontró regla de verificación para el dígito: ${lastDigit}`);
    }
    return rule;
  }

  /**
   * Calcula la próxima fecha de verificación basada en el holograma obtenido
   */
  static calculateNextVerificationDate(
    plate: string,
    currentDate: Date,
    hologramType?: HologramType,
    isHybridOrElectric: boolean = false,
    vehicleRegistrationDate?: Date
  ): CalculationResult {
    const rule = this.getVerificationRule(plate);

    // Vehículos híbridos o eléctricos - exentos por 8 años
    if (isHybridOrElectric && vehicleRegistrationDate) {
      const exemptUntil = new Date(vehicleRegistrationDate);
      exemptUntil.setFullYear(exemptUntil.getFullYear() + 8);

      return {
        nextVerificationDate: exemptUntil,
        exemptUntil,
        verificationMonths: rule.months,
        hologramColor: rule.color,
      };
    }

    // Holograma 00 - exento por 2 años
    if (hologramType === '00') {
      const exemptUntil = new Date(currentDate);
      exemptUntil.setFullYear(exemptUntil.getFullYear() + 2);

      return {
        nextVerificationDate: exemptUntil,
        exemptUntil,
        verificationMonths: rule.months,
        hologramColor: rule.color,
      };
    }

    // Holograma 0, 1, 2 - verificación cada 6 meses
    if (hologramType && ['0', '1', '2'].includes(hologramType)) {
      const nextDate = this.calculateNextSixMonthDate(currentDate, rule.months);
      return {
        nextVerificationDate: nextDate,
        verificationMonths: rule.months,
        hologramColor: rule.color,
      };
    }

    // Caso por defecto - primera verificación o sin holograma especificado
    const nextDate = this.calculateNextSixMonthDate(currentDate, rule.months);
    return {
      nextVerificationDate: nextDate,
      verificationMonths: rule.months,
      hologramColor: rule.color,
    };
  }

  /**
   * Calcula la próxima fecha dentro de los meses de verificación correspondientes
   */
  private static calculateNextSixMonthDate(currentDate: Date, verificationMonths: number[]): Date {
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();

    // Encontrar el próximo mes de verificación
    let nextMonth = verificationMonths.find((month) => month > currentMonth);
    let nextYear = currentYear;

    // Si no hay un mes válido en el año actual, tomar el primer mes del siguiente año
    if (!nextMonth) {
      nextMonth = verificationMonths[0];
      nextYear = currentYear + 1;
    }

    // Crear la fecha para el último día del mes de verificación
    const nextDate = new Date(nextYear, nextMonth - 1, 1);
    // Ir al último día del mes
    nextDate.setMonth(nextDate.getMonth() + 1, 0);

    return nextDate;
  }

  /**
   * Verifica si un vehículo necesita verificación pronto
   */
  static needsVerificationSoon(nextVerificationDate: Date, daysThreshold: number = 30): boolean {
    const today = new Date();
    const timeDiff = nextVerificationDate.getTime() - today.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

    return daysDiff <= daysThreshold && daysDiff >= 0;
  }

  /**
   * Verifica si un vehículo está vencido en verificación
   */
  static isVerificationOverdue(nextVerificationDate: Date): boolean {
    const today = new Date();
    return nextVerificationDate < today;
  }

  /**
   * Obtiene los meses de verificación en formato legible
   */
  static getVerificationMonthsText(months: number[]): string {
    const monthNames = [
      '',
      'Enero',
      'Febrero',
      'Marzo',
      'Abril',
      'Mayo',
      'Junio',
      'Julio',
      'Agosto',
      'Septiembre',
      'Octubre',
      'Noviembre',
      'Diciembre',
    ];

    const firstPeriod = months
      .slice(0, 2)
      .map((m) => monthNames[m])
      .join('-');
    const secondPeriod = months
      .slice(2, 4)
      .map((m) => monthNames[m])
      .join('-');

    return `${firstPeriod} / ${secondPeriod}`;
  }

  /**
   * Calcula cuándo enviar recordatorio a la flota (mes anterior)
   */
  static calculateFleetReminderDate(nextVerificationDate: Date): Date {
    const reminderDate = new Date(nextVerificationDate);
    reminderDate.setMonth(reminderDate.getMonth() - 1);
    reminderDate.setDate(1); // Primer día del mes anterior

    return reminderDate;
  }
}
