import { Request, Response, NextFunction } from 'express';
import { EmissionsVerification } from '../models/emissions-verification.model';
import { VerificationCenter } from '../models/verification-center.model';
import { VerificationLogicService } from '../services/verification-logic.service';
import '../types/request.types'; // Importar tipos globales

export class ValidationMiddleware {
  /**
   * Valida formato de placa mexicana
   */
  static validatePlateFormat(req: Request, res: Response, next: NextFunction) {
    const { vehiclePlate, plate } = req.body || req.params;
    const plateToValidate = vehiclePlate || plate;

    if (!plateToValidate) {
      return res.status(400).json({
        success: false,
        message: 'La placa es requerida',
      });
    }

    // Formato básico de placas mexicanas (letras y números)
    const plateRegex = /^[A-Z0-9]{6,8}$/;
    const cleanPlate = plateToValidate.replace(/[^A-Z0-9]/g, '').toUpperCase();

    if (!plateRegex.test(cleanPlate)) {
      return res.status(400).json({
        success: false,
        message: 'Formato de placa inválido',
      });
    }

    // Agregar placa limpia al request
    req.body.vehiclePlate = cleanPlate;
    return next();
  }

  /**
   * Valida que no exista verificación duplicada en el mismo período
   */
  static async validateDuplicateVerification(req: Request, res: Response, next: NextFunction) {
    try {
      const { vehiclePlate, verificationDate } = req.body;
      const cleanPlate = vehiclePlate.replace(/[^A-Z0-9]/g, '').toUpperCase();
      const verificationDateObj = new Date(verificationDate);

      // Buscar verificaciones en los últimos 30 días
      const thirtyDaysAgo = new Date(verificationDateObj.getTime() - 30 * 24 * 60 * 60 * 1000);
      const thirtyDaysLater = new Date(verificationDateObj.getTime() + 30 * 24 * 60 * 60 * 1000);

      const existingVerification = await EmissionsVerification.findOne({
        vehiclePlate: cleanPlate,
        verificationDate: {
          $gte: thirtyDaysAgo,
          $lte: thirtyDaysLater,
        },
        status: { $ne: 'cancelled' },
      });

      if (existingVerification) {
        return res.status(409).json({
          success: false,
          message: 'Ya existe una verificación registrada para este vehículo en este período',
          existingVerification: {
            id: existingVerification._id,
            date: existingVerification.verificationDate,
            status: existingVerification.status,
          },
        });
      }

      return next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Error al validar verificación duplicada',
      });
    }
  }

  /**
   * Valida que el centro de verificación sea válido y esté activo
   */
  static async validateVerificationCenter(req: Request, res: Response, next: NextFunction) {
    try {
      const { verificationCenterId } = req.body;
      console.log('🔍 validateVerificationCenter - verificationCenterId recibido:', verificationCenterId);

      if (!verificationCenterId) {
        console.log('❌ validateVerificationCenter - Centro de verificación no proporcionado');
        return res.status(400).json({
          success: false,
          message: 'El centro de verificación es requerido',
        });
      }

      console.log('🔍 validateVerificationCenter - Buscando centro en BD...');
      const center = await VerificationCenter.findById(verificationCenterId);
      console.log('🔍 validateVerificationCenter - Centro encontrado:', center);

      if (!center) {
        console.log('❌ validateVerificationCenter - Centro no encontrado en BD');
        return res.status(404).json({
          success: false,
          message: 'Centro de verificación no encontrado',
        });
      }

      console.log('🔍 validateVerificationCenter - Estado del centro (isActive):', center.isActive);
      if (!center.isActive) {
        console.log('❌ validateVerificationCenter - Centro no está activo');
        return res.status(400).json({
          success: false,
          message: 'El centro de verificación no está activo',
        });
      }

      console.log('✅ validateVerificationCenter - Centro válido, continuando...');
      return next();
    } catch (error) {
      console.log('❌ validateVerificationCenter - Error capturado:', error);
      return res.status(500).json({
        success: false,
        message: 'Error al validar centro de verificación',
      });
    }
  }

  /**
   * Valida que la fecha de verificación sea válida
   */
  static validateVerificationDate(req: Request, res: Response, next: NextFunction) {
    const { verificationDate } = req.body;

    if (!verificationDate) {
      return res.status(400).json({
        success: false,
        message: 'La fecha de verificación es requerida',
      });
    }

    const date = new Date(verificationDate);
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    if (isNaN(date.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Fecha de verificación inválida',
      });
    }

    if (date > today) {
      return res.status(400).json({
        success: false,
        message: 'La fecha de verificación no puede ser futura',
      });
    }

    if (date < thirtyDaysAgo) {
      return res.status(400).json({
        success: false,
        message: 'La fecha de verificación no puede ser anterior a 30 días',
      });
    }

    return next();
  }

  /**
   * Valida evidencias del vendor
   */
  static validateVendorEvidence(req: Request, res: Response, next: NextFunction) {
    const { vehiclePhoto, circulationCard } = req.body;

    if (!vehiclePhoto || !circulationCard) {
      return res.status(400).json({
        success: false,
        message: 'La foto del vehículo y la tarjeta de circulación son requeridas',
      });
    }

    // Validar que sean URLs válidas o paths de archivos
    const urlRegex = /^(https?:\/\/|\/)/;

    if (!urlRegex.test(vehiclePhoto)) {
      return res.status(400).json({
        success: false,
        message: 'URL de foto del vehículo inválida',
      });
    }

    if (!urlRegex.test(circulationCard)) {
      return res.status(400).json({
        success: false,
        message: 'URL de tarjeta de circulación inválida',
      });
    }

    return next();
  }

  /**
   * Valida evidencias del customer
   */
  static validateCustomerEvidence(req: Request, res: Response, next: NextFunction) {
    const { hologramType, verificationCertificate, hologramPhoto } = req.body;

    // Al menos uno de los campos debe estar presente
    if (!hologramType && !verificationCertificate && !hologramPhoto) {
      return res.status(400).json({
        success: false,
        message: 'Se requiere al menos una evidencia',
      });
    }

    // Validar tipo de holograma si se proporciona
    if (hologramType && !['00', '0', '1', '2'].includes(hologramType)) {
      return res.status(400).json({
        success: false,
        message: 'Tipo de holograma inválido',
      });
    }

    // Validar URLs si se proporcionan
    const urlRegex = /^(https?:\/\/|\/)/;

    if (verificationCertificate && !urlRegex.test(verificationCertificate)) {
      return res.status(400).json({
        success: false,
        message: 'URL de certificado de verificación inválida',
      });
    }

    if (hologramPhoto && !urlRegex.test(hologramPhoto)) {
      return res.status(400).json({
        success: false,
        message: 'URL de foto del holograma inválida',
      });
    }

    return next();
  }

  /**
   * Valida que el link único sea válido y la verificación exista
   */
  static async validateUniqueLink(req: Request, res: Response, next: NextFunction) {
    try {
      const { uniqueLink } = req.params;

      if (!uniqueLink) {
        return res.status(400).json({
          success: false,
          message: 'Link único requerido',
        });
      }

      const verification = await EmissionsVerification.findOne({ uniqueLink });

      if (!verification) {
        return res.status(404).json({
          success: false,
          message: 'Verificación no encontrada',
        });
      }

      // Agregar verificación al request para uso posterior
      req.verification = verification;
      return next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Error al validar link único',
      });
    }
  }

  /**
   * Valida que la verificación no haya expirado (para customer)
   */
  static validateVerificationNotExpired(req: Request, res: Response, next: NextFunction) {
    const verification = req.verification;

    if (!verification) {
      return res.status(400).json({
        success: false,
        message: 'Verificación no encontrada',
      });
    }

    // Verificación expira después de 30 días sin completar
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    if (verification.createdAt < thirtyDaysAgo && verification.status === 'pending_customer') {
      return res.status(410).json({
        success: false,
        message: 'La verificación ha expirado. Contacte al centro de verificación.',
      });
    }

    if (verification.status === 'expired') {
      return res.status(410).json({
        success: false,
        message: 'La verificación ha expirado.',
      });
    }

    return next();
  }

  /**
   * Valida período de verificación según último dígito de placa
   */
  static validateVerificationPeriod(req: Request, res: Response, next: NextFunction) {
    try {
      const { vehiclePlate, verificationDate } = req.body;
      const date = new Date(verificationDate);
      const month = date.getMonth() + 1; // JavaScript months are 0-indexed

      const rule = VerificationLogicService.getVerificationRule(vehiclePlate);

      // Validar que esté dentro del período permitido (con tolerancia de ±1 mes)
      const allowedMonths = [...rule.months];

      // Agregar meses adyacentes para tolerancia
      rule.months.forEach((m) => {
        if (m > 1) allowedMonths.push(m - 1);
        if (m < 12) allowedMonths.push(m + 1);
      });

      const uniqueAllowedMonths = [...new Set(allowedMonths)];

      if (!uniqueAllowedMonths.includes(month)) {
        const monthsText = VerificationLogicService.getVerificationMonthsText(rule.months);
        return res.status(400).json({
          success: false,
          message: `Esta placa debe verificar en: ${monthsText}`,
          allowedMonths: rule.months,
          hologramColor: rule.color,
        });
      }

      return next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Error al validar período de verificación',
      });
    }
  }
}
