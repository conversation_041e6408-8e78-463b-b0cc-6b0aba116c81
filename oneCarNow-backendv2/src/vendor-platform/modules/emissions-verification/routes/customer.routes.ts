import { Router } from 'express';
import { CustomerVerificationController } from '../controllers/customer.controller';
import { ValidationMiddleware } from '../middlewares/validation.middleware';

const router = Router();

// Obtener opciones de hologramas disponibles (público)
router.get('/hologram-options/list', CustomerVerificationController.getHologramOptions);

// Obtener información de verificación por link único (público)
router.get(
  '/:uniqueLink',
  ValidationMiddleware.validateUniqueLink,
  ValidationMiddleware.validateVerificationNotExpired,
  CustomerVerificationController.getVerificationInfo
);

// Subir evidencias del customer (público)
router.put(
  '/:uniqueLink/evidence',
  ValidationMiddleware.validateUniqueLink,
  ValidationMiddleware.validateVerificationNotExpired,
  ValidationMiddleware.validateCustomerEvidence,
  CustomerVerificationController.uploadEvidence
);

// Validar si el proceso está completo (público)
router.get(
  '/:uniqueLink/completion-status',
  ValidationMiddleware.validateUniqueLink,
  CustomerVerificationController.validateCompletion
);

// Obtener información sobre la próxima verificación (público)
router.get(
  '/:uniqueLink/next-verification',
  ValidationMiddleware.validateUniqueLink,
  CustomerVerificationController.getNextVerificationInfo
);

export default router;
