import type { AsyncController } from '../types&interfaces/types';

import StockVehicle from '../models/StockVehicleSchema';
import { uploadFile } from '../aws/s3';
import fs from 'fs';
import { logger } from '../clean/lib/logger';

export const isElectric: AsyncController = async (req, res) => {
  try {
    const { plates } = req.params;
    const vehicle = await StockVehicle.findOne({ 'carPlates.plates': plates.trim().toUpperCase() });
    if (!vehicle) return res.status(404).send({ message: 'Vehículo no encontrado' });

    return res.status(200).send({ isElectric: vehicle.isElectric });
  } catch (error) {
    console.error('Error in isElectric:', error);
    return res.status(500).json({ error: 'Vehiculo no encontrado' });
  }
};

export const uploadVerificationData: AsyncController = async (req, res) => {
  try {
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    const { plates, userId } = req.body;

    if (!plates || !userId) {
      return res.status(400).json({
        error: 'Placas y ID de usuario son requeridos',
      });
    }

    const vehicle = await StockVehicle.findOne({
      'carPlates.plates': plates.trim().toUpperCase(),
    });

    if (!vehicle) {
      return res.status(404).json({ message: 'Vehículo no encontrado' });
    }

    if (!files?.holograma || !files?.certificado) {
      return res.status(400).json({
        error: 'Se requieren las imágenes del holograma y certificado',
      });
    }

    const hologramaFile = files.holograma[0];
    const certificadoFile = files.certificado[0];
    const uploadedFiles: Express.Multer.File[] = [];

    const cleanupFiles = async (filesToClean: Express.Multer.File[]) => {
      for (const file of filesToClean) {
        if (file.path) {
          try {
            await fs.promises.unlink(file.path);
          } catch (error) {
            if (error instanceof Error && !error.message?.includes('ENOENT')) {
              logger.error(`Error deleting temporary file ${file.path}:`, error);
            }
          }
        }
      }
    };

    try {
      const hologramaUrl = await uploadFile(
        hologramaFile,
        `${Date.now()}-holograma-${hologramaFile.originalname}`,
        `verificentros/${plates}/${userId}/`
      );
      uploadedFiles.push(hologramaFile);

      const certificadoUrl = await uploadFile(
        certificadoFile,
        `${Date.now()}-certificado-${certificadoFile.originalname}`,
        `verificentros/${plates}/${userId}/`
      );
      uploadedFiles.push(certificadoFile);

      const verificationData = {
        holograma: {
          url: hologramaUrl,
          uploadedAt: new Date(),
          fileName: hologramaFile.originalname,
        },
        certificado: {
          url: certificadoUrl,
          uploadedAt: new Date(),
          fileName: certificadoFile.originalname,
        },
        userId,
        completedAt: new Date(),
        status: 'completed',
      };

      await StockVehicle.findByIdAndUpdate(
        vehicle._id,
        { $set: { 'verificentros.verificationProcess': verificationData } },
        { new: true }
      );

      await cleanupFiles(uploadedFiles);

      return res.status(200).json({
        message: 'Proceso de verificación completado exitosamente',
        data: verificationData,
      });
    } catch (uploadError) {
      await cleanupFiles(uploadedFiles);
      console.error('Error uploading files:', uploadError);
      return res.status(500).json({
        error: 'Error al subir los archivos',
      });
    }
  } catch (error) {
    console.error('Error in uploadVerificationData:', error);
    return res.status(500).json({
      error: 'Error interno del servidor',
    });
  }
};
