import eslintPlugin from "@nabla/vite-plugin-eslint";
import react from "@vitejs/plugin-react-swc";
import dotenv from "dotenv";
import path from "path";
import { defineConfig } from "vite";
import viteSvgr from "vite-plugin-svgr";
import tsconfigPaths from "vite-tsconfig-paths";

dotenv.config();

export default defineConfig({
  base: "/",
  optimizeDeps: {
    esbuildOptions: {
      tsconfig: "tsconfig.json",
    },
  },
  plugins: [react(), eslintPlugin(), viteSvgr(), tsconfigPaths()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@components": path.resolve(__dirname, "./src/components"),
      "@views": path.resolve(__dirname, "./src/views"),
      "@misc": path.resolve(__dirname, "./src/misc"),
    },
  },
  server: {
    port: 3001,
  },
});
