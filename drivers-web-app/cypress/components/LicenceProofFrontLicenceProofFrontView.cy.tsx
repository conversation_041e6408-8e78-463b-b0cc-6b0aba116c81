import "cypress-file-upload";

import { ChakraProvider, extendTheme } from "@chakra-ui/react";
import { SetupProvider } from "context/SetupContext";
import i18n from "i18n/mocki18n";
import { MemoryRouter } from "react-router-dom";

import { LicenceProofFrontView } from "../../src/views/LicenceProofFront";

// Mock the theme
const mockTheme = extendTheme({
  gradients: {
    bgGradient: "linear(to-r, #6210FF, #A74DF9, #6210FF)",
  },
});

describe("<LicenseProofFrontView />", () => {
  it("Initial state", () => {
    cy.mount(
      <MemoryRouter>
        <ChakraProvider theme={mockTheme}>
          <SetupProvider>
            <LicenceProofFrontView />
          </SetupProvider>
        </ChakraProvider>
      </MemoryRouter>,
    );

    cy.get("[id=handleSubmitButton]")
      .should("be.disabled")
      .and("contain", i18n.t("Continue"));
    cy.get("[id=handleSubmitLaterButton]")
      .should("be.enabled")
      .and("contain", i18n.t("SubmitLater"));
  });

  it("File selected state", () => {
    // Mocking /media/upload/ API
    cy.intercept("POST", "/media/upload/", {
      fixture: "mediaUpload.json",
    }).as("mediaUpload");

    cy.mount(
      <MemoryRouter>
        <ChakraProvider theme={mockTheme}>
          <SetupProvider>
            <LicenceProofFrontView />
          </SetupProvider>
        </ChakraProvider>
      </MemoryRouter>,
    );

    cy.get("[id=fileUploadPrimaryButton]")
      .should("be.visible")
      .and("contain", i18n.t("UploadFile"))
      .click();

    cy.get('input[type="file"]').attachFile("../files/api-req.png").wait(1000);

    cy.wait("@mediaUpload");

    cy.get("[id=fileUploadPrimaryButton]").should("not.exist");
    cy.get("[id=handleSubmitButton]")
      .should("be.enabled")
      .and("contain", i18n.t("Continue"));
    cy.get("[id=handleSubmitLaterButton]")
      .should("be.disabled")
      .and("contain", i18n.t("SubmitLater"));
  });
});
