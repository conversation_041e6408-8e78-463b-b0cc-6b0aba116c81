import { ChakraProvider, extendTheme } from "@chakra-ui/react";
import i18n from "i18n/mocki18n";
import { MemoryRouter } from "react-router-dom";

import { WelcomeView } from "../../src/views/Welcome";

// Mock the theme
const mockTheme = extendTheme({
  gradients: {
    bgGradient: "linear(to-r, #6210FF, #A74DF9, #6210FF)",
  },
});

describe("<WelcomeView />", () => {
  it("renders", () => {
    cy.intercept("GET", "/assets/logo_ocn.svg", {
      fixture: "../../public/assets/logo_ocn.svg",
    });

    cy.mount(
      <MemoryRouter>
        <ChakraProvider theme={mockTheme}>
          <WelcomeView />
        </ChakraProvider>
      </MemoryRouter>,
    );

    cy.get("[id=welcomeText]").should("have.text", i18n.t("Welcome"));
    cy.get("[id=welcomeText1]").should("have.text", i18n.t("WelcomeText1"));
    cy.get("[id=welcomeText2]").should("have.text", i18n.t("WelcomeText2"));
    cy.get("[id=thankYouText]").should("have.text", i18n.t("ThankYou"));

    cy.get("[id=getStartedButton]").should("be.enabled");
  });
});
