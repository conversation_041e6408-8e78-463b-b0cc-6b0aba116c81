// ***********************************************************
// This example support/component.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import "./commands";

import { MountOptions, MountReturn } from "cypress/react";
import { mount } from "cypress/react18";
import { mount as vueMount } from "cypress/vue";
import i18next from "i18next";
import { MemoryRouterProps } from "react-router-dom";

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Cypress {
    interface Chainable {
      mount(
        component: React.ReactNode,
        options?: MountOptions & {
          routerProps?: MemoryRouterProps;
        } & typeof vueMount,
      ): Cypress.Chainable<MountReturn>;
    }
    interface Window {
      i18n: typeof i18next;
    }
  }
}

Cypress.Commands.add("mount", mount);

// Example use:
// cy.mount(<MyComponent />)
