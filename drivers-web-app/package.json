{"name": "drivers-web-app", "private": true, "version": "0.1.0", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --max-warnings=0", "lint:fix": "eslint src --fix", "types": "tsc --project tsconfig.json --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,md}\"", "prepare": "husky install"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/plus-jakarta-sans": "^5.0.18", "@heroicons/react": "^2.0.18", "@palenca/palenca-link": "^2.1.12", "@types/react-datepicker": "^4.19.4", "axios": "^1.6.2", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "formik": "^2.4.5", "framer-motion": "^10.16.16", "i18next": "^23.15.1", "luxon": "^3.5.0", "query-string": "^8.1.0", "react": "^18.2.0", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-i18next": "^15.0.2", "react-icons": "^5.4.0", "react-photo-view": "^1.2.7", "react-router-dom": "^6.21.0", "yup": "^1.3.2"}, "devDependencies": {"@nabla/vite-plugin-eslint": "^2.0.2", "@types/i18next": "^13.0.0", "@types/luxon": "^3.4.2", "@types/mocha": "^10.0.10", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.17", "@types/react-i18next": "^8.1.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "cypress": "^13.17.0", "cypress-file-upload": "^5.0.8", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unused-imports": "^3.0.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "vite": "^5.0.8", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.2.2"}, "lint-staged": {"*.js": "eslint --cache --fix"}}