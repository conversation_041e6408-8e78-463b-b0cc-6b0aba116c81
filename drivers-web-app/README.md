
# Driver Onboarding Web App

A React-based web application that handles the driver onboarding process after initial registration on the OCN website. This application manages document uploads and pre-approval workflow for new drivers.

## Overview

The Driver Onboarding Web App is designed to streamline the driver registration process. After a driver fills out the initial form on the OCN website, they receive a unique link to this web application. Through this platform, drivers can:

- Upload required documentation
- Complete the pre-approval process

## Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (version 18.x or higher)
- npm (version 8.x or higher) or yarn (version 1.22.x or higher)
- Git

## Installation

1. Clone the repository:
```bash
git clone https://github.com/your-org/driver-onboarding-webapp.git
cd driver-onboarding-webapp
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Set up environment variables:
```bash
# For Unix-based systems
cp .env.example .env

# For Windows
copy .env.example .env
```

4. Update the `.env` file with your configuration:
```env
VITE_API_URL=your_api_url
VITE_STORAGE_BUCKET=your_storage_bucket
VITE_AUTH_DOMAIN=your_auth_domain
```

## Development

To start the development server:
```bash
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:5173`

## Building for Production

To create a production build:
```bash
npm run build
# or
yarn build
```

The built files will be available in the `dist` directory.

## Testing

Run tests with:
```bash
npm run test
# or
yarn test
```

## Key Features

- Easy and Secure document upload system
- Responsive design for mobile and desktop
- Integration with OCN's main platform


## Contributing

1. Clone the repository
2. Create your feature branch from `main` (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Merge and test the branch with `develop`
6. Open a Pull Request
