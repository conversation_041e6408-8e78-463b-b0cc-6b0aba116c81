export enum Countries {
  "United States" = "United States",
  Mexico = "Mexico",
}

export const CountriesOptions = {
  [Countries["United States"]]: {
    value: Countries["United States"],
    label: Countries["United States"],
  },
  [Countries.Mexico]: {
    value: Countries.Mexico,
    label: Countries.Mexico,
  },
};

export const CountriesShortNames = Object.freeze({
  [Countries["United States"]]: "us",
  [Countries.Mexico]: "mx",
});

export const US_COUNTRY_CODE = "+1";

export enum USSTATES {
  Florida = "Florida",
  Texas = "Texas",
}

export const US_STATES_OBJ = {
  [USSTATES.Florida]: {
    value: USSTATES.Florida,
    label: USSTATES.Florida,
  },
  [USSTATES.Texas]: {
    value: USSTATES.Texas,
    label: USSTATES.Texas,
  },
};

export const US_STATES_OPTIONS = [US_STATES_OBJ[USSTATES.Texas]];

export enum USCITIESNAMES {
  Miami = "Miami",
  Dallas = "Dallas",
}

export const US_CITIES_NAMES_SHORT_CODES = {
  [USCITIESNAMES.Miami]: "MIA",
};

export const US_CITIES_OPTIONS = {
  [USCITIESNAMES.Miami]: {
    value: USCITIESNAMES.Miami,
    label: USCITIESNAMES.Miami,
  },
  [USCITIESNAMES.Dallas]: {
    value: USCITIESNAMES.Dallas,
    label: USCITIESNAMES.Dallas,
  },
};

export const US_STATES_CITIES = {
  [USSTATES.Florida]: [US_CITIES_OPTIONS[USCITIESNAMES.Miami]],
  [USSTATES.Texas]: [US_CITIES_OPTIONS[USCITIESNAMES.Dallas]],
};

export const REQUIRED_BANK_STATEMENTS_US = 6;
export const REQUIRED_BANK_STATEMENTS_MX = 3;

export const DAYS = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"];
export const MONTHS = [
  "Enero",
  "Febrero",
  "Marzo",
  "Abril",
  "Mayo",
  "Junio",
  "Julio",
  "Agosto",
  "Septiembre",
  "Octubre",
  "Noviembre",
  "Diciembre",
];
export interface DayInfo {
  date: Date | null;
  disabled: boolean;
  isToday?: boolean;
}

export const getDaysInMonth = (date: Date): DayInfo[] => {
  const year = date.getFullYear();
  const month = date.getMonth();
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  const days: DayInfo[] = [];
  const firstDayIndex = firstDay.getDay();

  for (let i = 0; i < firstDayIndex; i++) {
    days.push({ date: null, disabled: true });
  }

  for (let i = 1; i <= lastDay.getDate(); i++) {
    const currentDate = new Date(year, month, i);
    const isToday = new Date().toDateString() === currentDate.toDateString();
    const isPast = currentDate < new Date(new Date().setHours(0, 0, 0, 0));

    days.push({
      date: currentDate,
      disabled: isPast,
      isToday,
    });
  }

  return days;
};
