import {
  HttpClient,
  IHttpClientRequestParameters,
} from "core/misc/http-client";
import { buildSuccessResponse, parseErrors } from "core/misc/response";
import { IEnvelope } from "core/types";

import { BaseController } from "./types";

export class IpifyController implements BaseController {
  httpClient: HttpClient;

  constructor() {
    this.httpClient = new HttpClient();
  }

  public async getIP() {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: "https://api.ipify.org/",
      };
      const response = await this.httpClient.get<IEnvelope<string>>(params);
      const ip = response.data;
      return buildSuccessResponse<string>(ip, response);
    });
    return wrappedFunction();
  }
}
