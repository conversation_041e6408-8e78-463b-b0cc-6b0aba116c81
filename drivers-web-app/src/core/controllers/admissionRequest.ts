import { admissionRequestAdapter } from "core/domain/adapters";
import {
  AdmissionRequest,
  EarningsAnalysis,
  HomeVisit,
  PalencaAccount,
  PalencaAccountRetrieval,
  PalencaAccountSuccessLogin,
  RequestAvalData,
  RequestDocument,
  RequestDocumentsAnalysis,
  RequestHomeVisitData,
  RequestLatLongData,
  RequestPalenca,
  RequestPersonalData,
} from "core/domain/entities";
import {
  HttpClient,
  IHttpClientRequestParameters,
} from "core/misc/http-client";
import { buildSuccessResponse, parseErrors } from "core/misc/response";
import { IEnvelope } from "core/types";
import { API_URL } from "misc/settings";
import { Countries, CountriesShortNames } from "utils";

import { BaseController } from "./types";

export type IAdmissionRequest = AdmissionRequest;
export type IRequestDocument = RequestDocument;
export type IRequestDocumentsAnalysis = RequestDocumentsAnalysis;
export type IRequestPalenca = RequestPalenca;
export type IPalencaAccount = PalencaAccount;
export type IPalencaAccountRetrieval = PalencaAccountRetrieval;
export type IEarningsAnalysis = EarningsAnalysis;
type OmitBirthdate = Omit<RequestPersonalData, "birthdate">;
export type IRequestPersonalData = OmitBirthdate & {
  birthdate: string;
};
export type IRequestAvalData = RequestAvalData;
export type IRequestLocationData = RequestLatLongData;
export type IRequestHomeVisit = RequestHomeVisitData;
export type IHomeVisit = HomeVisit;
export interface IHomeImages extends Pick<HomeVisit, "images" | "homeImages"> {}

export interface ISlot {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  isAvailable: true;
  timezone: string;
}

export interface IAppointment {
  id: string;
  user: string;
  startTime: string;
  endTime: string;
  status: string;
  description: string;
  admissionRequestId: string;
  title: string;
  date: string;
  slot: string;
}

export class AdmissionRequestController implements BaseController {
  httpClient: HttpClient;

  constructor() {
    this.httpClient = new HttpClient();
  }

  // @ParseErrors()
  public async getAdmissionRequest(requestId: string) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}`,
      };

      const response =
        await this.httpClient.get<IEnvelope<IAdmissionRequest>>(params);

      const data = response.data;
      const admissionRequest = admissionRequestAdapter(data);
      const { id } = admissionRequest;
      if (id) {
        try {
          fetch(`${API_URL}/hubspot/link-opened/${id}`)
            .then((res) => res.json())
            .then((data) => console.log("hubspot", data));
        } catch (error) {
          console.error("Error obteniendo el token:", error);
          throw error; // Re-lanza el error para que pueda ser manejado por la función que llama a getToken()
        }
      }

      return buildSuccessResponse<IAdmissionRequest>(
        admissionRequest,
        response,
      );
    });

    return wrappedFunction();
  }

  public async addPalencaAccount(
    palencaAccountSuccessLogin: PalencaAccountSuccessLogin,
  ) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${palencaAccountSuccessLogin.requestId}/palenca-accounts`,
        payload: palencaAccountSuccessLogin,
      };

      const response =
        await this.httpClient.post<IEnvelope<IAdmissionRequest>>(params);

      const data = response.data;
      const admissionRequest = data;

      return buildSuccessResponse<IAdmissionRequest>(
        admissionRequest,
        response,
      );
    });

    return wrappedFunction();
  }

  // @ParseErrors()
  public async updatePersonalData(
    requestId: string,
    personalData: Partial<IRequestPersonalData>,
  ) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/personal-data`,
        payload: personalData,
      };

      const response =
        await this.httpClient.patch<IEnvelope<IRequestPersonalData>>(params);

      const data = response.data;
      const requestPersonalData = data;

      return buildSuccessResponse<IRequestPersonalData>(
        requestPersonalData,
        response,
      );
    });

    return wrappedFunction();
  }

  // @ParseErrors()
  public async updateDocuments(
    requestId: string,
    documents: RequestDocument[],
    country?: string,
  ) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/documents`,
        payload: {
          updates: documents,
        },
        params: {
          country: country || CountriesShortNames[Countries.Mexico],
        },
      };

      const response =
        await this.httpClient.patch<IEnvelope<IAdmissionRequest>>(params);

      const data = response.data;
      const requestPersonalData = data;

      return buildSuccessResponse<IAdmissionRequest>(
        requestPersonalData,
        response,
      );
    });

    return wrappedFunction();
  }

  public async startEarningsAnalysis(requestId: string) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/start-earnings-analysis`,
      };

      const response =
        await this.httpClient.post<IEnvelope<IAdmissionRequest>>(params);

      const data = response.data;
      const requestPersonalData = data;

      return buildSuccessResponse<IAdmissionRequest>(
        requestPersonalData,
        response,
      );
    });

    return wrappedFunction();
  }

  public async checkEarningsAnalysis(requestId: string) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/check-earnings-analysis`,
      };

      const response =
        await this.httpClient.get<IEnvelope<IEarningsAnalysis>>(params);

      const data = response.data;
      const earningsAnalysis = data;
      return buildSuccessResponse<IEarningsAnalysis>(
        earningsAnalysis,
        response,
      );
    });

    return wrappedFunction();
  }

  public async getCalendarAvailableSlots(requestId: string, date: string) {
    const wrappedFunction = parseErrors(async () => {
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/${date}/calendar/slots/get-available-slots`,
        params: {
          timezone: timeZone,
        },
      };
      const response =
        await this.httpClient.get<IEnvelope<Array<ISlot>>>(params);
      const data = response.data;
      const availableSlots = data;

      return buildSuccessResponse<Array<ISlot>>(availableSlots, response);
    });
    return wrappedFunction();
  }

  public async createHomeVisitAppointment(
    requestId: string,
    payload: Record<string, unknown>,
  ) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/calendar/appointment/create`,
        payload: payload,
      };
      const response =
        await this.httpClient.post<IEnvelope<IAppointment>>(params);
      const appointment = response.data;

      return buildSuccessResponse<IAppointment>(appointment, response);
    });

    return wrappedFunction();
  }

  public async getHomeVisitAppointment(requestId: string) {
    const wrappedFunction = parseErrors(async () => {
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/calendar/appointment`,
        params: {
          timezone: timeZone,
        },
      };
      const response =
        await this.httpClient.get<IEnvelope<IAppointment>>(params);
      const data = response.data;
      const appointment = data;

      return buildSuccessResponse<IAppointment>(appointment, response);
    });
    return wrappedFunction();
  }

  public async cancelHomeVisitAppointment(
    requestId: string,
    payload: Record<string, unknown>,
  ) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/calendar/appointment/cancel`,
        payload: payload,
      };

      const response =
        await this.httpClient.post<IEnvelope<IAppointment>>(params);
      const appointment = response.data;

      return buildSuccessResponse<IAppointment>(appointment, response);
    });

    return wrappedFunction();
  }

  public async addAvalData(
    requestId: string,
    avalData: Partial<IRequestAvalData>,
  ) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/aval-data`,
        payload: avalData,
      };
      const response =
        await this.httpClient.post<IEnvelope<IRequestAvalData>>(params);
      return buildSuccessResponse<IRequestAvalData>(response.data, response);
    });
    return wrappedFunction();
  }

  public async latLongLookup(
    requestId: string,
    data: Partial<IRequestLocationData>,
  ) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/lat-long-lookup`,
        payload: data,
      };
      const response =
        await this.httpClient.post<IEnvelope<IRequestLocationData>>(params);
      return buildSuccessResponse<IRequestLocationData>(
        response.data,
        response,
      );
    });
    return wrappedFunction();
  }

  public async ipAddressLookup(
    requestId: string,
    data: Partial<IRequestLocationData>,
  ) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/ip-lookup`,
        payload: data,
      };
      const response =
        await this.httpClient.post<IEnvelope<IRequestLocationData>>(params);
      return buildSuccessResponse<IRequestLocationData>(
        response.data,
        response,
      );
    });
    return wrappedFunction();
  }

  public async getHomeVisit(requestId: string) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/home-visit`,
      };
      const response = await this.httpClient.get<IEnvelope<HomeVisit>>(params);
      return buildSuccessResponse<HomeVisit>(response.data, response);
    });
    return wrappedFunction();
  }

  public async updateHomeVisit(
    requestId: string,
    homeVisit: {
      homeVisitData: Partial<IHomeVisit>;
      isHomeVisitData: boolean;
    },
  ) {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/public/admission/requests/${requestId}/home-visit`,
        payload: homeVisit,
      };
      const response =
        await this.httpClient.patch<IEnvelope<IRequestPersonalData>>(params);

      const data = response.data;
      const requestPersonalData = data;
      return buildSuccessResponse<IRequestPersonalData>(
        requestPersonalData,
        response,
      );
    });

    return wrappedFunction();
  }
}
