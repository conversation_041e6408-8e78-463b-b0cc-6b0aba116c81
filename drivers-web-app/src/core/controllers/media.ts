import { mediaAdapter } from "core/domain/adapters";
import { Media } from "core/domain/entities";
import { MediaType } from "core/domain/enums";
import {
  HttpClient,
  IHttpClientRequestParameters,
} from "core/misc/http-client";
import { buildSuccessResponse, parseErrors } from "core/misc/response";
import { BaseResponse, IEnvelope } from "core/types";

import { BaseController } from "./types";

export type IMedia = Media;

export class MediaController implements BaseController {
  httpClient: HttpClient;

  constructor() {
    this.httpClient = new HttpClient();
  }

  // @ParseErrors()
  public async uploadMedia(
    file: File | File[],
    mediaType: MediaType,
  ): Promise<BaseResponse<Media>> {
    const wrappedFunction = parseErrors(async () => {
      const params: IHttpClientRequestParameters = {
        url: `/media/upload/`,
        payload: {
          mediaType,
        },
        file,
      };

      const response =
        await this.httpClient.postMultiPart<IEnvelope<IMedia>>(params);

      const data = response.data;
      const isFileArray = Array.isArray(file);

      const media = mediaAdapter(
        Array.isArray(data) ? (isFileArray ? data : data[0]) : {},
      );
      return buildSuccessResponse<Media | Array<Media>>(media, response);
    });

    return wrappedFunction();
  }
}
