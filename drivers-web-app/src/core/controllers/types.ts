import { GigPlatform } from "core/domain/enums";
import { HttpClient } from "core/misc/http-client";

export interface ISearchParams {
  id?: string | null;
}

export interface BaseController {
  httpClient: HttpClient;
}

// Palenca Widget events

export interface IPalencaWidgetEventData {
  account_id: string;
  platform: GigPlatform;
  user_id: string;
  country: string;
}

export interface IPalencaWidgetEventError {
  code: string;
  message: string;
  errors: any;
}

export interface IPalencaWidgetEvent {
  success: boolean;
  data: IPalencaWidgetEventData;
  error: IPalencaWidgetEventError;
}
