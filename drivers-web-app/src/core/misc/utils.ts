export const dateToString = (date: Date | undefined) => {
  if (!date) return "";
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

export const truncateFileName = (fileName: string) => {
  if (fileName.length <= 30) return fileName;
  const fileNameArray = fileName.split(".");
  const extension = fileNameArray.pop();
  const name = fileNameArray.join(".");
  const truncatedName = name.slice(0, 27);
  return `${truncatedName}...${extension}`;
};
