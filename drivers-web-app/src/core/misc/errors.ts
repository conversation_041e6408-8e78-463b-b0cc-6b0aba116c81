import { AxiosError } from "axios";
import { HTTPErrorCode } from "core/domain/enums";
import { IEnvelope, IError } from "core/types";

export interface AppError {
  httpCode: HTTPErrorCode;
  details: IError | undefined;
  response?: IEnvelope<any>;
}

const extractEnvelope = (
  axiosError?: AxiosError
): IEnvelope<null> | undefined => {
  if (!axiosError) return undefined;
  const { response } = axiosError;
  if (!response) {
    return undefined;
  }
  const envelope = response.data as IEnvelope<null>;
  return envelope;
};

const extractError = (axiosError?: AxiosError): IError | undefined => {
  const envelope = extractEnvelope(axiosError);
  if (!envelope)
    return {
      code: "unknown_error"
    };
  return {
    code: envelope.error.code,
    message: envelope.error.message,
    errors: envelope.error.errors
  };
};

const extractRawData = (
  axiosError?: AxiosError
): IEnvelope<any> | undefined => {
  const envelope = extractEnvelope(axiosError);
  if (!envelope) return undefined;
  return envelope;
};

export class AppError extends Error implements AppError {
  httpCode = HTTPErrorCode.unknown_error;

  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super();
    this.httpCode = httpCode || HTTPErrorCode.unknown_error;
    this.details = extractError(axiosError);
    this.response = extractRawData(axiosError);
  }
}
export class ServerError extends AppError {
  httpCode = HTTPErrorCode.server_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}

export class ConflictError extends AppError {
  httpCode = HTTPErrorCode.conflict_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}

export class NotFoundError extends AppError {
  httpCode = HTTPErrorCode.not_found_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}

export class AuthenticationError extends AppError {
  constructor(axiosError?: AxiosError) {
    super(axiosError, HTTPErrorCode.server_error);
  }
}

export class UnprocessableError extends AppError {
  httpCode = HTTPErrorCode.unprocessable_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}

export class TooManyRequestsError extends AppError {
  httpCode = HTTPErrorCode.too_many_requests_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}

export class BadRequestError extends AppError {
  httpCode = HTTPErrorCode.bad_request_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}

export class TimeoutError extends AppError {
  httpCode = HTTPErrorCode.timeout_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}

export class NetworkError extends AppError {
  httpCode = HTTPErrorCode.network_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}

export class ForbiddenError extends AppError {
  httpCode = HTTPErrorCode.forbidden_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}

export class ServerUnavailableError extends AppError {
  httpCode = HTTPErrorCode.server_unavailable_error;
  constructor(axiosError?: AxiosError, httpCode?: HTTPErrorCode) {
    super(axiosError, httpCode);
  }
}
