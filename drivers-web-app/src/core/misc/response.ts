import { BaseResponse, IEnvelope } from "../types";
import { AppError } from "./errors";

export const buildErrorResponse = (error: AppError): BaseResponse<any> => {
  const result: BaseResponse<any> = [error, undefined, error.response];
  return result;
};

export const buildSuccessResponse = <T = undefined, U = any>(
  data: T,
  response: IEnvelope<U>,
): BaseResponse<T> => {
  return [undefined, data, response];
};

// Had difficulty getting this to work as a decorator in Vite
// export const ParseErrors = (): any => {
//   return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
//     const method = descriptor.value
//     descriptor.value = function (...args: any[]) {
//       try {
//         const result = method.apply(this, args)
//         if (result && result instanceof Promise) {
//           return result.catch((error: AppError) => {
//             return buildErrorResponse(error)
//           })
//         }
//         return result
//       } catch (error: any) {
//         return buildErrorResponse(error)
//       }
//     }

//     return descriptor
//   }
// }

export function parseErrors(fn: (...args: any[]) => any) {
  return function (this: any, ...args: any[]) {
    try {
      const result = fn.apply(this, args);
      if (result && result instanceof Promise) {
        return result.catch((error: AppError) => {
          // Assuming buildErrorResponse is a function that handles the error
          return buildErrorResponse(error);
        });
      }
      return result;
    } catch (error: any) {
      // Assuming buildErrorResponse is a function that handles the error
      return buildErrorResponse(error);
    }
  };
}
