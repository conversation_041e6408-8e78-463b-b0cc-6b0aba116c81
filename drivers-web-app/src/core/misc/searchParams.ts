import { searchParamsAdapter } from "core/domain/adapters";
import { SearchParams } from "core/domain/entities";
import queryString from "query-string";

export const getSearchParams = (): SearchParams => {
  const parsed = queryString.parse(window.location.search, {
    arrayFormat: "comma",
  });
  const searchParams = searchParamsAdapter(parsed);
  return searchParams;
};

export const getIdFromPath = (url: string) => {
  const pathSegments = new URL(url).pathname.split("/");
  const id = pathSegments[1]; // Extract the second segment (index 1)
  return id;
};
