import axios, {
  AxiosError,
  Axi<PERSON>Instance,
  AxiosRequestConfig,
  AxiosResponse,
} from "axios";
import {
  AppError,
  AuthenticationError,
  BadRequestError,
  ConflictError,
  ForbiddenError,
  NotFoundError,
  ServerError,
  ServerUnavailableError,
  TimeoutError,
  TooManyRequestsError,
  UnprocessableError,
} from "core/misc/errors";
import { API_URL, NETWORK_TIMEOUT } from "misc/settings";

export interface IHttpClientRequestParameters {
  url: string;
  payload?: object;
  params?: object;
  file?: File | File[];
}

export interface IHttpClient {
  http: AxiosInstance;
  config: AxiosRequestConfig;
  handleSuccessResponse<T>(response: AxiosResponse<T>): AxiosResponse<T>;
  handleErrorResponse: (error: AxiosError) => void;
  get<T>(parameters: IHttpClientRequestParameters): Promise<T>;
  post<T>(parameters: IHttpClientRequestParameters): Promise<T>;
  delete<T>(parameters: IHttpClientRequestParameters): Promise<T>;
  patch<T>(parameters: IHttpClientRequestParameters): Promise<T>;
}

const ErrorStatusMapping = {
  400: BadRequestError,
  401: AuthenticationError,
  403: ForbiddenError,
  404: NotFoundError,
  408: TimeoutError,
  409: ConflictError,
  422: UnprocessableError,
  429: TooManyRequestsError,
  500: ServerError,
  503: ServerUnavailableError,
  504: TimeoutError,
};

export class HttpClient implements IHttpClient {
  http: AxiosInstance;

  config: AxiosRequestConfig;

  handleSuccessResponse(response: AxiosResponse<any>) {
    return response;
  }

  handleErrorResponse(error: AxiosError) {
    // Catch servertimeout error and throw a timeout error
    if (error.isAxiosError && error.code === "ECONNABORTED") {
      throw new TimeoutError();
    }

    // Catch CORS error when server return 5XX and no response body
    if (error.isAxiosError && error.response === undefined) {
      throw new ServerError();
    }

    // if (_get(error, 'response.headers.content-type') !== 'application/json') {
    //   throw new ServerError()
    // }

    if (error.response && error.response.status) {
      const status = error.response.status;
      if (Object.hasOwn(ErrorStatusMapping, status)) {
        throw new ErrorStatusMapping[status](error);
      }
    } else {
      // Uknown error
      throw new AppError();
    }
  }

  private setupConfig(): AxiosRequestConfig {
    const requestConfig: AxiosRequestConfig = {
      timeout: NETWORK_TIMEOUT,
      baseURL: API_URL,
    };

    return requestConfig;
  }

  constructor() {
    this.http = axios.create();
    this.config = this.setupConfig();
    this.http.interceptors.response.use(
      (success) => this.handleSuccessResponse(success),
      (error) => this.handleErrorResponse(error),
    );
  }

  public async get<T>(parameters: IHttpClientRequestParameters): Promise<T> {
    const { url, params } = parameters;

    const response: AxiosResponse = await this.http.get(url, {
      ...this.config,
      params,
    });
    return response.data;
  }

  public async post<T>(parameters: IHttpClientRequestParameters): Promise<T> {
    const { url, payload } = parameters;

    const response: AxiosResponse = await this.http.post(
      url,
      payload,
      this.config,
    );
    return response.data;
  }

  public async delete<T>(parameters: IHttpClientRequestParameters): Promise<T> {
    const { url } = parameters;

    const response: AxiosResponse = await this.http.delete(url, this.config);
    return response.data;
  }

  public async patch<T>(parameters: IHttpClientRequestParameters): Promise<T> {
    const { url, payload, params } = parameters;

    this.config.params = params;
    const response: AxiosResponse = await this.http.patch(
      url,
      payload,
      this.config,
    );
    return response.data;
  }

  public async postMultiPart<T>(parameters: IHttpClientRequestParameters) {
    const { url, payload, file } = parameters;
    const formData = new FormData();

    // Add payload to formData
    for (const key in payload) {
      if (Object.prototype.hasOwnProperty.call(payload, key)) {
        formData.append(key, payload[key]);
      }
    }

    // Add file to formData
    if (file) {
      if (Array.isArray(file)) {
        file.forEach((f: File) => {
          formData.append("file", f);
        });
      } else {
        formData.append("file", file);
      }
    }

    const response: AxiosResponse = await this.http.post(url, formData, {
      ...this.config,
      headers: {
        ...this.config.headers,
        "Content-Type": "multipart/form-data",
      },
    });

    return response.data;
  }
}
