export enum HTTPErrorCode {
  conflict_error = "conflict_error",
  not_found_error = "not_found_error",
  authentication_error = "authentication_error",
  unprocessable_error = "unprocessable_error",
  server_error = "server_error",
  too_many_requests_error = "too_many_requests_error",
  bad_request_error = "bad_request_error",
  timeout_error = "timeout_error",
  network_error = "network_error",
  forbidden_error = "forbidden_error",
  server_unavailable_error = "server_unavailable_error",
  unknown_error = "unknown_error",
}

export enum AdmissionRequestStatus {
  created = "created",
  earnings_analysis = "earnings_analysis",
  judicial_analysis = "judicial_analysis",
  documents_analysis = "documents_analysis",
  risk_analysis = "risk_analysis",
  home_visit = "home_visit",
  approved = "approved",
  rejected = "rejected",
}

export enum RequestPersonalDataStepStatus {
  pending = "pending",
  completed = "completed",
  approved = "approved",
}

export enum RequestDocumentStatus {
  pending = "pending",
  approved = "approved",
  rejected = "rejected",
}

export enum GigPlatform {
  uber = "uber",
  didi = "didi",
  // removing InDrive SCRUM-128
  // indriver = "indriver",
}

export enum PalencaRetrievalStatus {
  pending = "pending",
  queued = "queued",
  success = "success",
  error = "error",
}

export enum EarningsAnalysisStatus {
  pending = "pending",
  approved = "approved",
  rejected = "rejected",
  approved_with_conditions = "approved_with_conditions",
}

export enum MediaType {
  proof_of_tax_situation = "proof_of_tax_situation",
  identity_card_front = "identity_card_front",
  identity_card_back = "identity_card_back",
  drivers_license_front = "drivers_license_front",
  drivers_license_back = "drivers_license_back",
  proof_of_address = "proof_of_address",
  bank_statement = "bank_statement",
  home_visit_evidence = "home_visit_evidence",
  proof_of_completion_of_safety_courses = "proof_of_completion_of_safety_courses",
  avg_weekly_income_of_last_twelve_weeks = "avg_weekly_income_of_last_twelve_weeks",
  ride_share_ride_history = "ride_share_ride_history",
  ride_share_dates = "ride_share_dates",
  driving_record = "driving_record",
  selfie_photo = "selfie_photo",
  garage_photo = "garage_photo",
  curp = "curp",
}

export enum MediaStatus {
  pending = "pending", // A document that has been uploaded but not yet processed
  active = "active", // A document that is currently in use and valid
  deleted = "deleted", // A document that has been marked for deletion
  archived = "archived", // A document that is no longer in active use but retained for historical purposes
}

export enum PalencaWidgetEventType {
  ready = "ready",
  user_created = "user_created",
  connection_success = "connection_success",
  connection_error = "connection_error",
}

export enum AdmissionRequestDocumentType {
  identity_card_front = "identity_card_front",
  identity_card_back = "identity_card_back",
  proof_of_address = "proof_of_address",
  bank_statement_month_1 = "bank_statement_month_1",
  bank_statement_month_2 = "bank_statement_month_2",
  bank_statement_month_3 = "bank_statement_month_3",
  // bank_statement_month_4 = "bank_statement_month_4",
  // bank_statement_month_5 = "bank_statement_month_5",
  // bank_statement_month_6 = "bank_statement_month_6",
}

export enum AdmissionRequestAdditionalDocumentType {
  drivers_license_front = "drivers_license_front",
  drivers_license_back = "drivers_license_back",
  curp = "curp",
  proof_of_tax_situation = "proof_of_tax_situation",
  selfie_photo = "selfie_photo",
  garage_photo = "garage_photo",
  solidarity_obligor_identity_card_front = "solidarity_obligor_identity_card_front",
  solidarity_obligor_identity_card_back = "solidarity_obligor_identity_card_back",
}

export enum RequestDocumentsAnalysisStatus {
  pending = "pending",
  approved = "approved",
  rejected = "rejected",
}

export enum AdmissionRequestDocumentTypeUS {
  drivers_license_front = "drivers_license_front",
  drivers_license_back = "drivers_license_back",
  proof_of_address = "proof_of_address",
  bank_statement_month_1 = "bank_statement_month_1",
  bank_statement_month_2 = "bank_statement_month_2",
  bank_statement_month_3 = "bank_statement_month_3",
  bank_statement_month_4 = "bank_statement_month_4",
  bank_statement_month_5 = "bank_statement_month_5",
  bank_statement_month_6 = "bank_statement_month_6",
  // proof_of_completion_of_safety_courses = "proof_of_completion_of_safety_courses",
  // avg_weekly_income_of_last_twelve_weeks = "avg_weekly_income_of_last_twelve_weeks",
  // ride_share_ride_history = "ride_share_ride_history",
  // ride_share_dates = "ride_share_dates",
  // driving_record = "driving_record",
  // signature = "signature",
}

export enum HomeVisitStatus {
  pending = "pending",
  approved = "approved",
  rejected = "rejected",
}
