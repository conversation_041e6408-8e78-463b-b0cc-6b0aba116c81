import {
  IAdmissionRequest,
  IEarningsAnalysis,
  IHomeVisit,
  IPalencaAccount,
  IPalencaAccountRetrieval,
  IRequestAvalData,
  IRequestDocument,
  IRequestDocumentsAnalysis,
  IRequestLocationData,
  IRequestPalenca,
  IRequestPersonalData,
} from "core/controllers/admissionRequest";
import { IMedia } from "core/controllers/media";
import {
  IPalencaWidgetEvent,
  IPalencaWidgetEventData,
  IPalencaWidgetEventError,
  ISearchParams,
} from "core/controllers/types";

import {
  AdmissionRequest,
  AvalData,
  EarningsAnalysis,
  HomeVisit,
  LocationData,
  Media,
  PalencaAccount,
  PalencaAccountRetrieval,
  PalencaWidgetEvent,
  PalencaWidgetEventData,
  PalencaWidgetEventError,
  RequestDocument,
  RequestDocumentsAnalysis,
  RequestPalenca,
  RequestPersonalData,
  SearchParams,
} from "./entities";
import { EarningsAnalysisStatus } from "./enums";

export const searchParamsAdapter = (params: ISearchParams) => {
  return new SearchParams(params);
};

export const personalDataAdapter = (
  personalData: IRequestPersonalData,
): RequestPersonalData => {
  return new RequestPersonalData({
    status: personalData.status,
    firstName: personalData.firstName || "",
    lastName: personalData.lastName || "",
    email: personalData.email || "",
    phone: personalData.phone || "",
    birthdate: personalData.birthdate || "",
    taxId: personalData.taxId || "",
    nationalId: personalData.nationalId || "",
    postalCode: personalData.postalCode || "",
    city: personalData.city || "",
    state: personalData.state || "",
    neighborhood: personalData.neighborhood || "",
    street: personalData.street || "",
    streetNumber: personalData.streetNumber || "",
    department: personalData.department || "",
    country: personalData.country || "",
    ssn: personalData.ssn || "",
    rideShareTotalRides: personalData.rideShareTotalRides,
    avgEarningPerWeek: personalData.rideShareTotalRides,
    ocnBackgroundAndCreditCheckForApplication:
      personalData.ocnBackgroundAndCreditCheckForApplication || false,
    privacyPolicy: personalData.privacyPolicy || false,
  });
};

export const requestDocumentsAdapter = (documentsDetail: IRequestDocument) => {
  return new RequestDocument({
    mediaId: documentsDetail.mediaId,
    status: documentsDetail.status,
    type: documentsDetail.type,
  });
};

export const requestDocumentsAnalysisAdapter = (
  requestDocuments: IRequestDocumentsAnalysis,
) => {
  const documents = requestDocuments.documents.map((document) => {
    return requestDocumentsAdapter(document as unknown as IRequestDocument);
  });
  return new RequestDocumentsAnalysis({
    status: requestDocuments.status,
    documents,
  });
};

export const palencaAccountRetrievalAdapter = (
  palencaAccountRetrieval: IPalencaAccountRetrieval,
) => {
  return new PalencaAccountRetrieval({
    status: palencaAccountRetrieval.status,
  });
};

export const palencaAccountAdapter = (palencaAccount: IPalencaAccount) => {
  return new PalencaAccount({
    accountId: palencaAccount.accountId,
    platform: palencaAccount.platform,
    earnings: palencaAccountRetrievalAdapter(palencaAccount.earnings!),
    metrics: palencaAccountRetrievalAdapter(palencaAccount.metrics!),
  });
};

export const requestPalencaAdapter = (requestPalenca: IRequestPalenca) => {
  const accounts = requestPalenca.accounts.map((account) => {
    return palencaAccountAdapter(account);
  });

  return new RequestPalenca({
    widgetId: requestPalenca.widgetId,
    externalId: requestPalenca.externalId,
    accounts: accounts,
  });
};

export const earningsAnalysisAdapter = (
  earningsAnalysis: IEarningsAnalysis,
) => {
  return new EarningsAnalysis({
    status: earningsAnalysis.status as EarningsAnalysisStatus,
  });
};

export const admissionRequestAdapter = (
  admissionRequest: IAdmissionRequest,
) => {
  return new AdmissionRequest({
    id: admissionRequest.id,
    status: admissionRequest.status,
    palenca: requestPalencaAdapter(admissionRequest.palenca),
    documentsAnalysis: requestDocumentsAnalysisAdapter(
      admissionRequest.documentsAnalysis,
    ),
    personalData: personalDataAdapter(admissionRequest.personalData),
    earningsAnalysis: earningsAnalysisAdapter(
      admissionRequest.earningsAnalysis,
    ),
    avalData: admissionRequest.avalData
      ? avalDataAdapter(admissionRequest.avalData)
      : undefined,
    locationData: admissionRequest.locationData
      ? locationDataAdapter(admissionRequest.locationData)
      : undefined,
    homeVisit: admissionRequest.homeVisit
      ? homeVisitDataAdapter(admissionRequest.homeVisit)
      : undefined,
  });
};

export const mediaAdapter = (media: IMedia) => {
  if (Array.isArray(media)) {
    return media.map(
      (item) =>
        new Media({
          id: item.id,
          fileName: item.fileName,
          mimeType: item.mimeType,
          path: item.path,
          url: item.url,
          type: item.type,
          status: item.status,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        }),
    );
  }

  return new Media({
    id: media.id,
    fileName: media.fileName,
    mimeType: media.mimeType,
    path: media.path,
    url: media.url,
    type: media.type,
    status: media.status,
    createdAt: media.createdAt,
    updatedAt: media.updatedAt,
  });
};

export const palencaWidgetEventDataAdapter = (
  palencaWidgetEventData: IPalencaWidgetEventData,
) => {
  return new PalencaWidgetEventData({
    accountId: palencaWidgetEventData.account_id,
    platform: palencaWidgetEventData.platform,
    userId: palencaWidgetEventData.user_id,
    country: palencaWidgetEventData.country,
  });
};

export const palencaWidgetEventErrorAdapter = (
  palencaWidgetEventError: IPalencaWidgetEventError,
) => {
  return new PalencaWidgetEventError({
    code: palencaWidgetEventError.code,
    message: palencaWidgetEventError.message,
    errors: palencaWidgetEventError.errors,
  });
};

export const palencaWidgetEventAdapter = (
  palencaWidgetEvent: IPalencaWidgetEvent,
) => {
  const error = palencaWidgetEvent.error
    ? palencaWidgetEventErrorAdapter(palencaWidgetEvent.error)
    : null;
  const data = palencaWidgetEventDataAdapter(palencaWidgetEvent.data);
  return new PalencaWidgetEvent({
    success: palencaWidgetEvent.success,
    error,
    data,
  });
};

export const avalDataAdapter = (avalData: IRequestAvalData) => {
  return new AvalData({
    name: avalData.name,
    phone: avalData.phone,
    email: avalData.email,
    location: avalData.location,
  });
};

export const locationDataAdapter = (locationData: IRequestLocationData) => {
  return new LocationData({
    latitude: locationData.latitude,
    longitude: locationData.longitude,
    location: locationData.location,
    ipAddress: locationData.ipAddress,
  });
};

export const homeVisitDataAdapter = (homeVisitData: IHomeVisit) => {
  return new HomeVisit({
    ...homeVisitData,
  });
};
