import {
  AdmissionRequestAdditionalDocumentType,
  AdmissionRequestDocumentType,
  AdmissionRequestDocumentTypeUS,
  AdmissionRequestStatus,
  EarningsAnalysisStatus,
  GigPlatform,
  HomeVisitStatus,
  MediaStatus,
  MediaType,
  PalencaRetrievalStatus,
  RequestDocumentsAnalysisStatus,
  RequestDocumentStatus,
  RequestPersonalDataStepStatus,
} from "./enums";

export class RequestPersonalData {
  status: RequestPersonalDataStepStatus | null = null;

  firstName = "";

  lastName = "";

  email = "";

  phone = "";

  birthdate = "";

  taxId = "";

  nationalId = "";

  postalCode = "";

  city = "";

  state = "";

  neighborhood = "";

  street = "";

  streetNumber = "";

  department = "";

  country = "";

  ssn = "";

  rideShareTotalRides = null;

  avgEarningPerWeek = null;

  ocnBackgroundAndCreditCheckForApplication = false;

  privacyPolicy = false;

  constructor({ ...data }: Partial<RequestPersonalData>) {
    Object.assign(this, data);
  }
}

export class RequestDocument {
  mediaId?: string | null;

  status?: RequestDocumentStatus | null = null;

  type:
    | AdmissionRequestDocumentType
    | AdmissionRequestDocumentTypeUS
    | AdmissionRequestAdditionalDocumentType
    | null = null;

  constructor({ ...data }: Partial<RequestDocument>) {
    Object.assign(this, data);
  }
}

export class RequestDocumentsAnalysis {
  status: RequestDocumentsAnalysisStatus | null = null;
  documents: RequestDocument[] = [];

  constructor({ ...data }: Partial<RequestDocumentsAnalysis>) {
    Object.assign(this, data);
  }
}

export class PalencaAccountRetrieval {
  status: PalencaRetrievalStatus;

  constructor(props: PalencaAccountRetrieval) {
    this.status = props.status;
  }
}

export class PalencaAccount {
  accountId: string | null = null;

  platform: GigPlatform | null = null;

  earnings: PalencaAccountRetrieval | null = null;

  metrics: PalencaAccountRetrieval | null = null;

  status: string | null = null;

  constructor({ ...data }: Partial<PalencaAccount>) {
    Object.assign(this, data);
  }
}

export class PalencaWidgetEventData {
  accountId: string;
  platform: GigPlatform;
  userId: string;
  country: string;

  constructor(props: PalencaWidgetEventData) {
    this.accountId = props.accountId;
    this.platform = props.platform;
    this.userId = props.userId;
    this.country = props.country;
  }
}

export class PalencaWidgetEventError {
  code: string;
  message: string;
  errors: unknown;

  constructor(props: PalencaWidgetEventError) {
    this.code = props.code;
    this.message = props.message;
    this.errors = props.errors;
  }
}

export class PalencaWidgetEvent {
  data: PalencaWidgetEventData;
  error?: PalencaWidgetEventError | null;
  success: boolean;

  constructor(props: PalencaWidgetEvent) {
    this.data = props.data;
    this.error = props.error;
    this.success = props.success;
  }
}

export class PalencaAccountSuccessLogin {
  accountId: string;
  platform: GigPlatform;
  requestId: string;

  constructor(props: PalencaAccountSuccessLogin) {
    this.accountId = props.accountId;
    this.platform = props.platform;
    this.requestId = props.requestId;
  }
}

export class RequestPalenca {
  widgetId: string; // Its needed to know which Palenca widget was used and listen to the correct webhook

  externalId: string; // This is the same as our customerId, the only way we can map a Palenca customer with our customer is through this field

  accounts: PalencaAccount[]; // We keep track of the accounts that the customer has connected through Palenca

  constructor(props: RequestPalenca) {
    this.widgetId = props.widgetId;
    this.externalId = props.externalId;
    this.accounts = props.accounts;
  }
}

export class EarningsAnalysis {
  status: EarningsAnalysisStatus;

  constructor(props: EarningsAnalysis) {
    this.status = props.status;
  }
}

export class AvalData {
  name: string;
  phone: string;
  email: string;
  location: string;

  constructor(props: AvalData) {
    this.name = props.name;
    this.phone = props.phone;
    this.email = props.email;
    this.location = props.location;
  }
}

export class LocationData {
  latitude: number;
  longitude: number;
  location: string;
  ipAddress: string;

  constructor(props: LocationData) {
    this.latitude = props.latitude;
    this.longitude = props.longitude;
    this.location = props.location;
    this.ipAddress = props.ipAddress;
  }
}

export class HomeVisit {
  status: HomeVisitStatus;
  isAddressProvidedByApplicant: string;
  residentOwnershipStatus: string;
  hasGarage: string;
  comments: string;
  images: Array<string>;
  visitDate: string;
  visitTime: string;
  media: Array<Media>;
  houseInformation: {
    ownProperty: string;
    nameOfOwner: string;
    ownerRelative: string;
    ownerRelativeRelation: string;
    ownerPhone: string;
    typeOfHousing: string;
    noOfBedrooms: number;
    livingRoom: string;
    dinningRoom: string;
    kitchen: string;
    television: string;
    audioSystem: string;
    stove: string;
    refrigerator: string;
    washingMachine: string;
  };
  proofOfPropertyOwnership: Array<string>[];
  visitorEmailAddress: string;
  doesProofOfAddressMatchLocation: string;
  characteristicsOfGarage: string;
  behaviourOfCustomerDuringCall: string;
  homeVisitStepsStatus: {
    personal: RequestPersonalDataStepStatus;
    contact: RequestPersonalDataStepStatus;
    address: RequestPersonalDataStepStatus;
    family: RequestPersonalDataStepStatus;
    property: RequestPersonalDataStepStatus;
    automobile: RequestPersonalDataStepStatus;
    debt: RequestPersonalDataStepStatus;
    references: RequestPersonalDataStepStatus;
    outcome: RequestPersonalDataStepStatus;
  };
  responsible: string;
  createdAt?: Date;
  updatedAt?: Date;
  homeImages: {
    homeImagesFront: string[];
    homeImagesGarage: string[];
    homeImagesSurroundings: string[];
  };

  constructor(props: HomeVisit) {
    this.status = props.status;
    this.isAddressProvidedByApplicant = props.isAddressProvidedByApplicant;
    this.residentOwnershipStatus = props.residentOwnershipStatus;
    this.hasGarage = props.hasGarage;
    this.comments = props.comments;
    this.images = props.images;
    this.visitDate = props.visitDate;
    this.visitTime = props.visitTime;
    this.media = props.media;
    this.houseInformation = props.houseInformation;
    this.proofOfPropertyOwnership = props.proofOfPropertyOwnership;
    this.visitorEmailAddress = props.visitorEmailAddress;
    this.doesProofOfAddressMatchLocation =
      props.doesProofOfAddressMatchLocation;
    this.characteristicsOfGarage = props.characteristicsOfGarage;
    this.behaviourOfCustomerDuringCall = props.behaviourOfCustomerDuringCall;
    this.homeVisitStepsStatus = props.homeVisitStepsStatus;
    this.responsible = props.responsible;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
    this.visitTime = props.visitTime;
    this.homeImages = props.homeImages;
  }
}

export class AdmissionRequest {
  id: string;
  status: AdmissionRequestStatus;
  personalData: RequestPersonalData;
  documentsAnalysis: RequestDocumentsAnalysis;
  palenca: RequestPalenca;
  earningsAnalysis: EarningsAnalysis;
  avalData?: AvalData;
  locationData?: LocationData;
  homeVisit?: HomeVisit;

  constructor(props: AdmissionRequest) {
    this.id = props.id;
    this.status = props.status;
    this.personalData = props.personalData;
    this.documentsAnalysis = props.documentsAnalysis;
    this.palenca = props.palenca;
    this.earningsAnalysis = props.earningsAnalysis;
    this.avalData = props.avalData;
    this.locationData = props.locationData;
    this.homeVisit = props.homeVisit;
  }
}

export class SearchParams {
  id?: string | null = null;
  account_id?: string | null = null;
  skipPalenca?: string | null = null;

  constructor(props: SearchParams) {
    this.id = props.id || null;
    this.account_id = props.account_id || null;
    this.skipPalenca = props.skipPalenca || null;
  }
}

export class Media {
  id?: string | undefined;

  fileName: string;

  path: string;

  type: MediaType;

  status: MediaStatus;

  mimeType: string;

  url?: string;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: Media) {
    this.id = props.id;
    this.fileName = props.fileName;
    this.path = props.path;
    this.type = props.type;
    this.status = props.status;
    this.mimeType = props.mimeType;
    this.url = props.url;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}

export class RequestAvalData {
  name = "";
  phone = "";
  email = "";
  location = "";
  constructor({ ...data }: Partial<RequestAvalData>) {
    Object.assign(this, data);
  }
}

export class RequestLatLongData {
  latitude = 0.0;
  longitude = 0.0;
  location = "";
  ipAddress = "";
  constructor({ ...data }: Partial<RequestLatLongData>) {
    Object.assign(this, data);
  }
}

export class RequestHomeVisitData {
  status = HomeVisitStatus.pending;
  constructor({ ...data }: Partial<RequestHomeVisitData>) {
    Object.assign(this, data);
  }
}
