import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import { Languages } from "./langs";
import { en } from "./locales/en";
import { esMX } from "./locales/es-MX";

i18n.use(initReactI18next).init({
  fallbackLng: "es-MX",
  resources: {
    [Languages.esMX]: {
      translation: esMX,
    },
    [Languages.en]: {
      translation: en,
    },
  },
});

export default i18n;
