export const en = {
  Welcome: "Welcome!",
  WelcomeText1: `We know how exciting it is to get a new vehicle, and at One Car Now
        We are committed to helping you make that dream come true.`,
  WelcomeText2: `Your participation in this evaluation is an important step to achieve this.
        Complete the following information that will allow us to better understand your
        situation and give you a response as soon as possible.`,
  ThankYou: `Thank you!`,
  GetStarted: `Begin`,
  PersonalDataTitle: `Personal Data`,
  PersonalDataSubtitle: `Complete your personal information`,
  FirstName: `First Name`,
  LastName: `Last Name`,
  Phone: `Phone`,
  Email: `Email`,
  Continue: `Continue`,
  PlatformTitle: `Platforms`,
  PlatformSubtitle: `Select the platform you want to connect.`,
  ConnectPlatformTitle: `Update your data to our platform.`,
  ConnectPlatformSubtitle: `Follow these simple steps:`,
  Birthdate: `Birthdate`,
  ZipCode: `Zip code`,
  State: `State`,
  City: `City`,
  Colonia: `Colonia`,
  Street: `Street address`,
  Address1: `Street address line 2`,
  Address2: `Address 2`,
  SSN: `SSN`,
  LicenseTitle: `License Front`,
  LicenseSubtitle: `Upload the front of your license in JPG or PNG format, no larger than {{maxFileSizeMB}} MB`,
  LicenseBackTitle: `License Back`,
  LicenseBackSubtitle: `Upload the back of your license in JPG or PNG format, no larger than {{maxFileSizeMB}} MB`,
  Back: `Back`,
  UploadFile: `Upload File`,
  ProofOfAddressTitle: `Proof of Address`,
  ProofOfAddressSubtitle: `Upload proof of address in PDF, JPG or PNG format, no larger than {{maxFileSizeMB}} MB`,
  ProofOfAddressNote: `Proof of address accepted: Electricity, telephone, water and property receipts.`,
  BankAccountStatementsTitle: `Account Statements`,
  BankAccountStatementsSubtitle: `Upload your latest {{accountStatementNumber}} account statements in PDF format, each no larger than {{maxFileSizeMB}}MB`,
  ProofOfCompletionOfSafetyCoursesTitle: `Proof of Completion of Safety Courses`,
  ProofOfCompletionOfSafetyCoursesSubtitle: `Upload proof of completion of safety courses in PDF format, no larger than {{maxFileSizeMB}} MB`,
  AverageEarningPer: `Average earning per week`,
  RideSharetotalRides: `Ride share total rides`,
  RideShareRideHistoryTitle: `Ride Share Ride History`,
  RideShareRideHistorySubtitle: `Upload your ride share ride history in PDF format, no larger than {{maxFileSizeMB}} MB`,
  DataSendCorrectly: `Your data has been sent correctly`,
  DataSendSuccessMsg: `Thanks for your time. An OCN advisor will contact you soon to inform you of the result of your evaluation, along with the steps to follow.`,
  FirstNameMinMsg: `First name must be at least 3 characters`,
  FirstNameRequired: `First name is required`,
  LastNameMinMsg: `Last name must be at least 3 characters`,
  LastNameRequired: `Last name is required`,
  EmailValidMsg: "Please enter a valid email",
  EmailRequired: "Email is required",
  PhoneNumberRequired: "Phone number is required",
  PhoneNumberMin: "Phone number must be at least 10 characters",
  PhoneNumberMax: "Phone number must be at most 14 characters",
  First: "First",
  Second: "Second",
  ShowExample: "Show Example",
  UploadScreenshot: "Upload screenshot",
  ConnectPlatformScreenShotStepOtherPlatformText:
    "Please upload a screenshot of your driver profile from your ridesharing platform's mobile app.",
  ConnectPlatformScreenShotStepText: `Upload screenshot of your driver profile from the {{platform}} mobile app`,
  ConnectPlatformScreenShotStepSuccess: `Profile uploaded successfully`,
  ConnectPlatformScreenShotStepText1: `Upload screenshot of <bold>{{mainMsg}}</bold> income`,
  ConnectPlatformScreenShotStepSubText: `driver profile`,
  ConnectPlatformScreenShotStepText1SubText: `last twelve weeks`,
  WeekStartDate: `Week Start Date`,
  UploadStatus: `Upload Status`,
  MissingWeekData: `Missing week data`,
  AccountCreatedSuccessfully: `Your {{name}} account has been successfully connected`,
  ConnectPlatForm: `Connect another platform`,
  Folowing: `Following`,
  ConnectedPlatforms: `Connected platforms:`,
  DataCollected: `Data Collected`,
  DataRecordedMessage: `We have successfully recorded your data.`,
  UploadPlatformData: `Please upload platform data`,
  UploadVideo: `Upload Video`,
  UploadVideoTitle: `Upload your data to our platform!`,
  UploadVideoSubtitle: `Follow these simple steps:`,
  RecordYourSellPhoneScreenMainText: `Record your cell phone screen`,
  RecordYourSellPhoneScreenText: `<bold>{{mainMsg}}</bold> using application like Screen Recorder or similar.`,
  ShowProfileText: `<bold>Show your profile: </bold> make sure your name and platform stars are visible.`,
  AccessEarningsText: `<bold>Access your earnings</bold> and select the period of weekly time.`,
  MoreInformationText: `The more information you provide, the faster we can process your request.`,
  UnKnownError: `An unknown error occurred`,
  PleaseTryAgain: `Please try again later. If the problem persists, contact us`,
  EarningAnalysisDataSendSuccessfully: "Your data was sent correctly",
  EarningAnalysisDataSendSubMsg: `Thanks for your time. We are reviewing your data. An OCN advisor will contact you as soon as possible to continue the process.`,
  FileFormatError: "The file format must be ",
  InvalidFileError: "The file is invalid.",
  FileSizeError: "File size should not exceed ",
  UserWithCURPAlreadyExist: "User with CURP already exist",
  PleaseCheckTheFieldsAndTryAgain: "Please check the fields and try again",
  Optional: "optional",
  ProofOfTaxSituation: "Proof of Tax Situation",
  AnErrorOccurredWhileUploadingTheFile:
    "An error occurred while uploading the file",
  ProfileInstructions:
    "Please share a screenshot with details such as your total trips, customer rating, acceptance rate, cancellation rate, and the year you started driving on the platform.",
  ChooseFile: "Choose File",
  Checked: "Successful",
  Required: "Required",
  NoProfileInfo: "The uploaded image does not contain profile information.",
  ErrorUploadingImage: "Error uploading image",
  ErrorUploadingMultipleTimes:
    "Error encountered while attempting to upload the same image multiple times.",
  InvalidData: "Invalid Data",
  SelfiePhoto: "Selfie Photo",
  SelfiePhotoSubtitle:
    "Upload your face photo in JPG or PNG format, no larger than {{maxFileSizeMB}} MB",
  GaragePhoto: "Garage Photo",
  GaragePhotoSubtitle:
    "Upload a photo where you safely park your car in JPG or PNG format, no larger than {{maxFileSizeMB}} MB",
  SolidarityObligorINEFrontPhoto: "INE of the Solidarity Obligor Front",
  SolidarityObligorINEFrontSubtitle:
    "Upload the front side of your solidarity obligor INE in JPG or PNG format, no larger than {{maxFileSizeMB}} MB",
  SolidarityObligorINEBackPhoto: "INE of the Solidarity Obligor Back",
  SolidarityObligorINEBackSubtitle:
    "Upload the back side of your solidarity obligor INE in JPG or PNG format, no larger than {{maxFileSizeMB}} MB",
  SubmitLater: "Submit later",
  AppointmentPageSuccessMessage:
    "Your appointment has been successfully scheduled",
  AppointmentPageCancelMessage:
    "Your appointment has been successfully cancelled",
  AppointmentConfirmed: "Appointment Confirmed",
  Cancel: "Cancel",
  AppointmentCancelMessage: "Are you sure you want to cancel your appointment?",
  AppointmentReversedMessage:
    "This action cannot be reversed and you have to book another appointment.",
  Reschedule: "Reschedule",
  Success: "Success",
  AppointmentCancelToastMessage: "Appointment canceled successfully",
  ErrorOccurredWhileCancelingAppointment:
    "Error occurred while canceling appointment",
  Error: "Error",
  ReScheduleVirtualHomeVisit: "ReSchedule Virtual Home Visit Appointment",
  ScheduleVirtualHomeVisit: "Schedule Virtual Home Visit",
  ThirtyMinutes: "30 min",
  SelectDate: "Select Date",
  TimeZone: "Time zone",
  ErrorOccurredWhileFetchingAvailableSlots:
    "Error occurred while fetching available slots",
  SelectTimeSlot: "Select Time Slot",
  AppointmentBookedSuccessfullyToastMessage: "Appointment booked successfully",
  ErrorOccurredWhileBookingAppointment:
    "Error occurred while booking appointment",
  ScheduleAvailableFor: "Schedules available for",
  meeting_link_creation_failed:
    "Meeting link creation failed, please try again or contact support",
  schedule_not_found: "Schedule not found",
  slot_booking_time_limit: "Slot booking time limit reached",
  appointment_max_reschedule_limit_exceeded:
    "Appointment maximum rescheduling limit reached, please contact support",
  slot_is_not_available: "Slot is not available",
  appointment_already_exists: "Appointment already exist",
  appointment_not_found: "Appointment not found",
  slot_not_found: "Slot not found",
  SolidarityObligorDetails: "Solidarity Obligor Details",
  name: "Name",
  location: "Location",
  enterDetails: "Enter Details",
  ScheduleEvent: "Schedule Event",
  close: "Close",
  pleaseWait: "Please wait",
  userInfoGathering: "OCN is gathering your location data and IP Address",
  userInfoGatheringSuccess: "Location and IP address is successfully fetched",
  unableToGetIPAddress: "Unable to get IP address",
  curpTitle: `CURP`,
  curpSubtitle: `Upload the CURP in PDF format, no larger than {{maxFileSizeMB}} MB`,
  continueOnboarding:
    "To expedite the process and ensure you get your car as quickly as possible, we recommend submitting the remaining documents as soon as possible. Just click Continue and we'll be one step closer to completing your application. We're ready to move forward with you!",
  WelcomeToOCN: "Welcome 👋 to OCN",
  FollowInstructionsOnEachPage:
    "We ask that you follow the instructions on each page to upload the required photos.",
  ImagesUploadedSuccessfully: "Images uploaded successfully",
  CloseThisWindow: "Close this window",
  UploadPhotos: "Upload Photos",
  InstructionForTakingPhotosCorrectly:
    "Instructions for taking photos correctly:",
  GoBack: "Go Back",
  ErrorOccurredWhileDeletingFile: "Error occured while deleting file",
  ViewExample: "View Example",
  InvalidFileFormat: "Invalid file format",
};
