export const esMX = {
  Welcome: "¡Bienvenido!",
  WelcomeText1: `Sabemos lo emocionante que obtener un vehículo nuevo, y en One Car Now
        estamos comprometidos para ayudarte a hacer realidad ese sueño`,
  WelcomeText2: `Tu participación en esta evaluación es un paso importante para lograrlo.
        Completa los siguientes datos que nos permitirán comprender mejor tu
        situación y darte una respuesta lo más pronto posible.`,
  ThankYou: `¡Gracias!`,
  GetStarted: `Empezar`,
  PersonalDataTitle: `Datos personales`,
  PersonalDataSubtitle: `Completa tus datos personales`,
  FirstName: `Nombres`,
  LastName: `Apellidos`,
  Phone: `Teléfono`,
  Email: `Correo electrónico`,
  Continue: `Continuar`,
  PlatformTitle: `Plataformas`,
  PlatformSubtitle: `Selecciona la plataforma que deseas conectar.`,
  ConnectPlatformTitle: `Actualiza tus datos a nuestra plataforma.`,
  ConnectPlatformSubtitle: `Sigue estos sencillos pasos:`,
  Birthdate: `Fecha de nacimiento`,
  ZipCode: `Código postal`,
  State: `Estado`,
  City: `Ciudad`,
  Colonia: `Colonia`,
  Street: `Calle`,
  Address1: `Número Exterior`,
  Address2: `Número Interior (Opcional)`,
  SSN: `SSN`,
  LicenseTitle: `Licencia Frente`,
  LicenseSubtitle: `Sube el frente de tu licencia en formato JPG o PNG, no mayor a {{maxFileSizeMB}} MB`,
  LicenseBackTitle: `Licencia Reverso`,
  LicenseBackSubtitle: `Sube el reverso de tu licencia en formato JPG o PNG, no mayor a {{maxFileSizeMB}} MB`,
  Back: `Atrás`,
  UploadFile: `Subir Archivo`,
  ProofOfAddressTitle: `Comprobante de Domicilio`,
  ProofOfAddressSubtitle: `Sube un comprobante de domicilio en formato PDF, JPG o PNG, no mayor a {{maxFileSizeMB}} MB`,
  ProofOfAddressNote: `Comprobantes de domicilio aceptados: Recibo de luz, teléfono, agua y predial.`,
  BankAccountStatementsTitle: `Estados de Cuenta`,
  BankAccountStatementsSubtitle: `Sube tus últimos {{accountStatementNumber}} estados de cuenta en formato PDF, cada uno no mayor a {{maxFileSizeMB}}MB`,
  ProofOfCompletionOfSafetyCoursesTitle: `Prueba de finalización de cursos de seguridad`,
  ProofOfCompletionOfSafetyCoursesSubtitle: `Cargar comprobante de finalización de cursos de seguridad en formato PDF, no mayor a {{maxFileSizeMB}} MB`,
  AverageEarningPer: `Ganancia promedio por`,
  WeekRideSharetotalRides: `Ride Share total Rides`,
  RideShareRideHistoryTitle: `Historial de viajes de Ride Share`,
  RideShareRideHistorySubtitle: `Sube tu historial de viajes de Ride Share en formato PDF, no mayor a {{maxFileSizeMB}} MB`,
  DataSendCorrectly: `Tus datos se han enviado correctamente`,
  DataSendSuccessMsg: `Gracias por tu tiempo. Un asesor de OCN se pondrá en contacto contigo proximamente para comunicarte el resultado de tu evaluación, junto con los pasos a seguir.`,
  FirstNameMinMsg: `El nombre debe tener al menos 3 caracteres.`,
  FirstNameRequired: `El nombre es requerido.`,
  LastNameMinMsg: `El apellido debe tener al menos 3 caracteres.`,
  LastNameRequired: `El apellido es requerido.`,
  EmailValidMsg: `Por favor, ingresa un correo válido`,
  EmailRequired: `El correo es requerido`,
  PhoneNumberRequired: `El número de teléfono es requerido`,
  PhoneNumberMin: `El número de teléfono debe tener al menos 10 caracteres`,
  PhoneNumberMax: `El número de teléfono debe tener como máximo 14 caracteres`,
  First: "Primero",
  Second: "Segundo",
  ShowExample: "Ver ejemplo",
  UploadScreenshot: "Cargar captura de pantalla",
  ConnectPlatformScreenShotStepOtherPlatformText:
    "Por favor sube una captura de pantalla de tu perfil de conductor desde la aplicación móvil de tu plataforma de ridesharing.",
  ConnectPlatformScreenShotStepText: `Sube una captura de pantalla de tu perfil de conductor desde la aplicación móvil de {{platform}}.`,
  ConnectPlatformScreenShotStepSuccess: `Perfil cargado exitosamente`,
  ConnectPlatformScreenShotStepText1: `Cargue una captura de pantalla de los ingresos de las <bold>{{mainMsg}}</bold>`,
  ConnectPlatformScreenShotStepSubText: `perfil de conductor`,
  ConnectPlatformScreenShotStepText1SubText: `últimas doce semanas`,
  WeekStartDate: `Fecha de inicio de la semana`,
  UploadStatus: `Estado de carga`,
  MissingWeekData: `Datos de la semana faltantes`,
  AccountCreatedSuccessfully: `Tu cuenta de {{name}} se ha conectado correctamente`,
  ConnectPlatForm: `Conectar otra plataforma`,
  Folowing: `Siguiente`,
  ConnectedPlatforms: `Plataformas conectadas:`,
  DataCollected: `Datos recopilados`,
  DataRecordedMessage: `Hemos registrado correctamente sus datos.`,
  UploadPlatformData: `Por favor sube los datos de la plataforma`,
  UploadVideo: `Subir video`,
  UploadVideoTitle: `¡Sube tu data a nuestra plataforma!`,
  UploadVideoSubtitle: `Sigue estos sencillos pasos:`,
  RecordYourSellPhoneScreenMainText: `Graba la pantalla de tu celular`,
  RecordYourSellPhoneScreenText: `<bold>{{mainMsg}}</bold> usando una aplicación como Screen Recorder o similar.`,
  ShowProfileText: `<bold>Muestra tu perfil: </bold> asegúrate de que se vean tu nombre y las estrellas de tu plataforma.`,
  AccessEarningsText: `<bold>Accede a tus ganancias</bold> y selecciona el periodo de tiempo semanal.`,
  MoreInformationText: `Cuanta más información muestres, más rápido podremos procesar tu solicitud.`,
  UnKnownError: ` Ocurrió un error desconocido`,
  PleaseTryAgain: `Por favor, intenta más tarde. Si el problema persiste, contacta con nosotros`,
  EarningAnalysisDataSendSuccessfully: "Tus datos se enviaron correctamente",
  EarningAnalysisDataSendSubMsg: `Gracias por tu tiempo. Estamos revisando tus datos. Un asesor OCN se pondrá en contacto contigo a la brevedad para continuar el proceso.`,
  FileFormatError: "El formato del archivo debe ser ",
  InvalidFileError: "El archivo no es válido.",
  FileSizeError: "El tamaño del archivo no debe exceder ",
  UserWithCURPAlreadyExist: "El usuario con CURP ya existe",
  PleaseCheckTheFieldsAndTryAgain: "Verifica los campos e intenta de nuevo",
  Optional: "Opcional",
  ProofOfTaxSituation: "Constancia de Situación Fiscal",
  AnErrorOccurredWhileUploadingTheFile: "Ocurrió un error al subir el archivo",
  ProfileInstructions:
    "Comparta una captura de pantalla con detalles como el total de viajes, la calificación de los clientes, la tasa de aceptación, la tasa de cancelación y el año en que comenzó a conducir en la plataforma.",
  ChooseFile: "Elija archivo",
  Checked: "Exitoso",
  Required: "Requerido",
  NoProfileInfo: "La imagen cargada no contiene información de perfil.",
  ErrorUploadingImage: "Error al subir la imagen",
  ErrorUploadingMultipleTimes:
    "Se produjo un error al intentar cargar la misma imagen varias veces",
  InvalidData: "Datos no válidos",
  SelfiePhoto: "Foto Selfie",
  SelfiePhotoSubtitle:
    "Sube la foto de tu rostro en formato JPG o PNG, que no supere los {{maxFileSizeMB}} MB",
  GaragePhoto: "Foto Garage",
  GaragePhotoSubtitle:
    "Sube una foto donde estacionas tu auto de manera segura en formato JPG o PNG, no mayor a {{maxFileSizeMB}} MB",
  SolidarityObligorINEFrontPhoto: "INE del Obligado Solidario Frontal",
  SolidarityObligorINEFrontSubtitle:
    "Sube el anverso de tu obligado solidario INE en formato JPG o PNG, no mayor a {{maxFileSizeMB}} MB",
  SolidarityObligorINEBackPhoto: "INE del Obligado Solidario Reverso",
  SolidarityObligorINEBackSubtitle:
    "Sube el reverso de tu obligado solidario INE en formato JPG o PNG, no mayor a {{maxFileSizeMB}} MB",
  SubmitLater: "Enviar más tarde",
  AppointmentPageSuccessMessage: "Tu cita ha sido programada exitosamente",
  AppointmentPageCancelMessage: "Tu cita ha sido cancelada exitosamente",
  AppointmentConfirmed: "Cita confirmada",
  Cancel: "Cancelar",
  AppointmentCancelMessage: "Tu cita ha sido cancelada exitosamente",
  AppointmentReversedMessage:
    "Esta acción no se puede revertir y hay que reservar otra cita.",
  Reschedule: "Reprogramar",
  Success: "Éxito",
  AppointmentCancelToastMessage: "Mensaje de tostado de cancelación de cita",
  ErrorOccurredWhileCancelingAppointment:
    "Error occurred while canceling appointment",
  Error: "Error",
  ReScheduleVirtualHomeVisit: "Reprogramar visita virtual",
  ScheduleVirtualHomeVisit: "Programar visita virtual",
  ThirtyMinutes: "30 minutos",
  SelectDate: "Seleccionar fecha",
  TimeZone: "Zona horaria",
  ErrorOccurredWhileFetchingAvailableSlots:
    "Ocurrió un error al buscar los horarios disponibles",
  SelectTimeSlot: "Seleccionar horario",
  AppointmentBookedSuccessfullyToastMessage: "Cita reservada exitosamente",
  ErrorOccurredWhileBookingAppointment:
    "Se produjo un error al reservar la cita.",
  ScheduleAvailableFor: "Horarios disponibles para",
  meeting_link_creation_failed:
    "La creación del enlace de la reunión falló, inténtelo de nuevo o póngase en contacto con el soporte",
  schedule_not_found: "Programa no encontrado",
  slot_booking_time_limit:
    "El tiempo límite para reservar esta plaza ha pasado. Elija otro horario al menos 30 minutos después.",
  appointment_max_reschedule_limit_exceeded:
    "Se ha alcanzado el límite máximo de reprogramación de citas, póngase en contacto con el soporte",
  slot_is_not_available: "La ranura no está disponible",
  appointment_already_exists: "La cita ya existe",
  appointment_not_found: "Cita no encontrada",
  slot_not_found: "Ranura no encontrada",
  SolidarityObligorDetails: "Detalles del Deudor Solidario",
  name: "Nombre",
  location: "Dirección",
  enterDetails: "Ingrese detalles",
  ScheduleEvent: "Programar evento",
  close: "Cerca",
  pleaseWait: "Espere por favor",
  userInfoGathering:
    "OCN está recopilando sus datos de ubicación y dirección IP",
  userInfoGatheringSuccess:
    "La ubicación y la dirección IP se obtuvieron correctamente",
  unableToGetIPAddress: "No se puede obtener la dirección IP",
  curpTitle: `CURP`,
  curpSubtitle: `Sube el CURP en formato PDF, no mayor a {{maxFileSizeMB}} MB`,
  continueOnboarding:
    "Para agilizar el proceso y que puedas tener tu auto lo antes posible, te recomendamos enviar los documentos restantes cuanto antes. Solo haz clic en Continuar y estaremos un paso más cerca de finalizar tu trámite. ¡Estamos listos para avanzar contigo!",
  WelcomeToOCN: "Bienvenido 👋 a OCN",
  FollowInstructionsOnEachPage: `Te pedimos que sigas las instrucciones compartidas en cada página para
          subir las fotos requeridas.`,
  ImagesUploadedSuccessfully: "Imágenes cargadas correctamente",
  CloseThisWindow: "Cerrar esta ventana",
  UploadPhotos: "Subir fotos",
  InstructionForTakingPhotosCorrectly:
    "Instructivo para tomar las fotos de manera correcta:",
  GoBack: "Regresar",
  ErrorOccurredWhileDeletingFile: "Se produjo un error al eliminar el archivo",
  ViewExample: "Ver Ejemplo",
  InvalidFileFormat: "Formato de archivo no válido",
};
