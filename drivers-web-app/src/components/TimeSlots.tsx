import { Box, Text, useToast } from "@chakra-ui/react";
import { useScheduleView } from "context/ScheduleViewContext";
import {
  AdmissionRequestController,
  ISlot,
} from "core/controllers/admissionRequest";
import { DateTime } from "luxon";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Button } from "./Button";

interface ITimeSlots {
  selectedDate: Date | null;
  setSelectedSlot: (slot: ISlot) => void;
}

export const TimeSlots = (props: ITimeSlots) => {
  const { selectedDate, setSelectedSlot } = props;
  const [availableSlots, setAvailableSlots] = useState<Array<ISlot>>([]);
  const toast = useToast();

  const { setStep, admissionRequest } = useScheduleView();
  const { t } = useTranslation();

  useEffect(() => {
    const admissionRequestController = new AdmissionRequestController();
    const fetchAvailableSlots = async (): Promise<void> => {
      try {
        const dateStr = selectedDate
          ? DateTime.fromJSDate(selectedDate).toISODate()
          : "";
        const [err, slots, response] =
          await admissionRequestController.getCalendarAvailableSlots(
            admissionRequest ? admissionRequest.id : "",
            dateStr!,
          );
        if (response?.success) {
          setAvailableSlots(slots);
        }

        if (err) {
          const errorMessage = response?.error?.code
            ? t(`${response?.error?.code}`)
            : t("ErrorOccurredWhileFetchingAvailableSlots");
          toast({
            title: t("Error"),
            description: errorMessage,
            status: "error",
            isClosable: true,
            duration: 3000,
          });
        }
      } catch (error: unknown) {
        const errorTitle = t("Error");
        const errorMessage = t("ErrorOccurredWhileFetchingAvailableSlots");
        toast({
          title: errorTitle,
          description: errorMessage,
          status: "error",
          isClosable: true,
        });
      }
    };
    fetchAvailableSlots();
    if (selectedDate) {
      fetchAvailableSlots();
    }
  }, [admissionRequest, selectedDate, t, toast]);

  const handleSlotSelect = (slot: ISlot) => {
    setSelectedSlot(slot);
    setStep(3);
  };

  return (
    <Box px={{ base: 4 }}>
      <Box py={4}>
        <Text fontSize={"large"} fontWeight={"Bold"}>
          {t("SelectTimeSlot")}
        </Text>
      </Box>
      <Box>
        <Text>
          {t("ScheduleAvailableFor")}{" "}
          {selectedDate?.toLocaleDateString("en-US", {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </Text>
      </Box>

      <Box
        display={"grid"}
        gridTemplateColumns="repeat(2, 1fr)"
        gap={1}
        textAlign={"center"}
        py={4}
      >
        {availableSlots.map((slot) => {
          // const localStartTime = DateTime.fromISO(slot.startTime)
          //   .setZone("local")
          //   .toFormat("HH:mm");

          // const localEndTime = DateTime.fromISO(slot.endTime)
          //   .setZone("local")
          //   .toFormat("HH:mm");

          const localStartTime = slot.startTime;
          const localEndTime = slot.endTime;

          return (
            <Button
              key={slot.id}
              variant="calendarBtn-primary"
              onClick={() => handleSlotSelect(slot)}
              px={12}
            >
              <Box>
                {localStartTime} - {localEndTime}
              </Box>
            </Button>
          );
        })}
      </Box>
    </Box>
  );
};
