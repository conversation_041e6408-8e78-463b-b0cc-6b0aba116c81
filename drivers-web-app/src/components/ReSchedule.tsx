import {
  Box,
  Container,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Text,
  useDisclosure,
  useToast,
} from "@chakra-ui/react";
import { Steps, useScheduleView } from "context/ScheduleViewContext";
import { AdmissionRequestController } from "core/controllers/admissionRequest";
import { Fragment, useState } from "react";
import { useTranslation } from "react-i18next";

import { AppointmentDetails } from "./AppointmentScheduleDetails";
import { Button } from "./Button";
import { CarSvg } from "./Car";

export const ReScheduleView = () => {
  const { step, setStep } = useScheduleView();
  const { t } = useTranslation();

  return (
    step === Steps.Reschedule && (
      <Box
        px={4}
        py={4}
        display={{ base: "block", md: "flex" }}
        justifyContent={{ base: "center", md: "center" }}
      >
        <Box
          px={8}
          w={{ md: "70%" }}
          display={"flex"}
          flexDirection={"column"}
          gap={2}
        >
          <Button
            variant="brand"
            w="full"
            onClick={() => {
              setStep(Steps.Date);
            }}
          >
            {t("Reschedule")}
          </Button>
          <CancelSlotModal />
        </Box>
      </Box>
    )
  );
};

export const ReScheduleHeading = () => {
  const { step } = useScheduleView();
  const { t } = useTranslation();

  return step === Steps.Reschedule ? (
    <Box px={4} py={4}>
      <Text textAlign={"center"} fontSize={"x-large"} fontWeight={800}>
        {t("AppointmentConfirmed")}
      </Text>
    </Box>
  ) : null;
};

const CancelSlotModal = () => {
  const { setStep, appointment } = useScheduleView();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();
  const { t } = useTranslation();

  const handleCancelAppointment = async (): Promise<void> => {
    setIsLoading(true);
    const admissionRequestController = new AdmissionRequestController();
    const payload = {
      slot: appointment?.slot,
      admissionRequestId: appointment?.admissionRequestId,
      id: appointment?.id,
    };
    const [err, , response] =
      await admissionRequestController.cancelHomeVisitAppointment(
        appointment?.admissionRequestId ?? "",
        payload,
      );

    setIsLoading(false);
    if (response?.success) {
      toast({
        title: t("Success"),
        description: t("AppointmentCancelToastMessage"),
        status: "success",
      });
      setStep(Steps.Cancel);
      onClose();
    }
    if (err) {
      const errorMessage =
        response?.error?.code || t("ErrorOccurredWhileCancelingAppointment");
      toast({
        title: t("Error"),
        description: errorMessage,
        status: "error",
        isClosable: true,
      });
    }
  };

  return (
    <Fragment>
      <Button
        variant="ghost"
        w="full"
        backgroundColor={"#FD3D39"}
        color={"#FFFFFF"}
        border={0}
        _hover={{ backgroundColor: "#c92100" }}
        onClick={onOpen}
      >
        {t("Cancel")}
      </Button>

      <Container centerContent>
        <Modal isOpen={isOpen} onClose={onClose} isCentered>
          <ModalOverlay />
          <ModalContent>
            <ModalCloseButton />
            <ModalBody px={4} py={6}>
              <Box px={4} py={4}>
                <Text color={"#0A293B"} fontWeight={"bold"}>
                  {t("AppointmentCancelMessage")}
                </Text>
                <Text color="#5A7190">{t("AppointmentReversedMessage")}</Text>
              </Box>
              <Box
                px={8}
                py={2}
                display={"flex"}
                justifyItems={"flex-end"}
                gap={2}
              >
                <Button
                  variant="brand"
                  w="full"
                  onClick={() => {
                    setStep(Steps.Date);
                    onClose();
                  }}
                  isDisabled={isLoading}
                >
                  {t("Reschedule")}
                </Button>

                <Button
                  variant="ghost"
                  w="full"
                  backgroundColor={"#FD3D39"}
                  color={"#FFFFFF"}
                  border={0}
                  _hover={{ backgroundColor: "#c92100" }}
                  onClick={handleCancelAppointment}
                  isLoading={isLoading}
                  isDisabled={isLoading}
                >
                  {t("Cancel")}
                </Button>
              </Box>
            </ModalBody>
          </ModalContent>
        </Modal>
      </Container>
    </Fragment>
  );
};

const AppointmentReScheduleImg = () => {
  const { step } = useScheduleView();
  return (
    <Box flex={1}>
      {step === Steps.Reschedule && (
        <Box
          px={8}
          display={"flex"}
          alignItems={"center"}
          justifyContent={"center"}
          py={8}
        >
          <CarSvg />
        </Box>
      )}
    </Box>
  );
};

export const AppointmentReScheduler = () => {
  return (
    <Fragment>
      <AppointmentDetails />
      <AppointmentReScheduleImg />
    </Fragment>
  );
};
