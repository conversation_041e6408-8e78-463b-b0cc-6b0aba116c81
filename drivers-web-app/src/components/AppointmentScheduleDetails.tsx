import { TimeIcon } from "@chakra-ui/icons";
import { Box, Text } from "@chakra-ui/react";
import { useScheduleView } from "context/ScheduleViewContext";
import { DateTime } from "luxon";
import { Fragment } from "react";
import { useTranslation } from "react-i18next";
import { FiCalendar, FiClock, FiPhoneCall } from "react-icons/fi";
import { MdOutlineMail } from "react-icons/md";

import { HomeSvg } from "./Svgs/HomeSvg";
import { PersonSvg } from "./Svgs/PersonSvg";

export const AppointmentScheduleInformation = () => {
  const { isStepReschedule } = useScheduleView();
  const { t } = useTranslation();

  return (
    <Box
      py={{ base: 2, md: 8 }}
      px={{ base: 4, md: 8 }}
      flex={{ base: 0, md: 1 }}
    >
      {isStepReschedule ? (
        <Fragment>
          <Text py={2} fontSize={"large"} fontWeight={700}>
            {t("ReScheduleVirtualHomeVisit")}
          </Text>
          <AppointmentDetailsLayout />
        </Fragment>
      ) : (
        <Fragment>
          <Text fontSize={"xx-large"} fontWeight={"bold"}>
            {t("ScheduleVirtualHomeVisit")}
          </Text>
          <Box
            py={4}
            display={"flex"}
            gap={2}
            alignItems={"center"}
            color={"#1A1A1A99"}
          >
            <TimeIcon /> <Text fontWeight={"bold"}>{t("ThirtyMinutes")}</Text>
          </Box>
        </Fragment>
      )}
    </Box>
  );
};

export const AppointmentDetailsLayout = () => {
  const { appointment, admissionRequest } = useScheduleView();
  const date = appointment?.date
    ? DateTime.fromISO(appointment?.date as unknown as string, {
        zone: "utc",
      }).toLocaleString(DateTime.DATE_FULL)
    : "";

  const clientDetails = [
    {
      Icon: HomeSvg,
      text: "Home Visit",
    },
    {
      Icon: PersonSvg,
      text: admissionRequest?.personalData?.firstName,
    },
    {
      Icon: MdOutlineMail,
      text: admissionRequest?.personalData.email,
    },
    {
      Icon: FiPhoneCall,
      text: admissionRequest?.personalData.phone,
    },
    {
      Icon: FiCalendar,
      text: date,
    },
    {
      Icon: FiClock,
      text: appointment?.startTime,
    },
  ];

  return (
    <Box>
      {clientDetails.map((clientDetail, index) => {
        return (
          <Box
            key={clientDetail.text + "" + index}
            py={2}
            display={"flex"}
            gap={4}
            alignItems={"center"}
            color={"#464E5F"}
          >
            <clientDetail.Icon size={25} />{" "}
            <Text fontWeight={"bold"}>{clientDetail.text}</Text>
          </Box>
        );
      })}
    </Box>
  );
};

export const AppointmentDetails = () => {
  return (
    <Box
      py={{ base: 2, md: 8 }}
      px={{ base: 4, md: 8 }}
      minW={{ md: "400px" }}
      flex={{ base: 0, md: 1 }}
    >
      <AppointmentDetailsLayout />
    </Box>
  );
};
