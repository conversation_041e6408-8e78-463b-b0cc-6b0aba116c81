import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputProps,
  Text,
  useToast,
} from "@chakra-ui/react";
import { Button } from "components";
import { Steps, useScheduleView } from "context/ScheduleViewContext";
import {
  AdmissionRequestController,
  ISlot,
} from "core/controllers/admissionRequest";
import { Field, Form, Formik } from "formik";
import { calendarAppointmentValidationSchema } from "misc/validationSchemas";
import { useTranslation } from "react-i18next";

const InputWithCustomStyles = (props: InputProps) => {
  return <Input focusBorderColor={"#6210FF"} placeholder="etc..." {...props} />;
};

interface ICustomerForm {
  selectedSlot: ISlot | null;
  setSelectedDate: (date: Date | null) => void;
  setSelectedSlot: (slot: ISlot | null) => void;
}
export const AppointmentSchedulingForm = (props: ICustomerForm) => {
  const { selectedSlot } = props;

  const toast = useToast();
  const { t } = useTranslation();

  const { setStep, setAppointment, admissionRequest, isStepReschedule } =
    useScheduleView();

  const handleBookAppointment = async (
    values: Record<string, string>,
  ): Promise<void> => {
    const admissionRequestController = new AdmissionRequestController();

    if (!selectedSlot) {
      throw new Error("Por favor selecciona un horario");
    }
    const searchParams = new URLSearchParams(window.location.search);
    const source = searchParams.get("source");
    const entity = searchParams.get("entity");

    const payload = {
      ...values,
      slotId: selectedSlot.id,
      admissionRequestId: admissionRequest?.id,
      isAppointmentReschedule: isStepReschedule,
      source: source ? source : "customer",
      entity: entity,
    };
    const [err, data, response] =
      await admissionRequestController.createHomeVisitAppointment(
        admissionRequest ? admissionRequest.id : "",
        payload,
      );
    if (response?.success) {
      const success = t("Success");
      const description = t("AppointmentBookedSuccessfullyToastMessage");
      toast({
        title: success,
        description: description,
        status: "success",
      });
      setAppointment(data);
      setStep(Steps.Success);
    }
    if (err) {
      const errorMessage = response?.error?.code
        ? t(`${response?.error?.code}`)
        : t("ErrorOccurredWhileBookingAppointment");
      toast({
        title: t("Error"),
        description: errorMessage,
        status: "error",
        isClosable: true,
      });
    }
  };

  const handleSubmit = async (values: Record<string, string>) => {
    await handleBookAppointment(values);
  };

  return (
    <Box px={{ base: 4 }}>
      <Box py={4}>
        <Text fontSize={"large"} fontWeight={"Bold"}>
          {t("enterDetails")}
        </Text>
      </Box>

      <Formik
        initialValues={{
          firstName: admissionRequest?.personalData?.firstName || "",
          lastName: admissionRequest?.personalData?.lastName || "",
          email: admissionRequest?.personalData?.email || "",
          phone: admissionRequest?.personalData?.phone || "",
        }}
        validationSchema={calendarAppointmentValidationSchema}
        onSubmit={handleSubmit}
      >
        {({ errors, isSubmitting, touched }) => {
          return (
            <Form>
              <Box py={2} minW={"350px"}>
                <FormControl
                  isInvalid={
                    touched.firstName && errors.firstName ? true : false
                  }
                >
                  <FormLabel fontWeight={"bold"}>{t("FirstName")}</FormLabel>
                  <Field name="firstName" disabled as={InputWithCustomStyles} />
                  <FormErrorMessage>{errors.firstName}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box py={2}>
                <FormControl
                  isInvalid={touched.lastName && errors.lastName ? true : false}
                >
                  <FormLabel fontWeight={"bold"}>{t("LastName")}</FormLabel>
                  <Field name="lastName" disabled as={InputWithCustomStyles} />
                  <FormErrorMessage>{errors.lastName}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box py={2}>
                <FormControl
                  isInvalid={touched.email && errors.email ? true : false}
                >
                  <FormLabel fontWeight={"bold"}>{t("Email")}</FormLabel>
                  <Field name="email" disabled as={InputWithCustomStyles} />
                  <FormErrorMessage>{errors.email}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box py={2}>
                <FormControl
                  isInvalid={touched.phone && errors.phone ? true : false}
                >
                  <FormLabel fontWeight={"bold"}>{t("Phone")}</FormLabel>
                  <Field name="phone" disabled as={InputWithCustomStyles} />
                  <FormErrorMessage>{errors.phone}</FormErrorMessage>
                </FormControl>
              </Box>

              <Flex w="full" py={4}>
                <Button
                  variant="brand"
                  w="full"
                  type="submit"
                  isDisabled={isSubmitting}
                  isLoading={isSubmitting}
                >
                  {t("ScheduleEvent")}
                </Button>
              </Flex>
            </Form>
          );
        }}
      </Formik>
    </Box>
  );
};
