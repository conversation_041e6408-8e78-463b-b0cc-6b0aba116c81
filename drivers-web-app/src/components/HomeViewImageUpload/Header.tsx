import { Flex, Text } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";

import { HeaderImage } from "./HeaderImage";

export const HomeViewImageUploadHeader = () => {
  const { t } = useTranslation();

  return (
    <>
      <HeaderImage />
      <Flex
        justifyContent={"center"}
        alignItems={"center"}
        flexDirection={"column"}
        py={6}
        gap={2}
      >
        <Text
          fontSize={"xx-large"}
          textAlign={"center"}
          style={{ fontWeight: 900 }}
        >
          {t("UploadPhotos")}
        </Text>
        <Text fontSize={"large"} textAlign={"center"}>
          {t("InstructionForTakingPhotosCorrectly")}
        </Text>
      </Flex>
    </>
  );
};
