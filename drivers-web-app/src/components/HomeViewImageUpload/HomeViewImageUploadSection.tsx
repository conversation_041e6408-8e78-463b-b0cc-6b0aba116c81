import { Box, useSteps } from "@chakra-ui/react";
import { useMediaQuery } from "hooks/useMediaQuery";
import { ReactNode } from "react";

import { ImageUploadDesktopStepper } from "../Steppers/ImageUploadDesktopStepper";
import { ImageUploadMobileStepper } from "../Steppers/ImageUploadMobileStepper";

export const ImageUploadSection = (props: {
  handleSteps: (step: number) => void;
  children: ReactNode;
}) => {
  const { handleSteps, children } = props;
  const { activeStep, goToNext, goToPrevious, setActiveStep } = useSteps({
    count: 2,
  });
  const isSmallDevice = useMediaQuery("only screen and (max-width : 768px)");

  return (
    <Box px={4}>
      {children}
      {isSmallDevice ? (
        <ImageUploadMobileStepper
          activeStep={activeStep}
          goToNext={goToNext}
          goToPrevious={goToPrevious}
          handleSteps={handleSteps}
        />
      ) : (
        <ImageUploadDesktopStepper
          activeStep={activeStep}
          goToNext={goToNext}
          goToPrevious={goToPrevious}
          handleSteps={handleSteps}
          setActiveStep={setActiveStep}
        />
      )}
    </Box>
  );
};
