import { Box } from "@chakra-ui/react";
import { AppointmentSchedulingForm } from "components/AppointmentSchedulingForm";
import { SchedulingCalendar } from "components/SchedulingCalendar";
import { TimeSlots } from "components/TimeSlots";
import { Steps, useScheduleView } from "context/ScheduleViewContext";
import { ISlot } from "core/controllers/admissionRequest";
import { Fragment, useState } from "react";
import { DayInfo } from "utils";

import { AppointmentScheduleInformation } from "./AppointmentScheduleDetails";

export const AppointmentSchedulerClient = () => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedSlot, setSelectedSlot] = useState<ISlot | null>(null);

  const { step, setStep } = useScheduleView();

  const handleDateSelect = (dayInfo: DayInfo) => {
    if (!dayInfo.date || dayInfo.disabled) return;
    setSelectedDate(dayInfo.date);
    setStep(2);
  };

  return (
    <Box flex={1}>
      <Box
        display={"flex"}
        flexDirection={"column"}
        alignItems={"center"}
        borderLeft={{ base: "none", md: "1px solid #E2E8F0" }}
      >
        <Box>
          {step === Steps.Date && (
            <SchedulingCalendar handleDateSelect={handleDateSelect} />
          )}
          {step === Steps.Time && (
            <TimeSlots
              selectedDate={selectedDate}
              setSelectedSlot={setSelectedSlot}
            />
          )}
          {step === Steps.Form && (
            <AppointmentSchedulingForm
              selectedSlot={selectedSlot}
              setSelectedDate={setSelectedDate}
              setSelectedSlot={setSelectedSlot}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export const AppointmentScheduler = () => {
  return (
    <Fragment>
      <AppointmentScheduleInformation />
      <AppointmentSchedulerClient />
    </Fragment>
  );
};
