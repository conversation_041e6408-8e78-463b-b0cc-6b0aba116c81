import {
  Container,
  Image,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  useDisclosure,
} from "@chakra-ui/react";
import { Button } from "components";
import { t } from "i18next";
import React from "react";

interface ImageDialogProps {
  imageUrl: string;
  altText?: string;
}

const ImageDialog: React.FC<ImageDialogProps> = ({
  imageUrl,
  altText = "Image",
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <Container centerContent>
      {/* Button to open the modal */}
      <Image src={imageUrl} alt={altText} maxW="60%" onClick={onOpen}></Image>
      <br></br>
      <Button
        as="span"
        variant="outline"
        width="60"
        height="10"
        style={{
          cursor: "pointer",
        }}
        onClick={onOpen}
      >
        {t("ShowExample")}
      </Button>
      {/* Modal component */}
      <Modal isOpen={isOpen} onClose={onClose} isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalCloseButton />
          <ModalBody>
            <Image src={imageUrl} alt={altText} maxW="100%" h="auto" />
          </ModalBody>
        </ModalContent>
      </Modal>
    </Container>
  );
};

export default ImageDialog;
