import { MediaController } from "core/controllers/media";
import { Media } from "core/domain/entities";
import { useState } from "react";

export const useFileUpload = (mediaType, totalFiles = 5) => {
  const [uploadedFiles, setUploadedFiles] = useState<Media[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const mediaController = new MediaController();

  const uploadFile = async (file) => {
    setIsLoading(true);
    const [err, data] = await mediaController.uploadMedia(file, mediaType);
    if (!err) {
      setUploadedFiles([...uploadedFiles, data]);
    }
    setIsLoading(false);
  };

  const deleteFile = (index) => {
    const newUploadedFiles = uploadedFiles.filter((_, idx) => idx !== index);
    setUploadedFiles(newUploadedFiles);
  };

  return {
    uploadedFiles,
    uploadFile,
    deleteFile,
    isLoading,
    istotalFilesReached: uploadedFiles.length >= totalFiles,
  };
};

export default useFileUpload;
