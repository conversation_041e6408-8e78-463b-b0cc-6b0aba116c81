// FileUpload.js

import { DeleteIcon } from "@chakra-ui/icons";
import { Box, Flex, Text, VStack } from "@chakra-ui/react";
import { Media } from "core/domain/entities";
import { MediaType } from "core/domain/enums";
import { truncateFileName } from "core/misc/utils";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Button } from "../Button";
import useFileUpload from "./useFileUpload";

interface FileUploadProps {
  accept: {
    text: string;
    types: string[];
  };
  primaryButtonText: string;
  mediaType: MediaType;
  showPreview?: boolean;
  totalFiles?: number;
  maxFileSize?: number; // in bytes
  onUploadChange?: (media: Media[]) => void;
  identifier?: string;
}

const arrayToAcceptString = (types: string[]) => {
  return types.map((item) => `${item}`).join(",");
};

export const FileUpload = ({
  primaryButtonText,
  accept,
  mediaType,
  showPreview = false,
  totalFiles = 1,
  maxFileSize = 1024 * 1024 * 5, // 5 MB
  onUploadChange,
  identifier,
}: FileUploadProps) => {
  const {
    uploadedFiles,
    uploadFile,
    deleteFile,
    isLoading,
    istotalFilesReached,
  } = useFileUpload(mediaType, totalFiles);

  const [errorMessage, setErrorMessage] = useState("");

  const { t } = useTranslation();

  const handleFileChange = async (event) => {
    const file = event.target.files[0];
    setErrorMessage("");
    if (!file) {
      setErrorMessage(t("InvalidFileError"));
      return;
    }

    if (!accept.types.includes(file.type)) {
      setErrorMessage(`${t("FileFormatError")} ${accept.text}.`);
      return;
    }

    if (file) {
      if (file.size > maxFileSize) {
        setErrorMessage(
          `${t("FileSizeError")} ${maxFileSize / 1024 / 1024} MB.`,
        );
        return;
      }
      await uploadFile(file);
    }
    event.target.value = null;
  };

  useEffect(() => {
    if (onUploadChange) {
      onUploadChange(uploadedFiles);
    }
  }, [uploadedFiles, onUploadChange]);

  const htmlKey = identifier ? `${identifier}-file-upload` : `file-upload`;

  return (
    <VStack key={`${identifier}-vStack`} spacing={3} width="full">
      {errorMessage && (
        <Text color="red.500" mb={4}>
          {errorMessage}
        </Text>
      )}
      {uploadedFiles.map((file, index) => (
        <Box
          key={index}
          borderRadius="md"
          p={4}
          width="100%"
          border="1px"
          borderColor="gray.300"
        >
          {showPreview && file && (
            <Box borderRadius="md" p={4} width="100%" mt={2}>
              <img
                src={file.url}
                alt="Preview"
                style={{ width: "100%", height: "auto" }}
              />
            </Box>
          )}
          <Flex alignItems="center" justify="space-between" w="full">
            <Box>
              <Text fontSize="md" fontWeight="semibold" ml={2} color="brand">
                {truncateFileName(file.fileName)}
              </Text>
            </Box>
            <DeleteIcon
              boxSize={5}
              color="brand"
              _hover={{ opacity: 0.8 }}
              style={{ cursor: "pointer" }}
              onClick={() => deleteFile(index)}
            />
          </Flex>
        </Box>
      ))}
      {!istotalFilesReached && (
        <label key={`${identifier}-label`} htmlFor={htmlKey}>
          <Button
            key={`${identifier}-upload-button`}
            id="fileUploadPrimaryButton"
            as="span"
            disabled={isLoading}
            variant="outline"
            width="80"
            isLoading={isLoading}
            style={{
              cursor: isLoading ? "not-allowed" : "pointer",
            }}
          >
            {primaryButtonText}
          </Button>
        </label>
      )}
      <input
        key={`${identifier}-input-file`}
        type="file"
        accept={arrayToAcceptString(accept.types)}
        onChange={handleFileChange}
        style={{ display: "none" }}
        disabled={isLoading || istotalFilesReached}
        id={htmlKey}
      />
    </VStack>
  );
};
