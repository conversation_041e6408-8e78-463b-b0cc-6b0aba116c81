import { Box, Flex, Text } from "@chakra-ui/react";
import { ImageViewer } from "components/Steppers/ImageViewer";
import { allowedFileFormats } from "components/Steppers/utils";
import { FileUploadLogo } from "components/Svgs/FileUploadLogo";
import { MdOutlineUploadFile } from "components/Svgs/MdOutlineUploadFile";
import { ShowLogo } from "components/Svgs/ShowLogo";
import { TrashLogo } from "components/Svgs/TrashLogo";
import { MediaController } from "core/controllers/media";
import { MediaType } from "core/domain/enums";
import { Dispatch, ReactNode, SetStateAction, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { RiLoader2Fill } from "react-icons/ri";

export interface IUploadedFile {
  id: string;
  fileName: string;
  path: string;
  url: string;
  createdAt: string;
  updatedAt: string;
  file?: File;
}

export const uploadFile = async (files: Array<File>) => {
  const mediaController = new MediaController();
  const [err, data] = await mediaController.uploadMedia(
    files,
    MediaType.home_visit_evidence,
  );
  if (err) {
    throw new Error("Error occured while uploading file.");
  }
  return data as unknown as Array<IUploadedFile>;
};

interface IFilesSection {
  requestId: string;
  uploadText: ReactNode;
  fieldToLookForImages: string;
  isUploading?: boolean;
  setIsUploading: Dispatch<SetStateAction<boolean>>;
  uploadedFiles: Array<IUploadedFile>;
  setUploadedFiles: Dispatch<SetStateAction<IUploadedFile[]>>;
  handleDelete: (fileMetadata: IUploadedFile) => Promise<void>;
  uploadImage: (newFiles: Array<File>) => Promise<void>;
}

export const FilesSection = (props: IFilesSection) => {
  const { uploadText, isUploading, uploadedFiles, handleDelete, uploadImage } =
    props;

  const onDrop = useCallback(uploadImage, [uploadImage]);

  const { getRootProps, getInputProps } = useDropzone({ onDrop });

  return (
    <div>
      <Box
        {...getRootProps()}
        cursor={"pointer"}
        border={"dashed"}
        borderRadius={"md"}
        borderColor={"#5E5E5E"}
        borderWidth={"1px"}
      >
        <Flex
          direction={"column"}
          alignItems={"center"}
          justifyContent={"center"}
          py={20}
        >
          <MdOutlineUploadFile />
          {uploadText}
          <input
            {...getInputProps()}
            accept={allowedFileFormats}
            id="file-upload"
            type="file"
          />
        </Flex>
        {isUploading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <RiLoader2Fill size={40} color="#A74DF9" />
          </Box>
        ) : null}
      </Box>

      <Flex gap={2} py={2} flexWrap={{ base: "wrap" }}>
        {uploadedFiles.map((fileMetadata, index) => {
          return (
            <FileUploaded
              key={fileMetadata.fileName + index}
              fileName={fileMetadata.fileName.slice(0, 15)}
              fileSize={
                fileMetadata?.file?.size
                  ? (fileMetadata.file.size / (1024 * 1024)).toFixed(2) + "MB"
                  : ""
              }
              fileUrl={fileMetadata.url}
              onShow={() => {
                window.open(fileMetadata.url, "_blank");
              }}
              onDelete={() => handleDelete(fileMetadata)}
            />
          );
        })}
      </Flex>
    </div>
  );
};

interface IFileUploaded {
  fileName: string;
  fileSize: string;
  fileUrl: string;
  onShow: () => void;
  onDelete: () => void;
}

const FileUploaded = (props: IFileUploaded) => {
  const { fileName, fileSize, fileUrl, onDelete } = props;

  return (
    <Flex
      p={2}
      justifyContent="space-between"
      alignItems="center"
      border="1px solid"
      borderColor={"#84A0C8"}
      borderRadius="md"
      width={{ base: "70%", md: "30%" }}
    >
      <Flex alignItems="center" flexGrow={"0.3"}>
        <FileUploadLogo />
        <Box px={2}>
          <Text color={"#1D4F79"} fontSize={{ base: "medium", md: "small" }}>
            {fileName}
          </Text>
          <Text color={"#1D4F79"} fontSize={{ base: "medium", md: "small" }}>
            {fileSize}
          </Text>
        </Box>
      </Flex>
      <Flex gap={2}>
        <ImageViewer imagePath={fileUrl}>
          <ShowLogo />
        </ImageViewer>
        <TrashLogo onClick={onDelete} />
      </Flex>
    </Flex>
  );
};
