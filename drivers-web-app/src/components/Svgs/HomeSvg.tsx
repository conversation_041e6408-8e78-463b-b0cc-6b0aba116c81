export const HomeSvg = () => {
  return (
    <svg
      width="21"
      height="20"
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_54_1337)">
        <path
          d="M18.6667 4.64931L12.8333 0.712642C12.1452 0.244639 11.3322 -0.00561523 10.5 -0.00561523C9.66779 -0.00561523 8.85481 0.244639 8.16667 0.712642L2.33333 4.64931C1.76846 5.03112 1.30591 5.54572 0.986271 6.14795C0.666627 6.75019 0.499655 7.42167 0.500001 8.10347V15.8335C0.501324 16.9381 0.940736 17.9972 1.72185 18.7783C2.50297 19.5594 3.562 19.9988 4.66667 20.0001H16.3333C17.438 19.9988 18.497 19.5594 19.2782 18.7783C20.0593 17.9972 20.4987 16.9381 20.5 15.8335V8.10347C20.5003 7.42167 20.3334 6.75019 20.0137 6.14795C19.6941 5.54572 19.2315 5.03112 18.6667 4.64931ZM18.8333 15.8335C18.8333 16.4965 18.5699 17.1324 18.1011 17.6012C17.6323 18.0701 16.9964 18.3335 16.3333 18.3335H4.66667C4.00363 18.3335 3.36774 18.0701 2.8989 17.6012C2.43006 17.1324 2.16667 16.4965 2.16667 15.8335V8.10347C2.16664 7.69424 2.26708 7.29126 2.45917 6.9299C2.65125 6.56855 2.9291 6.25987 3.26833 6.03098L9.10167 2.09431C9.51416 1.81408 10.0013 1.66424 10.5 1.66424C10.9987 1.66424 11.4858 1.81408 11.8983 2.09431L17.7317 6.03098C18.0709 6.25987 18.3488 6.56855 18.5408 6.9299C18.7329 7.29126 18.8334 7.69424 18.8333 8.10347V15.8335ZM10.5 5.83848C9.5102 5.83835 8.54264 6.13201 7.71987 6.68224C6.8971 7.23247 6.25616 8.01453 5.87822 8.92933C5.50029 9.84414 5.40237 10.8505 5.59688 11.821C5.79139 12.7915 6.26956 13.6825 6.97083 14.381L8.49083 15.8676C9.02944 16.3892 9.74981 16.6809 10.4996 16.6809C11.2494 16.6809 11.9697 16.3892 12.5083 15.8676L14.0358 14.3743C14.7352 13.675 15.2115 12.7841 15.4045 11.8141C15.5975 10.8441 15.4985 9.83868 15.12 8.92498C14.7415 8.01127 14.1005 7.23034 13.2782 6.68094C12.4558 6.13154 11.489 5.83836 10.5 5.83848ZM12.8633 13.1893L11.3425 14.6768C11.1165 14.8957 10.8142 15.0181 10.4996 15.0181C10.185 15.0181 9.88268 14.8957 9.65667 14.6768L8.1425 13.196C7.67663 12.7301 7.35928 12.1366 7.23053 11.4904C7.10177 10.8442 7.1674 10.1744 7.41912 9.56554C7.67083 8.95666 8.09734 8.43603 8.64478 8.06941C9.19222 7.70279 9.83603 7.50662 10.4949 7.50569C11.1537 7.50476 11.7981 7.69911 12.3466 8.06418C12.8951 8.42925 13.323 8.94868 13.5765 9.55684C13.8299 10.165 13.8974 10.8346 13.7705 11.4812C13.6436 12.1277 13.3279 12.7221 12.8633 13.1893ZM11.3333 10.8335C11.3333 10.9983 11.2845 11.1594 11.1929 11.2965C11.1013 11.4335 10.9712 11.5403 10.8189 11.6034C10.6666 11.6664 10.4991 11.683 10.3374 11.6508C10.1758 11.6186 10.0273 11.5393 9.91075 11.4227C9.7942 11.3062 9.71484 11.1577 9.68268 10.996C9.65053 10.8344 9.66703 10.6668 9.7301 10.5146C9.79318 10.3623 9.89999 10.2322 10.037 10.1406C10.1741 10.049 10.3352 10.0001 10.5 10.0001C10.721 10.0001 10.933 10.0879 11.0893 10.2442C11.2455 10.4005 11.3333 10.6125 11.3333 10.8335Z"
          fill="#46435F"
        />
      </g>
      <defs>
        <clipPath id="clip0_54_1337">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
