interface IShowLogo {
  onClick?: () => void;
}
export const ShowLogo = (props: IShowLogo) => {
  const { onClick } = props;
  return (
    <svg
      width="28"
      height="29"
      viewBox="0 0 28 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
    >
      <rect x="0.5" y="1" width="27" height="27" rx="3.5" fill="#F6F6F6" />
      <rect x="0.5" y="1" width="27" height="27" rx="3.5" stroke="#CFD8E1" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14 11.5C12.3431 11.5 11 12.8431 11 14.5C11 16.1569 12.3431 17.5 14 17.5C15.6569 17.5 17 16.1569 17 14.5V14.4738C17 14.1478 16.7629 13.8704 16.441 13.8195C15.6105 13.6884 14.9963 12.9859 14.9963 12.186C14.9963 11.8564 14.754 11.577 14.4277 11.5303C14.2877 11.5103 14.1449 11.5 14 11.5ZM12.3247 14.5C12.3247 13.6609 12.9416 12.9658 13.7467 12.8437C13.9724 13.816 14.6868 14.6048 15.6112 14.9608C15.411 15.662 14.7655 16.1753 14 16.1753C13.0747 16.1753 12.3247 15.4253 12.3247 14.5Z"
        fill="#586D79"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14 8.5C11.7377 8.5 9.75113 9.639 8.3617 10.8978C7.66079 11.5328 7.08675 12.22 6.68259 12.8553C6.29956 13.4574 6 14.1198 6 14.6836C6 15.2556 6.30635 15.899 6.69411 16.472C7.10286 17.0761 7.68135 17.7179 8.38459 18.3057C9.78257 19.4742 11.7618 20.5 14 20.5C16.2382 20.5 18.2174 19.4742 19.6154 18.3057C20.3187 17.7179 20.8971 17.0761 21.3059 16.472C21.6937 15.899 22 15.2556 22 14.6836C22 14.1198 21.7004 13.4574 21.3174 12.8553C20.9132 12.22 20.3392 11.5328 19.6383 10.8978C18.2489 9.639 16.2623 8.5 14 8.5ZM7.53672 14.6836C7.53672 14.5507 7.64188 14.185 7.99073 13.6367C8.31845 13.1216 8.80346 12.5361 9.41122 11.9855C10.6391 10.873 12.2684 9.98905 14 9.98905C15.7316 9.98905 17.3609 10.873 18.5888 11.9855C19.1965 12.5361 19.6816 13.1216 20.0093 13.6367C20.3581 14.185 20.4633 14.5507 20.4633 14.6836C20.4633 14.8081 20.3649 15.147 20.0208 15.6555C19.6977 16.133 19.2171 16.6721 18.6117 17.1782C17.3923 18.1974 15.7558 19.0109 14 19.0109C12.2442 19.0109 10.6077 18.1974 9.38833 17.1782C8.7829 16.6721 8.30234 16.133 7.97921 15.6555C7.63509 15.147 7.53672 14.8081 7.53672 14.6836Z"
        fill="#586D79"
      />
    </svg>
  );
};
