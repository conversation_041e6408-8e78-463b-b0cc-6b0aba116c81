interface ITrashLogo {
  onClick: () => void;
}
export const TrashLogo = (props: ITrashLogo) => {
  const { onClick } = props;

  return (
    <svg
      width="28"
      height="29"
      viewBox="0 0 28 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
    >
      <rect x="0.5" y="1" width="27" height="27" rx="3.5" fill="#F6F6F6" />
      <rect x="0.5" y="1" width="27" height="27" rx="3.5" stroke="#CFD8E1" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.2795 20.0181L17.9267 12.8232L19.4363 12.9627L18.7892 20.1577C18.6699 21.4843 17.5725 22.5 16.2583 22.5H11.7417C10.4275 22.5 9.33008 21.4844 9.21076 20.1577L8.56362 12.9627L10.0733 12.8232L10.7204 20.0181C10.7686 20.5535 11.2114 20.9633 11.7417 20.9633H16.2583C16.7886 20.9633 17.2314 20.5535 17.2795 20.0181Z"
        fill="#586D79"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.9209 8.03673C12.8758 8.03673 12.8338 8.05973 12.8091 8.09793L12.283 8.91055H15.717L15.1909 8.09793C15.1662 8.05973 15.1242 8.03673 15.0791 8.03673H12.9209ZM17.5302 8.91055L16.4583 7.25479C16.1534 6.78373 15.6349 6.5 15.0791 6.5H12.9209C12.3651 6.5 11.8466 6.78373 11.5417 7.25479L10.4698 8.91055H8.64968C7.73859 8.91055 7 9.65928 7 10.5829V11.989C7 12.9126 7.73859 13.6614 8.64968 13.6614H19.3503C20.2614 13.6614 21 12.9126 21 11.989V10.5829C21 9.65928 20.2614 8.91055 19.3503 8.91055H17.5302ZM8.64968 10.4473C8.57581 10.4473 8.51592 10.508 8.51592 10.5829V11.989C8.51592 12.0639 8.57581 12.1246 8.64968 12.1246H19.3503C19.4242 12.1246 19.4841 12.0639 19.4841 11.989V10.5829C19.4841 10.508 19.4242 10.4473 19.3503 10.4473H8.64968Z"
        fill="#586D79"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14 14.6706C14.3447 14.6706 14.6242 14.9539 14.6242 15.3033V18.5174C14.6242 18.8669 14.3447 19.1502 14 19.1502C13.6553 19.1502 13.3758 18.8669 13.3758 18.5174V15.3033C13.3758 14.9539 13.6553 14.6706 14 14.6706Z"
        fill="#586D79"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.6592 14.6706C12.004 14.6706 12.2834 14.9539 12.2834 15.3033V17.7139C12.2834 18.0634 12.004 18.3467 11.6592 18.3467C11.3145 18.3467 11.035 18.0634 11.035 17.7139V15.3033C11.035 14.9539 11.3145 14.6706 11.6592 14.6706Z"
        fill="#586D79"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.3408 14.6706C16.6855 14.6706 16.965 14.9539 16.965 15.3033V17.7139C16.965 18.0634 16.6855 18.3467 16.3408 18.3467C15.996 18.3467 15.7166 18.0634 15.7166 17.7139V15.3033C15.7166 14.9539 15.996 14.6706 16.3408 14.6706Z"
        fill="#586D79"
      />
    </svg>
  );
};
