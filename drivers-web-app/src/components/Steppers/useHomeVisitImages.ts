import { useToast } from "@chakra-ui/react";
import { IUploadedFile } from "components/FileUpload/FileUploadHomeVisit";
import {
  AdmissionRequestController,
  IHomeImages,
} from "core/controllers/admissionRequest";
import { MediaController } from "core/controllers/media";
import { HomeVisit } from "core/domain/entities";
import { MediaType } from "core/domain/enums";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { allowedFileFormats } from "./utils";

export const useHomeVisitImages = (
  requestId: string,
  fieldToLookForImages: string,
) => {
  const [isLoading, setLoading] = useState(true);
  const [uploadedFiles, setUploadedFiles] = useState<Array<IUploadedFile>>([]);
  const [homeVisit, setHomeVisit] = useState<HomeVisit>();
  const [imagesIds, setImagesIds] = useState<IHomeImages>({
    images: [],
    homeImages: {
      homeImagesFront: [],
      homeImagesGarage: [],
      homeImagesSurroundings: [],
    },
  });

  const toast = useToast();
  const { t } = useTranslation();

  const setMediaFiles = (
    homeVisit: HomeVisit,
    fieldToLookForImages: string,
  ) => {
    const fieldWithImagesIds =
      homeVisit.homeImages[
        fieldToLookForImages as keyof typeof homeVisit.homeImages
      ];
    const mediaFiles = fieldWithImagesIds
      ? fieldWithImagesIds
          .map((item) => homeVisit.media.find((media) => media.id === item))
          .filter(Boolean)
          .map((mediaMetadata) => mediaMetadata as unknown as IUploadedFile)
      : [];
    setUploadedFiles([...mediaFiles]);
  };

  useEffect(() => {
    async function fetchHomeVisit() {
      const admissionRequestController = new AdmissionRequestController();
      const [, homeVisit, response] =
        await admissionRequestController.getHomeVisit(requestId!);
      if (!response.success) {
        setLoading(false);
        return;
      }
      if (!homeVisit?.homeImages) {
        setLoading(false);
        return;
      }
      setHomeVisit(homeVisit as HomeVisit);
      setMediaFiles(homeVisit, fieldToLookForImages);
      setImagesIds({
        images: Array.isArray(homeVisit?.images) ? homeVisit?.images : [],
        homeImages: homeVisit?.homeImages || {},
      });
      setLoading(false);
    }
    if (!homeVisit) {
      fetchHomeVisit();
    }
  }, [requestId, homeVisit, fieldToLookForImages]);

  const updateImages = async (
    updatedImages: Array<string>,
    images: string[],
  ) => {
    const admissionRequestController = new AdmissionRequestController();

    const allUpdatedImagesIds = new Set<string>();
    images.forEach((id) => allUpdatedImagesIds.add(id));
    updatedImages.forEach((id) => allUpdatedImagesIds.add(id));

    const payload = {
      isHomeVisitData: true,
      homeVisitData: {
        homeImages: {
          ...imagesIds.homeImages,
          [fieldToLookForImages]: updatedImages,
        },
        images: Array.from(allUpdatedImagesIds),
      },
    };

    const [, , homeVisitResponse] =
      await admissionRequestController.updateHomeVisit(requestId!, payload);
    if (!homeVisitResponse.success) {
      return;
    }

    const [, homeVisit, response] =
      await admissionRequestController.getHomeVisit(requestId!);
    if (!response.success) {
      setLoading(false);
      return;
    }
    if (!homeVisit?.homeImages) {
      setLoading(false);
      return;
    }
    setHomeVisit(homeVisit);
    setMediaFiles(homeVisit, fieldToLookForImages);
    setImagesIds({
      images: Array.isArray(homeVisit?.images) ? homeVisit?.images : [],
      homeImages: homeVisit?.homeImages || {},
    });
  };

  const deleteImage = async (fileMetadata: IUploadedFile) => {
    try {
      setLoading(true);
      const uploadedImagesIds = uploadedFiles
        .filter((uploadedFile) => uploadedFile.id !== fileMetadata.id)
        .map((fileMetadata) => fileMetadata.id);

      const images = imagesIds.images.filter(
        (imageId) => imageId !== fileMetadata.id,
      );
      await updateImages(uploadedImagesIds, images);
    } catch (error) {
      toast({
        title: "Error",
        description: t("ErrorOccurredWhileDeletingFile"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const uploadImage = async (newFiles: Array<File>) => {
    setLoading(true);
    const allowedFileTypes = new Set(allowedFileFormats.split(","));
    const invalidFiles = newFiles.filter(
      (file) => !allowedFileTypes.has(file.type),
    );
    if (invalidFiles.length > 0) {
      toast({
        title: "Error",
        description: t("InvalidFileFormat"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      setLoading(false);
      return;
    }
    const mediaController = new MediaController();
    const [err, data] = await mediaController.uploadMedia(
      newFiles,
      MediaType.home_visit_evidence,
    );
    if (err) {
      toast({
        title: "Error",
        description: "Error occured while uploading file",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const uploadedFilesMetadata = data as unknown as IUploadedFile[];
    const uploadedImages = [...uploadedFiles, ...uploadedFilesMetadata].map(
      (fileMetadata) => fileMetadata.id,
    );
    await updateImages(uploadedImages, imagesIds.images);
    setUploadedFiles([...uploadedFiles, ...uploadedFilesMetadata]);
    setLoading(false);
  };

  return {
    homeVisit,
    isLoading,
    setLoading,
    fieldToLookForImages,
    uploadedFiles,
    setUploadedFiles,
    setMediaFiles,
    deleteImage,
    uploadImage,
    imagesIds,
  };
};
