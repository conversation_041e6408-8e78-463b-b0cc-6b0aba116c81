import { Box } from "@chakra-ui/react";

export const StepIndicatorGradient = ({
  bgGradient = "linear-gradient(to right, #6210FF, #A74DF9)",
  border = 0,
  color = "white",
  borderColor = "#5800F7",
  children,
  onClick,
}: {
  bgGradient?: string;
  border?: number;
  color?: string;
  borderColor?: string;
  children: React.ReactNode;
  onClick?: () => void;
}) => {
  const gradientStyle = bgGradient.includes("linear")
    ? { background: bgGradient }
    : { background: `linear-gradient(to right, ${bgGradient})` };

  return (
    <Box
      bgGradient={bgGradient}
      width={"35px"}
      height={"35px"}
      borderRadius={"50%"}
      borderColor={borderColor}
      borderWidth={border}
      style={{
        ...gradientStyle,
        border: border ? `${border}px solid ${borderColor}` : "none",
        color: color,
      }}
      display={"flex"}
      alignItems={"center"}
      justifyContent={"center"}
      flexShrink={"0"}
      cursor={onClick ? "pointer" : "default"}
      onClick={onClick}
    >
      {children}
    </Box>
  );
};

export const StepIndicator = ({
  border = 0,
  borderColor = "#5800F7",
  children,
  onClick,
}: {
  bgGradient?: string;
  border?: number;
  color?: string;
  borderColor?: string;
  children: React.ReactNode;
  onClick?: () => void;
}) => {
  return (
    <Box
      width={"35px"}
      height={"35px"}
      borderRadius={"50%"}
      borderColor={borderColor}
      borderWidth={border}
      display={"flex"}
      alignItems={"center"}
      justifyContent={"center"}
      flexShrink={"0"}
      cursor={onClick ? "pointer" : "default"}
      onClick={onClick}
    >
      {children}
    </Box>
  );
};
