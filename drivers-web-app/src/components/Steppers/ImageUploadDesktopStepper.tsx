import { <PERSON>, <PERSON><PERSON>, Step, Stepper, StepTitle, Text } from "@chakra-ui/react";
import { FilesSection } from "components/FileUpload/FileUploadHomeVisit";
import { CheckLogo } from "components/Svgs/CheckSvg";
import { LineLogoGray } from "components/Svgs/LineLogoGray";
import { useSetup } from "context/SetupContext";
import { Dispatch, SetStateAction } from "react";
import { useTranslation } from "react-i18next";
import { BsDot } from "react-icons/bs";

import { Button as InternalButton } from "../Button";
import {
  StepIndicator,
  StepIndicatorGradient,
} from "./ImageUploadStepperIndicator";
import { ImageViewer } from "./ImageViewer";
import { UploadText } from "./UploadText";
import { useHomeVisitImages } from "./useHomeVisitImages";
import {
  HomeImageUploadNumbersSteps,
  HomeImageUploadStepsNumbers,
  HomeViewImageUploadSteps,
} from "./utils";

export const ImageUploadDesktopStepper = (props: {
  activeStep: number;
  goToNext: () => void;
  goToPrevious: () => void;
  handleSteps: (step: number) => void;
  setActiveStep: Dispatch<SetStateAction<number>>;
}) => {
  const { activeStep, goToNext, goToPrevious, handleSteps } = props;
  const { admissionRequest } = useSetup();
  const { t } = useTranslation();

  const requestId = admissionRequest?.id;
  const currentStep = activeStep;
  const stepDescription =
    HomeViewImageUploadSteps[
      currentStep as keyof typeof HomeViewImageUploadSteps
    ].description || "";
  const exampleImage =
    HomeViewImageUploadSteps[currentStep]?.exampleImage ||
    "/assets/screenshots/home.png";

  const {
    homeVisit,
    isLoading,
    setLoading,
    uploadedFiles,
    setUploadedFiles,
    setMediaFiles,
    uploadImage,
    deleteImage,
    imagesIds,
  } = useHomeVisitImages(
    requestId!,
    HomeImageUploadNumbersSteps[currentStep].stepName,
  );

  const fieldToLookForImages =
    HomeImageUploadNumbersSteps[currentStep].stepName;

  const isNextStepDisabled =
    uploadedFiles.length <
    HomeImageUploadNumbersSteps[currentStep].imagesRequired;

  const isStepCompleted = (stepNumber: number) => {
    const uploadedImagesOfStep =
      imagesIds.homeImages[HomeImageUploadNumbersSteps[stepNumber].stepName];

    return (
      uploadedImagesOfStep.length >=
      HomeImageUploadNumbersSteps[stepNumber].imagesRequired
    );
  };

  return (
    <Box px={4} maxW={"1200px"} mx="auto">
      <Box>
        <Stepper index={activeStep}>
          {Object.keys(HomeViewImageUploadSteps).map((step, index) => {
            const stepNumber = parseInt(step);
            const stepTitle =
              HomeViewImageUploadSteps[stepNumber]?.title || "Paso Desconocido";
            const isActiveStep = currentStep === stepNumber;

            return (
              <Step key={index}>
                {isActiveStep ? (
                  <StepIndicatorGradient>
                    <Text fontSize={"medium"}>{stepNumber + 1}</Text>
                  </StepIndicatorGradient>
                ) : isStepCompleted(stepNumber) ? (
                  <StepIndicator border={1} borderColor="#5800F7">
                    <CheckLogo />
                  </StepIndicator>
                ) : (
                  <StepIndicator border={1} borderColor="#00000040">
                    <Text color={"#00000040"} fontSize={"medium"}>
                      {stepNumber + 1}
                    </Text>
                  </StepIndicator>
                )}

                <Box flexShrink={"0"}>
                  <StepTitle>{stepTitle}</StepTitle>
                </Box>

                {stepNumber !== 3 && (
                  <Box flexGrow={"1"}>
                    <LineLogoGray />
                  </Box>
                )}
              </Step>
            );
          })}
        </Stepper>
      </Box>
      <Box backgroundColor={"#FAFAFA"} mt={4} borderRadius={10} px={4} py={4}>
        <Box py={4} display={"flex"} justifyContent={"space-between"}>
          <Button
            variant="outline"
            width={"20%"}
            height={10}
            fontSize={"small"}
            color={"#5800F7"}
            borderRadius={50}
            onClick={() => {
              if (activeStep === 0) {
                handleSteps(0);
                return;
              }
              if (homeVisit) {
                const fieldToLookForImages =
                  HomeImageUploadNumbersSteps[currentStep - 1].stepName;
                setMediaFiles(homeVisit, fieldToLookForImages);
              }
              goToPrevious();
            }}
            isDisabled={isLoading}
          >
            {t("GoBack")}
          </Button>

          <Button
            variant="brand"
            width={"20%"}
            height={10}
            fontSize={"small"}
            color={"white"}
            backgroundColor={"#5800F7"}
            borderRadius={50}
            _hover={{ backgroundColor: "#6210FF" }}
            onClick={() => {
              if (
                currentStep ===
                  HomeImageUploadStepsNumbers.homeImagesSurroundings &&
                !isNextStepDisabled
              ) {
                handleSteps(2);
                return;
              }
              const fieldToLookForImages =
                HomeImageUploadNumbersSteps[currentStep + 1].stepName;
              setMediaFiles(homeVisit!, fieldToLookForImages);
              goToNext();
            }}
            isDisabled={!homeVisit || isLoading || isNextStepDisabled}
          >
            {t("Folowing")}
          </Button>
        </Box>
        <Box py={2}>
          <Box display={"flex"} alignItems={"center"}>
            <BsDot size={30} />
            <Text fontSize={"medium"} width={"80%"}>
              {stepDescription.DesktopFirst}
            </Text>
          </Box>
          <Box display={"flex"} alignItems={"center"}>
            <BsDot size={30} />
            <Text fontSize={"medium"} width={"80%"}>
              {stepDescription.DesktopSecond}
            </Text>
          </Box>
        </Box>

        <ImageViewer imagePath={exampleImage}>
          <InternalButton
            my={2}
            variant="outline"
            height={9}
            borderColor={"#5800F7"}
            color={"#5800F7"}
            fontSize={{ base: "medium", md: "small" }}
            py={{ md: 4 }}
          >
            {t("ViewExample")}
          </InternalButton>
        </ImageViewer>

        <FilesSection
          requestId={requestId!}
          uploadText={<UploadText />}
          fieldToLookForImages={fieldToLookForImages}
          uploadImage={uploadImage}
          isUploading={isLoading}
          setIsUploading={setLoading}
          uploadedFiles={uploadedFiles}
          setUploadedFiles={setUploadedFiles}
          handleDelete={deleteImage}
        />
      </Box>
    </Box>
  );
};
