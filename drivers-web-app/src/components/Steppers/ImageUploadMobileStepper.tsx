import {
  Box,
  Button,
  Flex,
  Step,
  Stepper,
  StepTitle,
  Text,
} from "@chakra-ui/react";
import { FilesSection } from "components/FileUpload/FileUploadHomeVisit";
import { LineLogoGray } from "components/Svgs/LineLogoGray";
import { useSetup } from "context/SetupContext";
import { useTranslation } from "react-i18next";
import { BsDot } from "react-icons/bs";

import { Button as InternalButton } from "../Button";
import { StepIndicatorGradient } from "./ImageUploadStepperIndicator";
import { ImageViewer } from "./ImageViewer";
import { UploadText } from "./UploadText";
import { useHomeVisitImages } from "./useHomeVisitImages";
import {
  HomeImageUploadNumbersSteps,
  HomeImageUploadStepsNumbers,
  HomeViewImageUploadSteps,
} from "./utils";

export const ImageUploadMobileStepper = (props: {
  activeStep: number;
  goToNext: () => void;
  goToPrevious: () => void;
  handleSteps: (step: number) => void;
}) => {
  const { activeStep, goToNext, goToPrevious, handleSteps } = props;

  const { t } = useTranslation();
  const { admissionRequest } = useSetup();
  const requestId = admissionRequest?.id;

  const currentStep = activeStep;
  const stepTitle = HomeViewImageUploadSteps[currentStep].title || "";
  const stepDescription =
    HomeViewImageUploadSteps[
      currentStep as keyof typeof HomeViewImageUploadSteps
    ].description || "";
  const exampleImage =
    HomeViewImageUploadSteps[currentStep].exampleImage ||
    "/assets/screenshots/home.png";

  const {
    homeVisit,
    isLoading,
    setLoading,
    uploadedFiles,
    setUploadedFiles,
    setMediaFiles,
    deleteImage,
    uploadImage,
  } = useHomeVisitImages(
    requestId!,
    HomeImageUploadNumbersSteps[currentStep].stepName,
  );

  const fieldToLookForImages =
    HomeImageUploadNumbersSteps[currentStep].stepName;

  const isNextStepDisabled =
    uploadedFiles.length <
    HomeImageUploadNumbersSteps[currentStep].imagesRequired;

  return (
    <>
      <Flex>
        <Stepper index={activeStep} overflowX={"scroll"}>
          <Step key={activeStep}>
            <StepIndicatorGradient>
              <Text fontSize={"large"}>{currentStep + 1}</Text>
            </StepIndicatorGradient>

            <Box flexShrink={"0"}>
              <StepTitle>{stepTitle}</StepTitle>
            </Box>
            <Box>
              <LineLogoGray />
            </Box>
          </Step>
        </Stepper>
      </Flex>
      <Flex
        backgroundColor={"#FAFAFA"}
        mt={2}
        direction={"column"}
        borderRadius={10}
      >
        <Box py={2}>
          <Box display={"flex"} alignItems={"flex-start"}>
            <Box>
              <BsDot size={30} />
            </Box>
            <Text fontSize={"medium"}>{stepDescription.DesktopFirst}</Text>
          </Box>
          <Box display={"flex"} alignItems={"flex-start"}>
            <Box>
              <BsDot size={30} />
            </Box>
            <Text fontSize={"medium"}>{stepDescription.DesktopSecond}</Text>
          </Box>
        </Box>
        <ImageViewer imagePath={exampleImage}>
          <InternalButton
            my={2}
            variant="outline"
            height={9}
            borderColor={"#5800F7"}
            color={"#5800F7"}
            fontSize={{ base: "small", md: "medium" }}
            py={{ md: 4 }}
          >
            {t("ViewExample")}
          </InternalButton>
        </ImageViewer>
        <FilesSection
          requestId={requestId!}
          uploadText={<UploadText />}
          fieldToLookForImages={fieldToLookForImages}
          uploadImage={uploadImage}
          isUploading={isLoading}
          setIsUploading={setLoading}
          uploadedFiles={uploadedFiles}
          setUploadedFiles={setUploadedFiles}
          handleDelete={deleteImage}
        />
      </Flex>

      <Box
        py={4}
        display={"flex"}
        flexDirection={"column"}
        alignItems={"center"}
        gap={2}
      >
        <Button
          variant="brand"
          width={"100%"}
          height={10}
          fontSize={"large"}
          color={"white"}
          backgroundColor={"#5800F7"}
          borderRadius={50}
          _hover={{ backgroundColor: "#6210FF" }}
          onClick={() => {
            if (
              currentStep ===
                HomeImageUploadStepsNumbers.homeImagesSurroundings &&
              !isNextStepDisabled
            ) {
              handleSteps(2);
              return;
            }
            const fieldToLookForImages =
              HomeImageUploadNumbersSteps[currentStep + 1].stepName;
            setMediaFiles(homeVisit!, fieldToLookForImages);
            goToNext();
          }}
          isDisabled={!homeVisit || isLoading || isNextStepDisabled}
        >
          {t("Folowing")}
        </Button>
        <Button
          variant="outline"
          width={"100%"}
          height={10}
          fontSize={"large"}
          color={"#5800F7"}
          borderRadius={50}
          onClick={() => {
            if (activeStep === 0) {
              handleSteps(0);
              return;
            }
            if (homeVisit) {
              const fieldToLookForImages =
                HomeImageUploadNumbersSteps[currentStep - 1].stepName;
              setMediaFiles(homeVisit, fieldToLookForImages);
            }
            goToPrevious();
          }}
          isDisabled={isLoading}
        >
          {t("GoBack")}
        </Button>
      </Box>
    </>
  );
};
