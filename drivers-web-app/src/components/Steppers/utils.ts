export const HomeViewImageUploadSteps = {
  0: {
    title: "Fachada del domicilio 🏠  ",
    description: {
      Mobile:
        "Toma 2 fotos del frente de tu casa, mostrando puerta, ventanas y detalles importantes. Asegúrate de buena iluminación, sin sombras ni zonas oscuras.",
      DesktopFirst:
        "Toma 2 fotos de la fachada de tu casa. Asegúrate de que ambas fotos muestren bien el frente de la vivienda, incluyendo la puerta de entrada, ventanas y cualquier detalle importante de la fachada",
      DesktopSecond:
        "Evita fotos con sombras o puntos oscuros; asegúrate de que haya buena iluminación natural.",
    },
    exampleImage: "/assets/screenshots/home.png",
  },
  1: {
    title: "Foto del garaje 🚗",
    description: {
      Mobile:
        "Toma 1 foto del garaje, mostrando toda la entrada y alrededores. Asegúrate de que esté centrada y sin objetos bloqueando la vista.",
      DesktopFirst:
        "Toma 1 foto del garaje de tu casa, asegurándote de que se vea claramente toda la entrada del garaje y los alrededores",
      DesktopSecond:
        "Asegúrate de que no haya objetos bloqueando la visión del garaje, y que la foto esté centrada.",
    },
    exampleImage: "/assets/screenshots/garage.png",
  },
  2: {
    title: "Fotos de los domicilios aledaños y la calle 🌳 🚶",
    description: {
      Mobile:
        "Toma 2 fotos de la calle y los domicilios cercanos, incluyendo tu casa. Trata de capturar tanto el entorno como tu domicilio en el ángulo de la foto.",
      DesktopFirst:
        "Toma 2 fotos de la calle y los domicilios cercanos, incluyendo tu casa. Trata de capturar tanto el entorno como tu domicilio en el ángulo de la foto.",
      DesktopSecond:
        "Asegúrate de que la calle y los domicilios aledaños estén bien visibles, y que la foto no esté demasiado cerca ni demasiado lejana.",
    },
    exampleImage: "/assets/screenshots/surrounding_homes.png",
  },
};

export enum HomeImageUploadStepsNames {
  homeImagesFront = "homeImagesFront",
  homeImagesGarage = "homeImagesGarage",
  homeImagesSurroundings = "homeImagesSurroundings",
}

export enum HomeImageUploadStepsNumbers {
  homeImagesFront = 0,
  homeImagesGarage = 1,
  homeImagesSurroundings = 2,
}

export const homeImagesFrontTotalImagesRequired = 2;
export const homeImagesGarageTotalImagesRequired = 1;
export const homeImagesSurroundingsTotalImagesRequired = 2;

export const HomeImageUploadNumbersSteps = {
  0: {
    stepName: HomeImageUploadStepsNames.homeImagesFront,
    imagesRequired: homeImagesFrontTotalImagesRequired,
  },
  1: {
    stepName: HomeImageUploadStepsNames.homeImagesGarage,
    imagesRequired: homeImagesGarageTotalImagesRequired,
  },
  2: {
    stepName: HomeImageUploadStepsNames.homeImagesSurroundings,
    imagesRequired: homeImagesSurroundingsTotalImagesRequired,
  },
};

export const allowedFileFormats =
  "image/png,image/jpeg,image/jpg,application/pdf,image/gif,image/tiff,image/bmp,image/webp";
