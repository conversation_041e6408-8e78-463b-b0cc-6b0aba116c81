import { ChevronLeftIcon, ChevronRightIcon } from "@chakra-ui/icons";
import { Box, Text } from "@chakra-ui/react";
import { DateTime } from "luxon";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { BiWorld } from "react-icons/bi";
import { DayInfo, DAYS, getDaysInMonth, MONTHS } from "utils";

import { Button } from "./Button";
interface ISchedulingCalendar {
  handleDateSelect: (dayInfo: DayInfo) => void;
}

export const SchedulingCalendar = (props: ISchedulingCalendar) => {
  const { handleDateSelect } = props;

  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());

  const handleNextMonth = (): void => {
    setCurrentMonth((prevMonth) => {
      const newMonth = DateTime.fromJSDate(prevMonth)
        .plus({ months: 1 })
        .toJSDate();
      return newMonth;
    });
  };

  const handlePreviousMonth = (): void => {
    setCurrentMonth((prevMonth) => {
      const newMonth = DateTime.fromJSDate(prevMonth)
        .minus({ months: 1 })
        .toJSDate();
      return newMonth;
    });
  };

  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const { t } = useTranslation();

  return (
    <Box px={4}>
      <Box py={4}>
        <Text fontSize={"large"} fontWeight={"Bold"}>
          {t("SelectDate")}
        </Text>
      </Box>

      <Box px={2}>
        <Box
          display={"flex"}
          justifyContent={"space-between"}
          alignItems={"center"}
          mb={4}
        >
          <Button
            variant="calendarBtn-clicker"
            onClick={handlePreviousMonth}
            h={10}
            w={10}
          >
            <ChevronLeftIcon h={8} w={8} />
          </Button>
          <h2 className="text-lg font-semibold">
            {MONTHS[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </h2>
          <Button
            variant="calendarBtn-clicker"
            onClick={handleNextMonth}
            h={10}
            w={10}
          >
            <ChevronRightIcon h={8} w={8} />
          </Button>
        </Box>
        <Box
          display={"grid"}
          gridTemplateColumns="repeat(7, 1fr)"
          gap={1}
          textAlign={"center"}
        >
          {DAYS.map((day) => (
            <div key={day}>{day}</div>
          ))}
        </Box>
        <Box display={"grid"} gridTemplateColumns="repeat(7, 1fr)" gap={1}>
          {getDaysInMonth(currentMonth).map((day, index) => (
            <Button
              key={index}
              variant={day.disabled ? "ghost" : "calendarBtn-primary"}
              disabled={day.disabled}
              height={{ base: "12" }}
              border={day.isToday ? "2px solid" : "none"}
              borderColor={day.isToday ? "primary" : "transparent"}
              visibility={!day.date ? "hidden" : "visible"}
              onClick={() => handleDateSelect(day)}
            >
              {day.date?.getDate()}
            </Button>
          ))}
        </Box>
      </Box>

      <Box py={4}>
        <Text fontSize={"large"} fontWeight={"Bold"}>
          {t("TimeZone")}
        </Text>
        <Box
          px={2}
          py={2}
          display={"flex"}
          justifyItems={"center"}
          alignItems={"center"}
        >
          <BiWorld height={15} width={15} />
          <Text display="inline-block" p={2}>
            {timezone}
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
