import {
  Button as ChakraButton,
  ButtonProps as ChakraButtonProps,
} from "@chakra-ui/react";

interface ButtonProps extends ChakraButtonProps {
  variant?:
    | "brand"
    | "outline"
    | "ghost"
    | "calendarBtn-primary"
    | "calendarBtn-clicker";
}

export const Button = ({ children, ...props }: ButtonProps) => {
  const { variant } = props;
  let buttonProps: ChakraButtonProps = {
    size: "md",
    variant: "solid",
    color: "white",
    height: "12",
  };

  if (variant === "brand") {
    buttonProps = {
      ...buttonProps,
      bgGradient: "linear(to-r, #6210FF, #A74DF9, #6210FF)",
      transition: "0.5s",
      backgroundSize: "200% auto",
      borderRadius: "25px",
      _hover: {
        bgGradient: "linear(to-r, #6210FF, #A74DF9, #6210FF)",
        backgroundPosition: "right center",
        color: "#fff",
        textDecoration: "none",
        _focus: {
          boxShadow:
            "0 0 1px 2px #fff, 0 1px 1px rgba(0, 0, 0, .08), 0 2px 6px rgba(0, 0, 0, .16)",
        },
      },
      _disabled: {
        bg: "gray.200",
        color: "gray.500",
        cursor: "not-allowed",
      },
    };
  }

  if (variant === "outline") {
    buttonProps = {
      ...buttonProps,
      bg: "white",
      border: "1px solid #6210FF",
      borderRadius: "25px",
      color: "#6210FF",
      transition: "0.5s",
      backgroundSize: "200% auto",
      _hover: {
        bg: "purple.50",
        backgroundPosition: "right center",
        color: "#6210FF",
        textDecoration: "none",
        _focus: {
          boxShadow:
            "0 0 1px 2px #fff, 0 1px 1px rgba(0, 0, 0, .08), 0 2px 6px rgba(0, 0, 0, .16)",
        },
      },
    };
  }

  if (variant === "ghost") {
    buttonProps = {
      ...buttonProps,
      bg: "transparent",
      color: "#1A1A1A9C",
      borderRadius: "50px",
      border: "1px solid #6210FF",
      transition: "0.5s",
      backgroundSize: "200% auto",
      _hover: {
        bg: "purple.50",
        backgroundPosition: "right center",
        color: "#6210FF",
        textDecoration: "none",
        _focus: {
          boxShadow:
            "0 0 1px 2px #fff, 0 1px 1px rgba(0, 0, 0, .08), 0 2px 6px rgba(0, 0, 0, .16)",
        },
        _disabled: {
          color: "gray.500",
        },
      },
    };
  }

  if (variant === "calendarBtn-primary") {
    buttonProps = {
      ...buttonProps,
      background: "#742BFA1F",
      transition: "0.5s",
      backgroundSize: "200% auto",
      borderRadius: "50px",
      color: "#6210FF",
      _hover: {
        bgGradient: "linear(to-r, #6210FF, #A74DF9, #6210FF)",
        backgroundPosition: "right center",
        color: "#fff",
        textDecoration: "none",
        _focus: {
          boxShadow:
            "0 0 1px 2px #fff, 0 1px 1px rgba(0, 0, 0, .08), 0 2px 6px rgba(0, 0, 0, .16)",
        },
      },
      _disabled: {
        bg: "gray.200",
        color: "gray.500",
        cursor: "not-allowed",
      },
    };
  }

  if (variant === "calendarBtn-clicker") {
    buttonProps = {
      ...buttonProps,
      transition: "0.5s",
      bgGradient: "#F9F9F9",
      backgroundSize: "200% auto",
      borderRadius: "25px",
      color: "gray.500",
      _hover: {
        background: "#742BFA1F",
        backgroundPosition: "right center",
        color: "#6210FF",
        textDecoration: "none",
        _focus: {
          boxShadow:
            "0 0 1px 2px #fff, 0 1px 1px rgba(0, 0, 0, .08), 0 2px 6px rgba(0, 0, 0, .16)",
        },
      },
    };
  }

  return (
    <ChakraButton {...buttonProps} {...props}>
      {children}
    </ChakraButton>
  );
};
