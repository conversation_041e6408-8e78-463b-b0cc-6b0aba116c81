import { Box, Text } from "@chakra-ui/react";
import { ReactNode } from "react";

interface HeaderProps {
  title?: string;
  subtitle?: string | ReactNode;
  note?: string;
}

export const Header = ({ title, subtitle, note }: HeaderProps) => {
  const TitleMarkup = title ? (
    <Text color="black" fontSize="3xl" fontWeight="semibold" textAlign="center">
      {title}
    </Text>
  ) : null;
  const SubtitleMarkup = subtitle ? (
    <Text mt="4" fontSize="lg" textAlign="center">
      {subtitle}
    </Text>
  ) : null;

  const NoteMarkup = note ? (
    <Text mt="2" fontSize="md" textAlign="center">
      {note}
    </Text>
  ) : null;

  return (
    <Box>
      {TitleMarkup}
      {SubtitleMarkup}
      {NoteMarkup}
    </Box>
  );
};
