import { Box, Container, Flex } from "@chakra-ui/react";
import { useEffect, useState } from "react";

interface LayoutProps {
  children: React.ReactNode;
  header?: React.ReactNode;
  navigation?: React.ReactNode;
}

export const Layout = ({ children, header, navigation }: LayoutProps) => {
  const [mainAreaHeight, setMainAreaHeight] = useState(0);

  useEffect(() => {
    const updateMainAreaHeight = () => {
      const navigationElement = document.getElementById("navigation");

      if (navigationElement) {
        const navigationHeight = navigationElement.offsetHeight;

        const mainAreaHeight = window.innerHeight - navigationHeight;

        setMainAreaHeight(mainAreaHeight);
      }
    };

    window.addEventListener("resize", updateMainAreaHeight);
    updateMainAreaHeight();

    return () => {
      window.removeEventListener("resize", updateMainAreaHeight);
    };
  }, [navigation]);

  const height = navigation ? `${mainAreaHeight}px` : "100%";

  return (
    <Box mx="auto" display="flex" maxW="lg" h="100%" className="font-sans">
    <Container minH="100vh" width="100%">
      {header && (
        <Box id="header" w="100%" mx="auto" mt={8} px={4} mb={12}>
          {header}
        </Box>
      )}
      <Box px={4} mx="auto" w="100%" h={height}>
        {children}
      </Box>
      {navigation && (
        <Flex
          id="navigation"
          pb={4}
          position="fixed"
          bottom={0}
          w="92%"
          mx="auto"
          maxW="md"
          px={4}
          justifyContent="center"
          alignItems="center"
          blur={4}
        >
          {navigation}
        </Flex>
      )}
    </Container>
    </Box>
  );
};
