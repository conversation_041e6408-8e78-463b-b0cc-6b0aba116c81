export const CarCancelSvg = () => {
  return (
    <svg
      width="321"
      height="361"
      viewBox="0 0 321 361"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_54_1533)">
        <path
          d="M181.695 277.615C258.357 277.615 320.503 215.469 320.503 138.808C320.503 62.1463 258.357 0 181.695 0C105.034 0 42.8877 62.1463 42.8877 138.808C42.8877 215.469 105.034 277.615 181.695 277.615Z"
          fill="#F2F2F2"
        />
        <path
          d="M272.404 62.0746H18.1275C17.5823 62.063 17.1497 61.6115 17.1614 61.0663C17.1727 60.5374 17.5987 60.1114 18.1275 60.1001H272.404C272.949 60.1118 273.382 60.5632 273.37 61.1085C273.359 61.6373 272.933 62.0633 272.404 62.0746Z"
          fill="#CACACA"
        />
        <path
          d="M185.747 95.3472H115.455C110.175 95.3472 105.895 99.6272 105.895 104.907V104.907C105.895 110.187 110.175 114.467 115.455 114.467H185.747C191.027 114.467 195.307 110.187 195.307 104.907C195.307 99.6272 191.027 95.3472 185.747 95.3472Z"
          fill="white"
        />
        <path
          d="M70.0456 133.586H231.156C236.436 133.586 240.716 137.866 240.716 143.146C240.716 148.426 236.436 152.706 231.156 152.706H70.0456C64.7659 152.706 60.4858 148.426 60.4858 143.146C60.4858 137.866 64.7659 133.586 70.0456 133.586Z"
          fill="white"
        />
        <path
          d="M70.0456 171.825H231.156C236.436 171.825 240.716 176.106 240.716 181.385C240.716 186.665 236.436 190.945 231.156 190.945H70.0456C64.7659 190.945 60.4858 186.665 60.4858 181.385C60.4858 176.106 64.7659 171.825 70.0456 171.825Z"
          fill="white"
        />
        <path
          d="M50.0807 359.457L49.7015 359.42C36.8369 358.093 25.2073 351.174 17.9023 340.501C15.9014 337.528 14.2829 334.314 13.085 330.936L12.9699 330.613L13.3108 330.586C17.3804 330.258 21.5612 329.568 23.4185 329.239L11.4424 325.182L11.3683 324.826C10.6583 321.49 12.0493 318.058 14.8814 316.158C17.7212 314.166 21.487 314.102 24.3925 315.997C25.6964 316.83 27.0603 317.655 28.3791 318.453C32.9112 321.194 37.5977 324.029 41.1113 327.908C46.4393 333.907 48.7747 341.99 47.4669 349.907L50.0807 359.457Z"
          fill="#F2F2F2"
        />
        <path
          d="M93.6135 353.501L99.5155 353.501L102.324 330.733L93.6118 330.733L93.6135 353.501Z"
          fill="#A0616A"
        />
        <path
          d="M93.4051 359.659L111.556 359.658V359.428C111.556 355.527 108.393 352.364 104.491 352.364L101.175 349.848L94.9893 352.364L93.4048 352.364L93.4051 359.659Z"
          fill="#2F2E41"
        />
        <path
          d="M46.3101 337.153L51.0312 340.694L66.941 324.168L59.9722 318.94L46.3101 337.153Z"
          fill="#A0616A"
        />
        <path
          d="M42.4482 341.953L56.9679 352.846L57.1056 352.662C59.4469 349.541 58.815 345.113 55.6942 342.771L54.5512 338.769L48.0932 337.069L46.8257 336.118L42.4482 341.953Z"
          fill="#2F2E41"
        />
        <path
          d="M65.931 246.658C65.931 246.658 66.2568 255.547 66.6678 262.674C66.7352 263.843 63.9965 265.113 64.0674 266.385C64.1145 267.229 64.6292 268.049 64.6771 268.929C64.7275 269.857 64.0168 270.679 64.0674 271.639C64.1169 272.578 64.9277 273.653 64.9766 274.614C65.503 284.947 67.411 297.318 65.5004 300.776C64.5588 302.481 52.5566 326.092 52.5566 326.092C52.5566 326.092 59.2438 336.123 61.1544 332.779C63.678 328.363 83.1266 307.702 83.1266 303.757C83.1266 299.816 87.6261 270.283 87.6261 270.283L90.7635 287.76L92.202 291.223L91.75 293.256L92.6797 296L92.7299 298.715L93.635 303.757C93.635 303.757 90.9124 343.039 92.4457 344.133C93.9837 345.232 101.43 347.204 102.309 345.232C103.183 343.259 108.873 304.235 108.873 304.235C108.873 304.235 109.79 286.351 110.784 269.729C110.841 268.764 111.519 267.598 111.572 266.648C111.634 265.544 111.204 264.111 111.261 263.042C111.323 261.814 111.868 260.949 111.92 259.774C112.326 250.694 110.047 239.637 109.321 238.548C107.129 235.261 105.156 232.634 105.156 232.634C105.156 232.634 75.6263 222.932 67.7354 232.791L65.931 246.658Z"
          fill="#2F2E41"
        />
        <path
          d="M95.3828 155.259L83.6765 152.918L78.2136 160.332C68.0202 170.355 67.8667 179.201 69.6863 193.38V213.28L68.3693 226.621C68.3693 226.621 63.7069 236.373 68.5157 238.644C73.3245 240.915 104.911 240.721 107.927 239.815C110.942 238.908 108.328 237.795 107.537 233.571C106.049 225.637 107.044 229.464 107.146 226.938C108.614 190.698 105.195 175.82 104.848 171.91L98.8947 161.112L95.3828 155.259Z"
          fill="#3F3D56"
        />
        <path
          d="M157.082 184.478C155.862 186.79 152.999 187.675 150.687 186.455C150.441 186.325 150.206 186.173 149.986 186.002L124.706 202.054L124.778 193.314L149.337 179.063C151.086 177.101 154.094 176.929 156.055 178.679C157.696 180.142 158.121 182.54 157.082 184.478Z"
          fill="#A0616A"
        />
        <path
          d="M86.1397 165.38L83.2615 165.075C80.6021 164.8 77.9929 165.939 76.387 168.076C75.7719 168.887 75.3301 169.815 75.0891 170.804L75.0874 170.812C74.3655 173.788 75.4978 176.906 77.9609 178.726L87.9016 186.055C94.8778 195.481 107.989 201.735 123.368 206.816L147.273 191.969L138.817 181.729L122.421 190.516L98.0485 171.65L98.0345 171.638L88.7583 165.662L86.1397 165.38Z"
          fill="#3F3D56"
        />
        <path
          d="M91.4275 149.511C98.9668 149.511 105.079 143.399 105.079 135.86C105.079 128.32 98.9668 122.208 91.4275 122.208C83.8882 122.208 77.7764 128.32 77.7764 135.86C77.7764 143.399 83.8882 149.511 91.4275 149.511Z"
          fill="#A0616A"
        />
        <path
          d="M91.7426 149.514C91.6326 149.517 91.5223 149.52 91.4121 149.523C91.3649 149.645 91.3141 149.766 91.2554 149.885L91.7426 149.514Z"
          fill="#2F2E41"
        />
        <path
          d="M95.4893 136.238C95.5107 136.373 95.5434 136.505 95.5872 136.634C95.5691 136.499 95.5363 136.366 95.4893 136.238Z"
          fill="#2F2E41"
        />
        <path
          d="M105.253 122.792C104.691 124.537 104.189 122.324 102.341 122.722C100.117 123.201 97.5296 123.037 95.7087 121.674C92.9955 119.683 89.4794 119.147 86.2964 120.239C83.1805 121.341 77.9605 122.119 77.1487 125.322C76.8665 126.436 76.7544 127.644 76.0703 128.567C75.4723 129.374 74.5283 129.834 73.739 130.456C71.0736 132.553 73.1108 138.511 74.3723 141.659C75.6338 144.807 78.5275 147.1 81.709 148.275C84.7872 149.412 88.1282 149.612 91.412 149.523C91.9832 148.041 91.7364 146.33 91.2652 144.795C90.755 143.132 89.9995 141.528 89.7864 139.802C89.5733 138.076 90.0413 136.116 91.4884 135.152C92.8185 134.265 94.983 134.805 95.489 136.238C95.1938 134.447 97.0087 132.713 98.88 132.408C100.889 132.081 102.898 132.806 104.902 133.167C106.905 133.528 106.194 125.094 105.253 122.792Z"
          fill="#2F2E41"
        />
        <path
          d="M181.6 158.441C182.873 143.065 171.439 129.568 156.063 128.296C140.687 127.023 127.19 138.456 125.917 153.833C124.645 169.209 136.078 182.706 151.455 183.978C166.831 185.251 180.327 173.818 181.6 158.441Z"
          fill="#6210FF"
        />
        <path
          d="M161.097 144.497L153.759 151.835L146.42 144.497C145.232 143.309 143.306 143.309 142.118 144.497C140.931 145.685 140.931 147.611 142.118 148.799L149.457 156.137L142.118 163.475C140.932 164.665 140.933 166.59 142.123 167.777C143.31 168.963 145.233 168.963 146.42 167.777L153.759 160.439L161.097 167.777C162.286 168.964 164.212 168.962 165.399 167.773C166.584 166.586 166.584 164.663 165.399 163.475L158.061 156.137L165.399 148.799C166.587 147.611 166.587 145.685 165.399 144.497C164.211 143.309 162.285 143.309 161.097 144.497Z"
          fill="white"
        />
        <path
          d="M142.75 132.656C143.573 135.137 142.23 137.816 139.749 138.639C139.485 138.727 139.213 138.791 138.937 138.831L133.018 168.187L126.762 162.084L133.48 134.496C133.276 131.876 135.234 129.586 137.854 129.382C140.047 129.211 142.071 130.565 142.75 132.656Z"
          fill="#A0616A"
        />
        <path
          d="M79.8554 170.627L77.6428 172.493C75.6026 174.221 74.6181 176.892 75.0489 179.531C75.208 180.536 75.5723 181.498 76.1188 182.356L76.1233 182.363C77.7709 184.944 80.8052 186.286 83.8232 185.768L95.9939 183.668C107.626 185.16 121.215 180.029 135.528 172.449L141.365 144.921L128.122 143.933L123.111 161.848L92.6239 166.374L92.606 166.376L81.8714 168.932L79.8554 170.627Z"
          fill="#3F3D56"
        />
        <path
          d="M149.156 360.13L0.648591 360.298C0.289226 360.297 -0.00116053 360.004 3.48728e-06 359.645C0.00115658 359.287 0.29086 358.997 0.648591 358.996L149.156 358.828C149.516 358.83 149.806 359.122 149.805 359.481C149.804 359.839 149.514 360.129 149.156 360.13Z"
          fill="#CACACA"
        />
        <path
          d="M31.0277 49.5682C33.58 49.5682 35.6491 47.453 35.6491 44.8438C35.6491 42.2346 33.58 40.1194 31.0277 40.1194C28.4753 40.1194 26.4062 42.2346 26.4062 44.8438C26.4062 47.453 28.4753 49.5682 31.0277 49.5682Z"
          fill="#3F3D56"
        />
        <path
          d="M46.993 49.5682C49.5454 49.5682 51.6144 47.453 51.6144 44.8438C51.6144 42.2346 49.5454 40.1194 46.993 40.1194C44.4407 40.1194 42.3716 42.2346 42.3716 44.8438C42.3716 47.453 44.4407 49.5682 46.993 49.5682Z"
          fill="#3F3D56"
        />
        <path
          d="M62.9579 49.5682C65.5102 49.5682 67.5793 47.453 67.5793 44.8438C67.5793 42.2346 65.5102 40.1194 62.9579 40.1194C60.4055 40.1194 58.3364 42.2346 58.3364 44.8438C58.3364 47.453 60.4055 49.5682 62.9579 49.5682Z"
          fill="#3F3D56"
        />
        <path
          d="M81.1619 48.5779C81.0199 48.5779 80.8783 48.5229 80.7713 48.4135L77.653 45.2258C77.4451 45.0134 77.4451 44.674 77.653 44.4616L80.7713 41.2742C80.9829 41.0586 81.3292 41.0551 81.544 41.2657C81.7599 41.4767 81.7636 41.8228 81.5526 42.0384L78.8081 44.8437L81.5526 47.6493C81.7636 47.8649 81.7599 48.211 81.544 48.422C81.4378 48.5261 81.2999 48.5779 81.1619 48.5779Z"
          fill="#3F3D56"
        />
        <path
          d="M86.402 48.5778C86.264 48.5778 86.1261 48.526 86.0199 48.422C85.804 48.2109 85.8003 47.8651 86.0113 47.6492L88.7555 44.8436L86.0113 42.0384C85.8003 41.8227 85.804 41.4767 86.0199 41.2656C86.2352 41.0542 86.5816 41.0583 86.7926 41.2741L89.9107 44.4615C90.1185 44.6739 90.1185 45.0133 89.9107 45.2258L86.7926 48.4134C86.6856 48.5228 86.544 48.5778 86.402 48.5778Z"
          fill="#3F3D56"
        />
        <path
          d="M249.534 40.6665H243.735C243.074 40.6665 242.539 41.202 242.539 41.8633V47.667C242.539 48.3283 243.074 48.8638 243.735 48.8638H249.534C250.195 48.8638 250.736 48.3283 250.736 47.667V41.8633C250.736 41.202 250.195 40.6665 249.534 40.6665Z"
          fill="#3F3D56"
        />
        <path
          d="M235.325 40.6665H229.527C228.866 40.6665 228.33 41.202 228.33 41.8633V47.667C228.33 48.3283 228.866 48.8638 229.527 48.8638H235.325C235.986 48.8638 236.527 48.3283 236.527 47.667V41.8633C236.527 41.202 235.986 40.6665 235.325 40.6665Z"
          fill="#3F3D56"
        />
        <path
          d="M262.923 40.9397H257.125C256.463 40.9397 255.928 41.4752 255.928 42.1365V47.9402C255.928 48.6015 256.463 49.137 257.125 49.137H262.923C263.584 49.137 264.125 48.6015 264.125 47.9402V42.1365C264.125 41.4752 263.584 40.9397 262.923 40.9397Z"
          fill="#3F3D56"
        />
        <path
          d="M175.527 43.1475H129.18C128.371 43.1475 127.721 43.8033 127.721 44.6066C127.721 45.41 128.371 46.0657 129.18 46.0657H175.527C176.331 46.0657 176.987 45.4099 176.987 44.6066C176.987 43.8033 176.331 43.1475 175.527 43.1475Z"
          fill="#3F3D56"
        />
      </g>
      <defs>
        <clipPath id="clip0_54_1533">
          <rect width="320.503" height="360.298" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
