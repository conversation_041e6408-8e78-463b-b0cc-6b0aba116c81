import { useSetup } from "context/SetupContext";
import { AdmissionRequest } from "core/domain/entities";
import {
  AdmissionRequestAdditionalDocumentType,
  AdmissionRequestDocumentType,
  AdmissionRequestDocumentTypeUS,
  RequestDocumentStatus,
} from "core/domain/enums";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

export interface setupNavigateProps {
  admissionRequest?: Partial<AdmissionRequest>;
}

export const useSetupNavigate = () => {
  const navigate = useNavigate();
  const { admissionRequest, skipScreens } = useSetup();
  const [screenChange, setScreenChange] = useState(false);
  const { isCountryUSA } = useSetup();
  const documentTypes = isCountryUSA
    ? Object.values(AdmissionRequestDocumentTypeUS)
    : Object.values(AdmissionRequestDocumentType);
  const optionalDocuments = isCountryUSA
    ? []
    : Object.values(AdmissionRequestAdditionalDocumentType);

  /// We assuming that if the solidarity INE card front is available then we also have
  // the INE card back along with the required details that's why we are not checking
  // solidarity INE back route
  const checkPendingDocument = (
    type:
      | AdmissionRequestDocumentType
      | AdmissionRequestDocumentTypeUS
      | AdmissionRequestAdditionalDocumentType,
  ) => {
    if (
      type ===
      AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back
    ) {
      return false;
    }
    return admissionRequest?.documentsAnalysis.documents.find(
      (doc) =>
        doc.type === type && doc.status === RequestDocumentStatus.pending,
    );
  };

  const navigateTo = (): string => {
    for (const docType of documentTypes) {
      const pendingDocument = checkPendingDocument(docType);
      if (pendingDocument) {
        // Special case for bank statements
        return docType.startsWith("bank_statement_month")
          ? "/documents/bank_statements"
          : `/documents/${docType}`;
      }
    }

    for (const docType of optionalDocuments) {
      const pendingDocument = checkPendingDocument(docType);
      if (pendingDocument && !skipScreens.includes(docType)) {
        return `/documents/${docType}`;
      }
    }

    return "/documents-success";
  };

  useEffect(() => {
    if (screenChange || skipScreens.length > 0) {
      navigate(navigateTo());
    }
  }, [screenChange, skipScreens]);

  const nextScreen = () => {
    setScreenChange(true);
  };

  const backScreen = () => {
    navigate(-1);
  };

  return {
    nextScreen,
    backScreen,
  };
};
