import { Box, Flex, Image, Text, VStack } from "@chakra-ui/react";
import { Button, Layout } from "components";
import { useSetup } from "context/SetupContext";
import { GigPlatform } from "core/domain/enums";
import { t } from "i18next";
import { platforms, platformsUS } from "misc/data";
import { API_URL } from "misc/settings";
import { useEffect, useState } from "react";
import { Trans } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { PlatformCard } from "types";

export const ConnectPlatformSuccessView = () => {
  const { slug } = useParams() as { slug: string };
  const [isRetrieving, setIsRetrieving] = useState(false);
  const navigate = useNavigate();
  const { retrieveAdmissionRequest, admissionRequest, isCountryUSA } =
    useSetup();
  const platformList = isCountryUSA ? platformsUS : platforms;
  const platform = platformList.find((platform) => platform.slug === slug);
  const name = platform?.name ?? "";

  const accounts = admissionRequest?.palenca.accounts;

  const handleConnectMore = () => {
    navigate("/platforms");
  };

  const handleContinue = async () => {
    const getEarnings = await fetch(
      `${API_URL}/ocr/get-earnings-analysis/${admissionRequest?.id}`,
    );
    if (getEarnings.ok) {
      navigate("/personal-data");
    } else {
      navigate("/platforms");
    }
  };

  async function getAdmissionRequest() {
    setIsRetrieving(true);
    await retrieveAdmissionRequest();
    setIsRetrieving(false);
  }

  useEffect(() => {
    getAdmissionRequest();
  }, []);

  const cardsData: PlatformCard[] = platformList.map((platform) => ({
    slug: platform.slug as GigPlatform,
    name: platform.name,
    logo: platform.logo,
    connected: false,
  }));

  // We mark as connected if the account is already in the admission request, otherwise we mark as not connected
  // We don't care the status, since it can be pending to retrieval
  const cardsWithStatus = accounts?.map((account) => {
    const card = cardsData.find((card) => card.slug === account.platform);
    return {
      ...card,
      connected: !!account,
    };
  });

  const navigationMarkup = (
    <Flex w="full" flexDirection="column" gap={6}>
      <Button variant="outline" w="full" onClick={handleConnectMore}>
        {t("ConnectPlatForm")}
      </Button>
      <Button variant="brand" w="full" onClick={handleContinue}>
        {t("Continue")}
      </Button>
    </Flex>
  );

  return (
    <Layout navigation={navigationMarkup}>
      <Flex flexDirection="column" align="center" justify="center" h="full">
        <VStack gap={4}>
          <Image
            src="/assets/icons/check_circle_icon.svg"
            alt="Icono Círculo Verificación"
            boxSize={28}
          />
          <Trans i18nKey="AccountCreatedSuccessfully" values={{ name }} />
          <Text mt="4" fontSize="md" textAlign="center">
            {t("ConnectedPlatforms")}
          </Text>
          <Flex wrap="wrap">
            {!isRetrieving &&
              cardsWithStatus &&
              cardsWithStatus.map((card, index) => (
                <Box
                  key={index}
                  p={2}
                  maxW="sm"
                  overflow="hidden"
                  m={2}
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  textAlign="center"
                >
                  <Image
                    boxSize={{ base: 12, md: 14 }}
                    objectFit="cover"
                    src={card.logo}
                    alt={`Image de ${card.name}`}
                    borderRadius="full"
                  />
                </Box>
              ))}
          </Flex>
        </VStack>
      </Flex>
    </Layout>
  );
};
