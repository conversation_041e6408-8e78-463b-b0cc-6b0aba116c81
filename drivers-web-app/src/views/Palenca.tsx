import { Box } from "@chakra-ui/react";
import { Layout } from "components";
import { useSetup } from "context/SetupContext";
import { PALENCA_WIDGET_ID, PLANECA_IS_SANDBOX } from "misc/settings";
import { useEffect } from "react";

export const PalencaView = () => {
  const setup = useSetup();
  const { admissionRequest } = setup;
  useEffect(() => {
    if (admissionRequest && admissionRequest?.id) {
      window.location.href = `https://connect.palenca.com/?widget_id=${PALENCA_WIDGET_ID}&is_sandbox=${PLANECA_IS_SANDBOX}&external_id=${admissionRequest?.id}`;
    }
  }, [admissionRequest]);
  return (
    <Layout>
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        height="100vh"
      >
        <p>
          Serás redirigido a Palenca, un socio de confianza de One Car Now para
          recopilar datos de la plataforma.
        </p>
      </Box>
    </Layout>
  );
};
