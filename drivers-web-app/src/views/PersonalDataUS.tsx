import {
  Box,
  Checkbox as ChakraCheckbox,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Select,
  SimpleGrid,
  Text,
  useToast,
} from "@chakra-ui/react";
import { <PERSON><PERSON>, Header, Layout } from "components";
import { useSetup } from "context/SetupContext";
import {
  AdmissionRequestController,
  IRequestPersonalData,
} from "core/controllers/admissionRequest";
import { Field, Form, Formik } from "formik";
import { useSetupNavigate } from "hooks/useSetupNavigate";
import { personalDataValidationSchemaUS } from "misc/validationSchemas";
import { ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { US_STATES_CITIES, US_STATES_OPTIONS, USSTATES } from "utils";

export const PersonalDataViewUS = () => {
  const toast = useToast();
  const { nextScreen } = useSetupNavigate();
  const { t } = useTranslation();
  const { admissionRequest, setAdmissionRequest } = useSetup();
  const admissionRequestController = new AdmissionRequestController();

  const personalData = admissionRequest?.personalData;
  const headingMarkup = (
    <Header
      title={t("PersonalDataTitle")}
      subtitle={t("PersonalDataSubtitle")}
    />
  );

  const initialValues = {
    birthdate: "",
    postalCode: "",
    city: "",
    state: "",
    street: "",
    streetNumber: "",
    department: "",
    ssn: "",
    rideShareTotalRides: null,
    avgEarningPerWeek: null,
    privacyPolicy: false,
    ocnBackgroundAndCreditCheckForApplication: false,
  };

  if (personalData) {
    initialValues.birthdate = personalData.birthdate;
    initialValues.postalCode = personalData.postalCode;
    initialValues.city = personalData.city;
    initialValues.state = personalData.state;
    initialValues.street = personalData.street;
    initialValues.streetNumber = personalData.streetNumber;
    initialValues.department = personalData.department;
    initialValues.ssn = personalData.ssn;
    initialValues.rideShareTotalRides = personalData.rideShareTotalRides;
    initialValues.avgEarningPerWeek = personalData.avgEarningPerWeek;
    initialValues.privacyPolicy = personalData.privacyPolicy;
    initialValues.ocnBackgroundAndCreditCheckForApplication =
      personalData.ocnBackgroundAndCreditCheckForApplication;
  }

  const handleSubmit = async (values) => {
    // Create a payload object containing only the fields that have changed
    const payload: Partial<IRequestPersonalData> = Object.keys(values).reduce(
      (acc, key) => {
        // Check if the current field value is different from the initial value
        if (values[key] !== initialValues[key]) {
          // If different, add it to the payload
          acc[key] = values[key];
        }
        return acc;
      },
      {},
    );
    if (admissionRequest) {
      const [err, data] = await admissionRequestController.updatePersonalData(
        admissionRequest?.id,
        payload,
      );
      if (!err) {
        setAdmissionRequest(data);
        nextScreen();
      } else {
        toast({
          title: "Error",
          description: "Verifica los campos e intenta de nuevo",
          status: "error",
        });
      }
    }
  };

  const navigationMarkup = ({ isSubmitting, isValid, dirty }) => {
    const initialDisabled = dirty && !isValid;
    return (
      <Flex w="full">
        <Button
          variant="brand"
          w="full"
          type="submit"
          isDisabled={isSubmitting || initialDisabled}
        >
          {t("Continue")}
        </Button>
      </Flex>
    );
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={personalDataValidationSchemaUS}
      onSubmit={handleSubmit}
    >
      {({ errors, setFieldValue, isSubmitting, isValid, dirty, touched }) => {
        return (
          <Form style={{ width: "100%" }}>
            <Layout
              header={headingMarkup}
              navigation={navigationMarkup({
                isSubmitting,
                isValid,
                dirty,
              })}
            >
              <SimpleGrid gap={4} pb={24}>
                <Box>
                  <FormControl
                    isInvalid={
                      touched.birthdate && errors.birthdate ? true : false
                    }
                  >
                    <FormLabel>{t("Birthdate")}</FormLabel>
                    <Field name="birthdate" as={Input} type="date" />
                    <FormErrorMessage>{errors.birthdate}</FormErrorMessage>
                  </FormControl>
                </Box>
                <Box>
                  <FormControl
                    isInvalid={touched.ssn && errors.ssn ? true : false}
                  >
                    <FormLabel>{t("SSN")}</FormLabel>
                    <Field
                      name="ssn"
                      as={Input}
                      onChange={(e) => {
                        const upperCaseValue = e.target.value;
                        setFieldValue("ssn", upperCaseValue);
                      }}
                    />
                    <FormErrorMessage>{errors.ssn}</FormErrorMessage>
                  </FormControl>
                </Box>

                <Box>
                  <FormControl
                    isInvalid={touched.street && errors.street ? true : false}
                  >
                    <FormLabel>{t("Street")}</FormLabel>
                    <Field name="street" as={Input} />
                    <FormErrorMessage>{errors.street}</FormErrorMessage>
                  </FormControl>
                </Box>

                <Box>
                  <FormControl
                    isInvalid={
                      touched.streetNumber && errors.streetNumber ? true : false
                    }
                  >
                    <FormLabel>{t("Address1")}</FormLabel>
                    <Field name="streetNumber" as={Input} />
                    <FormErrorMessage>{errors.streetNumber}</FormErrorMessage>
                  </FormControl>
                </Box>

                <Box>
                  <FormControl
                    isInvalid={
                      touched.postalCode && errors.postalCode ? true : false
                    }
                  >
                    <FormLabel>{t("ZipCode")}</FormLabel>
                    <Field name="postalCode" as={Input} />
                    <FormErrorMessage>{errors.postalCode}</FormErrorMessage>
                  </FormControl>
                </Box>
                <Box>
                  <FormControl
                    isInvalid={touched.state && errors.state ? true : false}
                  >
                    <FormLabel>{t("State")}</FormLabel>
                    <Field name="state" as={Select} placeholder="State">
                      {US_STATES_OPTIONS.map((state, index) => (
                        <option key={index} value={state.value}>
                          {state.label}
                        </option>
                      ))}
                    </Field>
                    <FormErrorMessage>{errors.state}</FormErrorMessage>
                  </FormControl>
                </Box>

                <Box>
                  <FormControl
                    isInvalid={touched.city && errors.city ? true : false}
                  >
                    <FormLabel>{t("City")}</FormLabel>
                    <Field name="city" as={Select} placeholder="City">
                      {US_STATES_CITIES[USSTATES.Texas].map((state, index) => (
                        <option key={index} value={state.value}>
                          {state.label}
                        </option>
                      ))}
                    </Field>
                    <FormErrorMessage>{errors.city}</FormErrorMessage>
                  </FormControl>
                </Box>
                <Box>
                  <FormControl
                    isInvalid={
                      touched.rideShareTotalRides && errors.rideShareTotalRides
                        ? true
                        : false
                    }
                  >
                    <FormLabel>{t("RideSharetotalRides")}</FormLabel>
                    <Field
                      name="rideShareTotalRides"
                      as={Input}
                      onChange={(e) => {
                        const value = e.target.value;
                        setFieldValue(
                          "rideShareTotalRides",
                          value ? Number(value) : null,
                        );
                      }}
                      type="number"
                    />
                    <FormErrorMessage>
                      {errors.rideShareTotalRides}
                    </FormErrorMessage>
                  </FormControl>
                </Box>

                <Box>
                  <FormControl
                    isInvalid={
                      touched.avgEarningPerWeek && errors.avgEarningPerWeek
                        ? true
                        : false
                    }
                  >
                    <FormLabel>{t("AverageEarningPer")}</FormLabel>
                    <Field
                      name="avgEarningPerWeek"
                      as={Input}
                      onChange={(e) => {
                        const value = e.target.value;
                        setFieldValue(
                          "avgEarningPerWeek",
                          value ? Number(value) : null,
                        );
                      }}
                      type="number"
                    />
                    <FormErrorMessage>
                      {errors.avgEarningPerWeek}
                    </FormErrorMessage>
                  </FormControl>
                </Box>

                <Box>
                  <FormControl
                    isInvalid={
                      touched.privacyPolicy && errors.privacyPolicy
                        ? true
                        : false
                    }
                  >
                    <ConsentCheckBox
                      name="privacyPolicy"
                      defaultCheck={initialValues.privacyPolicy}
                      onChange={(e) => {
                        setFieldValue("privacyPolicy", e.target.checked);
                      }}
                    >
                      <Text
                        onClick={(e) => {
                          e.preventDefault();
                          window.open(
                            "/assets/pdfs/OCN-US-PrivacyPolicy.pdf",
                            "_blank",
                          );
                        }}
                        style={{
                          cursor: "pointer",
                        }}
                        _hover={{
                          textDecoration: "underline",
                          color: "#6210FF",
                        }}
                      >
                        {"I have read and agree to the Privacy Policy."}
                      </Text>
                    </ConsentCheckBox>
                    <FormErrorMessage>{errors.privacyPolicy}</FormErrorMessage>
                  </FormControl>
                </Box>

                <Box>
                  <FormControl
                    isInvalid={
                      touched.ocnBackgroundAndCreditCheckForApplication &&
                      errors.ocnBackgroundAndCreditCheckForApplication
                        ? true
                        : false
                    }
                  >
                    <ConsentCheckBox
                      name="ocnBackgroundAndCreditCheckForApplication"
                      defaultCheck={
                        initialValues.ocnBackgroundAndCreditCheckForApplication
                      }
                      onChange={(e) => {
                        setFieldValue(
                          "ocnBackgroundAndCreditCheckForApplication",
                          e.target.checked,
                        );
                      }}
                    >
                      <Text>
                        {
                          "I accept that OCN will run a background and credit score check for completing my application."
                        }
                      </Text>
                    </ConsentCheckBox>
                    <FormErrorMessage>
                      {errors.ocnBackgroundAndCreditCheckForApplication}
                    </FormErrorMessage>
                  </FormControl>
                </Box>
              </SimpleGrid>
            </Layout>
          </Form>
        );
      }}
    </Formik>
  );
};

interface IConsentCheckBoxProps {
  name: string;
  belowText?: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  defaultCheck?: boolean;
  colorScheme?: string;
  children: ReactNode;
}

const ConsentCheckBox = (props: IConsentCheckBoxProps) => {
  const {
    colorScheme = "blue",
    name,
    onChange,
    belowText,
    defaultCheck,
    children,
  } = props;

  return (
    <div>
      <ChakraCheckbox
        name={name}
        colorScheme={colorScheme}
        onChange={onChange}
        defaultChecked={defaultCheck}
      >
        <span>{children}</span>
      </ChakraCheckbox>
      <p>{belowText}</p>
    </div>
  );
};
