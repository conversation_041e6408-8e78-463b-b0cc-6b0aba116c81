import { useState } from "react";

const UploadEarningsVideo = () => {
  const [selectedFile, setSelectedFile] = useState(null);

  const handleFileChange = (event) => {
    setSelectedFile(event.target.files[0]);
  };

  const handleUpload = () => {
    if (selectedFile) {
      const formData = new FormData();
      formData.append("video", selectedFile);

      // Aquí debes reemplazar 'URL_DEL_ENDPOINT' con la URL real del endpoint donde deseas enviar el video
      fetch("URL_DEL_ENDPOINT", {
        method: "POST",
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          // Aquí puedes manejar la respuesta del servidor después de subir el video
          console.log(data);
        })
        .catch((error) => {
          // Aquí puedes manejar cualquier error que ocurra durante la subida del video
          console.error(error);
        });
    }
  };

  return (
    <div>
      <input type="file" accept="video/*" onChange={handleFileChange} />
      <button onClick={handleUpload}>Subir video</button>
    </div>
  );
};

export default UploadEarningsVideo;
