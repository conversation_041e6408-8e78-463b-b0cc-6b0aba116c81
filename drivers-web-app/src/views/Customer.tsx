import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputGroup,
  InputLeftAddon,
  SimpleGrid,
  useToast,
} from "@chakra-ui/react";
import { <PERSON><PERSON>, Header, Layout } from "components";
import { useSetup } from "context/SetupContext";
import {
  AdmissionRequestController,
  IRequestPersonalData,
} from "core/controllers/admissionRequest";
import { Field, Form, Formik } from "formik";
import { customerValidationSchema } from "misc/validationSchemas";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { US_COUNTRY_CODE } from "utils";

export const CustomerView = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const { t } = useTranslation();
  const { admissionRequest, setAdmissionRequest, isCountryUSA } = useSetup();
  const admissionRequestController = new AdmissionRequestController();
  const hardCodedCountryCode = "+52";
  const countryCode = isCountryUSA ? US_COUNTRY_CODE : hardCodedCountryCode;
  const personalData = admissionRequest?.personalData;
  const headingMarkup = (
    <Header
      title={t("PersonalDataTitle")}
      subtitle={t("PersonalDataSubtitle")}
    />
  );

  const initialValues = {
    firstName: "",
    lastName: "",
    phone: "",
    email: "",
  };

  if (personalData) {
    initialValues.firstName = personalData.firstName;
    initialValues.lastName = personalData.lastName;
    initialValues.email = personalData.email;
    // If the phone number starts with the hard coded country code, remove it
    initialValues.phone = personalData.phone?.includes(countryCode)
      ? personalData.phone.replaceAll(countryCode, "")
      : personalData.phone;
  }

  const handleSubmit = async (values) => {
    const payload: Partial<IRequestPersonalData> = {
      firstName: values.firstName,
      lastName: values.lastName,
      phone: `${countryCode}${values.phone}`,
      email: values.email,
    };

    if (admissionRequest) {
      const [err, data] = await admissionRequestController.updatePersonalData(
        admissionRequest?.id,
        payload,
      );
      if (!err) {
        setAdmissionRequest(data);
        navigate("/palenca");
      } else {
        toast({
          title: "Error",
          description: "Verifica los campos e intenta de nuevo",
          status: "error",
        });
      }
    }
  };

  const navigationMarkup = ({ isSubmitting, isValid, dirty }) => {
    const initialDisabled = dirty && !isValid;
    return (
      <Flex w="full">
        <Button
          variant="brand"
          w="full"
          type="submit"
          isDisabled={isSubmitting || initialDisabled}
        >
          {t("Continue")}
        </Button>
      </Flex>
    );
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={customerValidationSchema}
      onSubmit={handleSubmit}
    >
      {({ errors, isSubmitting, isValid, dirty, touched }) => (
        <Form style={{ width: "100%" }}>
          <Layout
            header={headingMarkup}
            navigation={navigationMarkup({
              isSubmitting,
              isValid,
              dirty,
            })}
          >
            <SimpleGrid gap={4} pb={24}>
              <Box>
                <FormControl
                  isInvalid={!!(touched.firstName && errors.firstName)}
                >
                  <FormLabel>{t("FirstName")}</FormLabel>
                  <Field name="firstName" as={Input} />
                  <FormErrorMessage>
                    {t(`${errors.firstName}`)}
                  </FormErrorMessage>
                </FormControl>
              </Box>
              <Box>
                <FormControl
                  isInvalid={touched.lastName && errors.lastName ? true : false}
                >
                  <FormLabel>{t("LastName")}</FormLabel>
                  <Field name="lastName" as={Input} />
                  <FormErrorMessage>{t(`${errors.lastName}`)}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box>
                <FormControl
                  isInvalid={touched.phone && errors.phone ? true : false}
                >
                  <FormLabel>{t("Phone")}</FormLabel>
                  <InputGroup>
                    <InputLeftAddon>{countryCode}</InputLeftAddon>
                    <Field name="phone" as={Input} />
                  </InputGroup>
                  <FormErrorMessage>{t(`${errors.phone}`)}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box>
                <FormControl
                  isInvalid={touched.email && errors.email ? true : false}
                >
                  <FormLabel>{t("Email")}</FormLabel>
                  <Field name="email" as={Input} />
                  <FormErrorMessage>{t(`${errors.email}`)}</FormErrorMessage>
                </FormControl>
              </Box>
            </SimpleGrid>
          </Layout>
        </Form>
      )}
    </Formik>
  );
};
