import { Box, Grid, Image, Stack, Text, useToast } from "@chakra-ui/react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Layout } from "components";
import { useSetup } from "context/SetupContext";
import { GigPlatform } from "core/domain/enums";
import { platforms, platformsUS } from "misc/data";
import { API_URL } from "misc/settings";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { PlatformCard } from "types";

export const PlatformsView = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { admissionRequest, isCountryUSA } = useSetup();

  const toast = useToast();
  const accounts = admissionRequest?.palenca.accounts;

  const handlePlatformClick = (slug: string) => {
    navigate(`/platforms/${slug}/connect`);
  };

  const handleContinue = async () => {
    const getEarnings = await fetch(
      `${API_URL}/ocr/get-earnings-analysis/${admissionRequest?.id}`,
    );
    if (getEarnings.ok) {
      navigate("/personal-data");
    } else {
      toast({
        title: "Error",
        description: t("UploadPlatformData"),
        status: "error",
      });
      navigate("/platforms");
    }
  };

  const cardsData: PlatformCard[] = (
    isCountryUSA ? platformsUS : platforms
  ).map((platform) => ({
    slug: platform.slug as GigPlatform,
    name: platform.name,
    logo: platform.logo,
    connected: false,
  }));

  const cardsWithStatus = cardsData.map((card) => {
    const account = accounts?.find((account) => account.platform === card.slug);
    if (account && account.status !== "pending") {
      card.connected = true;
    }
    return card;
  });

  const connectedPlatforms = cardsWithStatus.filter(
    (card) => card.connected,
  ).length;

  const displayNavigation = connectedPlatforms > 0;

  const navigationMarkup = (
    <Button variant="brand" w="full" onClick={handleContinue}>
      {t("Continue")}
    </Button>
  );

  const headingMarkup = (
    <Header title={t("PlatformTitle")} subtitle={t("PlatformSubtitle")} />
  );

  return (
    <Layout
      header={headingMarkup}
      navigation={displayNavigation ? navigationMarkup : undefined}
    >
      <Stack spacing={4}>
        <Grid
          gap={{ base: 8, md: 12 }}
          templateColumns={isCountryUSA ? "repeat(3, 1fr)" : "repeat(2, 1fr)"}
          justifyContent="center"
          alignItems="center"
        >
          {cardsWithStatus.map((card, index) => (
            <Box
              key={index}
              display="flex"
              flexDirection="column"
              alignItems="center"
              textAlign="center"
              onClick={() => !card.connected && handlePlatformClick(card.slug)}
              cursor={card.connected ? "not-allowed" : "default"}
              transition="opacity 0.3s, transform 0.3s"
              _hover={
                card.connected
                  ? {}
                  : {
                      cursor: "pointer",
                      opacity: 0.7,
                    }
              }
            >
              <Box
                position="relative"
                borderRadius="full"
                border={card.connected ? "3px solid" : "none"}
                borderColor={card.connected ? "success" : "transparent"}
                p={0}
              >
                <Image
                  boxSize={{ base: 14, md: 16 }}
                  objectFit="cover"
                  src={card.logo}
                  alt={`Image de ${card.name}`}
                  borderRadius="full"
                />
                {card.connected && (
                  <Image
                    position="absolute"
                    bottom={{
                      base: "calc(50% - 30px)",
                      md: "calc(50% - 35px)",
                    }}
                    right={{ base: "calc(50% - 30px)", md: "calc(50% - 35px)" }}
                    boxSize={{ base: "18px", md: "20px" }}
                    src="/assets/icons/check_cirlce_filled_icon.svg"
                    alt="Icono"
                  />
                )}
              </Box>
              <Text mt="2" fontSize={{ base: "sm", md: "md" }}>
                {card.name}
              </Text>
              {/* <Text
                mt="1"
                fontSize="sm"
                color={card.connected ? 'success' : 'success'}
                visibility={card.connected ? 'visible' : 'hidden'}
              >
                Conectada
              </Text> */}
            </Box>
          ))}
        </Grid>
      </Stack>
    </Layout>
  );
};
