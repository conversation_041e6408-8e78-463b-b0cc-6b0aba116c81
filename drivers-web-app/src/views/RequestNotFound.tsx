import { Flex, Text, VStack } from "@chakra-ui/react";
import { Layout } from "components";

export const RequestNotFoundView = () => {
  return (
    <Layout>
      <Flex flexDirection="column" align="center" justify="center" h="full">
        <VStack gap={4}>
          <Text
            mt="8"
            color="gray.700"
            fontSize="2xl"
            fontWeight="semibold"
            textAlign="center"
          >
            Solicitud no encontrada
          </Text>

          <Text
            color="gray.500"
            fontSize="lg"
            fontWeight="medium"
            textAlign="center"
          >
            Por favor, verifica que la URL sea correcta
          </Text>
        </VStack>
      </Flex>
    </Layout>
  );
};
