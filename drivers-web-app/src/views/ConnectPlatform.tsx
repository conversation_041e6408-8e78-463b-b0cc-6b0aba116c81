import { Box, Container, Tag, Text, useToast } from "@chakra-ui/react";
import {
  Step,
  StepDescription,
  StepIcon,
  StepIndicator,
  StepN<PERSON><PERSON>,
  Stepper,
  StepSeparator,
  StepStatus,
  StepTitle,
  useSteps,
} from "@chakra-ui/react";
import { Collapse, Table, Tbody, Td, Th, Thead, Tr } from "@chakra-ui/react";
import { Header, Layout } from "components";
import ImageDialog from "components/ImageDialog";
import { useSetup } from "context/SetupContext";
import { API_URL } from "misc/settings";
import { useEffect, useState } from "react";
import { Trans, useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";

import { Button } from "../components/Button";

type Earning = {
  earning: boolean;
  earningWeek: string;
};

type StepType = {
  title: string;
  description: string;
};

const platformScreenShots = {
  uberProfile: {
    imageUrl: "assets/screenshots/uber_profile.png",
    altText: "Uber Profile Sample",
  },
  uberProfileUS: {
    imageUrl: "assets/screenshots/uber_profile_us.png",
    altText: "Uber Profile Sample",
  },
  uberEarnings: {
    imageUrl: "assets/screenshots/uber_earnings.jpg",
    altText: "Uber Earnings Sample",
  },
  uberEarningsUS: {
    imageUrl: "assets/screenshots/uber_earnings_us.jpg",
    altText: "Uber Earnings Sample",
  },
  didiProfile: {
    imageUrl: "assets/screenshots/didi_profile.jpg",
    altText: "Didi Profile Sample",
  },
  didiEarnings: {
    imageUrl: "assets/screenshots/didi_earnings.jpg",
    altText: "Didi Earnings Sample",
  },
  lyftProfile: {
    imageUrl: "assets/screenshots/lyft_profile_jon_dow.png",
    altText: "Lyft Profile Sample",
  },
  lyftEarnings: {
    imageUrl: "assets/screenshots/lyft_earnings.png",
    altText: "Lyft Earnings Sample",
  },
  otherProfile: {
    imageUrl: "assets/screenshots/other_profile.jpg",
    altText: "Other Profile Sample",
  },
};

const stepsMX = [
  { title: "First", description: "Perfil" },
  { title: "Second", description: "Ganancias semanales" },
];

// const stepsUSLyft = [{ title: "First", description: "Profile Info" }];
const stepsOtherUS = [{ title: "First", description: "Profile Info" }];
const stepsUS = [
  { title: "First", description: "Profile Info" },
  { title: "Second", description: "Earning Info" },
];

export const ConnectPlatformView = () => {
  const navigate = useNavigate();
  const { slug } = useParams();
  const toast = useToast();
  const { t, i18n } = useTranslation();
  const platform = slug ?? ""; // Provide a default value for slug
  const { admissionRequest, isCountryUSA } = useSetup();
  const [selectedProfileFile, setSelectedProfileFile] = useState(null);
  const [isProfileLoading, setIsProfileLoading] = useState(false);

  const [selectedEarningFile, setSelectedEarningFile] = useState(null);
  const [isEarningLoading, setIsEarningLoading] = useState(false);

  const [profilePicUploadStatus, setProfilePicUploadStatus] = useState(false);
  const [profileMetrics, setProfileMetrics] = useState(null);
  const [earnings, setEarnings] = useState<Earning[]>([]);
  const [earningsToggle, setEarningToggle] = useState(false);
  let imageUrlProfile = "";
  let imageAltTextProfile = "";
  let imageUrlEarnings = "";
  let imageAltTextEarnings = "";
  if (slug === "uber") {
    if (isCountryUSA) {
      imageUrlProfile = platformScreenShots.uberProfileUS.imageUrl;
      imageAltTextProfile = platformScreenShots.uberProfileUS.altText;
      imageUrlEarnings = platformScreenShots.uberEarningsUS.imageUrl;
      imageAltTextEarnings = platformScreenShots.uberEarningsUS.altText;
    } else {
      imageUrlProfile = platformScreenShots.uberProfile.imageUrl;
      imageAltTextProfile = platformScreenShots.uberProfile.altText;
      imageUrlEarnings = platformScreenShots.uberEarnings.imageUrl;
      imageAltTextEarnings = platformScreenShots.uberEarnings.altText;
    }
  }
  if (slug === "didi") {
    imageUrlProfile = platformScreenShots.didiProfile.imageUrl;
    imageAltTextProfile = platformScreenShots.didiProfile.altText;
    imageUrlEarnings = platformScreenShots.didiEarnings.imageUrl;
    imageAltTextEarnings = platformScreenShots.didiEarnings.altText;
  }
  if (slug === "lyft") {
    imageUrlProfile = platformScreenShots.lyftProfile.imageUrl;
    imageAltTextProfile = platformScreenShots.lyftProfile.altText;
    imageUrlEarnings = platformScreenShots.lyftEarnings.imageUrl;
    imageAltTextEarnings = platformScreenShots.lyftEarnings.altText;
  }
  if (slug === "other") {
    imageUrlProfile = platformScreenShots.otherProfile.imageUrl;
    imageAltTextProfile = platformScreenShots.otherProfile.altText;
    // imageUrlEarnings = platformScreenShots.didiEarnings.imageUrl;
    // imageAltTextEarnings = platformScreenShots.didiEarnings.altText;
  }

  let steps: Array<StepType> = [];
  if (isCountryUSA) {
    if (slug === "other") {
      steps = stepsOtherUS;
    } else {
      steps = stepsUS;
    }
  } else {
    steps = stepsMX;
  }

  const { activeStep, setActiveStep } = useSteps({
    index: 0,
    count: steps.length,
  });

  const handleProfileFileChange = (event) => {
    setSelectedProfileFile(event.target.files[0]);
  };

  const handleEarningsFileChange = (event) => {
    setSelectedEarningFile(event.target.files[0]);
  };

  const handleProfileUpload = () => {
    setIsProfileLoading(true);
    if (selectedProfileFile) {
      const formData = new FormData();
      formData.append("image", selectedProfileFile);
      formData.append("platform", platform);
      formData.append("requestId", admissionRequest?.id ?? "");
      formData.append("uploadType", "profile");
      fetch(`${API_URL}/ocr/upload-screenshots`, {
        method: "POST",
        body: formData,
      })
        .then(async (response) => {
          const data = await response.json();
          if (response.status === 500) {
            if (data?.message === "Invalid Data") {
              throw new Error(t("InvalidData"));
            } else {
              throw new Error(t("ErrorUploadingImage"));
            }
          }
          if (response.status === 429) {
            throw new Error(t("ErrorUploadingMultipleTimes"));
          }
          if (!response.ok) {
            throw new Error(t("ErrorUploadingImage"));
          }
          return data;
        })
        .then((res) => {
          if (res?.fullName || res?.rating || platform === "other") {
            setProfilePicUploadStatus(true);
            setSelectedProfileFile(null);
          } else {
            toast({
              title: "Error",
              description: t("NoProfileInfo"),
              status: "error",
            });
          }
          // navigate(`/platforms/${slug}/success`);
        })
        .catch((error) => {
          toast({
            title: "Error",
            description: error.message,
            status: "error",
          });
        })
        .finally(() => {
          setIsProfileLoading(false);
        });
      //navigate(`/platforms/${slug}/success`);
    }
  };

  const handleEarningsUpload = () => {
    setIsEarningLoading(true);
    if (selectedEarningFile) {
      const formData = new FormData();
      formData.append("image", selectedEarningFile);
      formData.append("platform", platform);
      formData.append("requestId", admissionRequest?.id ?? "");
      formData.append("uploadType", "earnings");

      fetch(`${API_URL}/ocr/upload-screenshots`, {
        method: "POST",
        body: formData,
      })
        .then(async (response: Response) => {
          const data = await response.json();
          if (response.status === 500) {
            if (data?.message === "Invalid Data") {
              throw new Error(t("InvalidData"));
            } else {
              throw new Error(t("ErrorUploadingImage"));
            }
          }
          if (response.status === 429) {
            throw new Error(t("ErrorUploadingMultipleTimes"));
          }
          if (!response.ok) {
            throw new Error(t("ErrorUploadingImage"));
          }
          return data;
        })
        .then(() => {
          setEarningToggle(!earningsToggle);
          // navigate(`/platforms/${slug}/success`);
        })
        .catch((error) => {
          toast({
            title: "Error",
            description: error.message,
            status: "error",
          });
        })
        .finally(() => {
          setIsEarningLoading(false);
        });
      // navigate(`/platforms/${slug}/success`);
    }
  };

  useEffect(() => {
    //fetch existing data of customer if already parsed
    const getProfileData = async () => {
      const currentLanguage = i18n.language;
      const res = await fetch(
        `${API_URL}/ocr/profile-data/${admissionRequest?.id}/${platform}/${currentLanguage}`,
      );
      const data = await res.json();
      const { profileMetrics, earnings } = data;
      let platformProfileMetrics = profileMetrics;
      if (isCountryUSA) {
        if (profilePicUploadStatus && slug === "other") {
          platformProfileMetrics = {};
          if (!platformProfileMetrics) {
            setActiveStep(0);
            return;
          }
          setProfileMetrics(platformProfileMetrics);
          toast({
            title: t("DataCollected"),
            description: t("DataRecordedMessage"),
            status: "success",
            duration: 9000,
            isClosable: true,
          });
          navigate(`/platforms/${slug}/success`);
          return;
        }
      }
      setProfileMetrics(platformProfileMetrics);
      setEarnings(earnings);
      if (!profileMetrics) {
        setActiveStep(0);
      } else {
        setActiveStep(1);
      }
      if (earnings) {
        let missingEarningData = false;
        for (let i = 0; i < 12; i++) {
          const { earning } = earnings[i];
          if (!earning) {
            missingEarningData = true;
          }
        }
        if (!missingEarningData) {
          toast({
            title: t("DataCollected"),
            description: t("DataRecordedMessage"),
            status: "success",
            duration: 9000,
            isClosable: true,
          });
          navigate(`/platforms/${slug}/success`);
        }
      }
    };
    getProfileData();
  }, [
    admissionRequest?.id,
    platform,
    profilePicUploadStatus,
    earningsToggle,
    isCountryUSA,
    i18n.language,
    setActiveStep,
    toast,
    t,
    navigate,
    slug,
  ]);

  const isSlugOther = slug === "other";

  const headingMarkup = (
    <Header
      title={t("ConnectPlatformTitle")}
      subtitle={t("ConnectPlatformSubtitle")}
    />
  );
  return (
    <Container bg="#FAFAFA">
      <Layout
        header={headingMarkup}
        // navigation={displayNavigation ? navigationMarkup : undefined}
      >
        <Stepper
          size="lg"
          index={activeStep}
          orientation="horizontal"
          colorScheme="purple"
          gap={0}
        >
          {steps.map((step, index) => (
            <Step key={index}>
              <StepIndicator>
                <StepStatus
                  complete={<StepIcon />}
                  incomplete={<StepNumber />}
                  active={<StepNumber />}
                />
              </StepIndicator>

              <Box flexShrink="0">
                {!isSlugOther && <StepTitle>{t(step.title)}</StepTitle>}
                <StepDescription>{step.description}</StepDescription>
              </Box>

              <StepSeparator />
            </Step>
          ))}
        </Stepper>
        <Collapse in={activeStep === 0}>
          <Container centerContent>
            <br></br>
            <Text>
              {isSlugOther ? (
                t("ConnectPlatformScreenShotStepOtherPlatformText")
              ) : (
                <Trans
                  i18nKey={"ConnectPlatformScreenShotStepText"} // optional -> fallbacks to defaults if not provided
                  values={{
                    platform:
                      slug && slug.length > 0
                        ? slug.charAt(0).toUpperCase() + slug.substring(1)
                        : "",
                  }}
                  components={{ bold: <strong /> }}
                />
              )}
            </Text>
            <br></br>
            <ImageDialog
              imageUrl={imageUrlProfile}
              altText={imageAltTextProfile}
            ></ImageDialog>
            <br></br>
            {!isSlugOther && <Text>{t("ProfileInstructions")}</Text>}
            <br></br>
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <label
                htmlFor="file-upload"
                style={{
                  display: "inline-block",
                  padding: "10px 20px",
                  backgroundColor: "#5800F7",
                  color: "white",
                  borderRadius: "20px",
                  cursor: "pointer",
                  fontSize: "16px",
                  fontWeight: "bold",
                  textAlign: "center",
                  boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                }}
              >
                {t("ChooseFile")}
              </label>
              <input
                id="file-upload"
                type="file"
                accept="image/*"
                style={{ display: "none" }}
                onChange={handleProfileFileChange}
              />
            </div>
            <br></br>
            {selectedProfileFile && (
              <>
                <Text>{(selectedProfileFile as File)?.name?.slice(0, 10)}</Text>
                <br></br>
              </>
            )}

            {selectedProfileFile && (
              <>
                <Button
                  as="span"
                  disabled={isProfileLoading}
                  variant="outline"
                  width="80"
                  isLoading={isProfileLoading}
                  style={{
                    cursor: isProfileLoading ? "not-allowed" : "pointer",
                  }}
                  onClick={handleProfileUpload}
                >
                  {t("UploadScreenshot")}
                </Button>
                <br></br>
              </>
            )}
          </Container>
        </Collapse>
        {
          <Collapse in={activeStep === 1 && profileMetrics !== null}>
            <Container centerContent>
              <br></br>
              <Text color={"green"}>
                {t("ConnectPlatformScreenShotStepSuccess")}
              </Text>
              <br></br>
              <Text>
                <Trans
                  i18nKey="ConnectPlatformScreenShotStepText1" // optional -> fallbacks to defaults if not provided
                  values={{
                    mainMsg: t("ConnectPlatformScreenShotStepText1SubText"),
                  }}
                  components={{ bold: <strong /> }}
                />
              </Text>

              <br></br>
              <ImageDialog
                imageUrl={imageUrlEarnings}
                altText={imageAltTextEarnings}
              ></ImageDialog>
              <br></br>
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <label
                  htmlFor="earning-upload"
                  style={{
                    display: "inline-block",
                    padding: "10px 20px",
                    backgroundColor: "#5800F7",
                    color: "white",
                    borderRadius: "20px",
                    cursor: "pointer",
                    fontSize: "16px",
                    fontWeight: "bold",
                    textAlign: "center",
                    boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {t("ChooseFile")}
                </label>
                <input
                  id="earning-upload"
                  type="file"
                  accept="image/*"
                  style={{ display: "none" }}
                  onChange={handleEarningsFileChange}
                />
              </div>
              <br></br>
              {selectedEarningFile && (
                <>
                  <Text>
                    {(selectedEarningFile as unknown as File)?.name?.slice(
                      0,
                      10,
                    )}
                  </Text>
                  <br></br>
                </>
              )}
              {selectedEarningFile && (
                <>
                  <Button
                    as="span"
                    disabled={isEarningLoading}
                    variant="outline"
                    width="80"
                    isLoading={isEarningLoading}
                    style={{
                      cursor: isEarningLoading ? "not-allowed" : "pointer",
                    }}
                    onClick={handleEarningsUpload}
                  >
                    {t("UploadScreenshot")}
                  </Button>
                  <br />
                </>
              )}

              <Table size="sm" variant="simple">
                <Thead>
                  <Tr>
                    <Th>{t("WeekStartDate")}</Th>
                    <Th>{t("UploadStatus")}</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {earnings.map((item, index) => (
                    <Tr key={index}>
                      <Td>{item.earningWeek}</Td>
                      <Td>
                        {item.earning ? (
                          <Tag size="md" variant="solid" colorScheme="green">
                            {t("Checked")}
                          </Tag>
                        ) : (
                          <Tag size="md" variant="solid" colorScheme="red">
                            {t("Required")}
                          </Tag>
                        )}
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
              <br></br>
            </Container>
          </Collapse>
        }
      </Layout>
    </Container>
  );
};
