import { Flex, Text, VStack } from "@chakra-ui/react";
import { Layout } from "components";
import { useTranslation } from "react-i18next";

export const ServerErrorView = () => {
  const { t } = useTranslation();

  return (
    <Layout>
      <Flex flexDirection="column" align="center" justify="center" h="full">
        <VStack gap={4}>
          <Text
            mt="8"
            color="gray.700"
            fontSize="2xl"
            fontWeight="semibold"
            textAlign="center"
          >
            {t("UnKnownError")}
          </Text>

          <Text
            color="gray.500"
            fontSize="lg"
            fontWeight="medium"
            textAlign="center"
          >
            {t("PleaseTryAgain")}
          </Text>
        </VStack>
      </Flex>
    </Layout>
  );
};
