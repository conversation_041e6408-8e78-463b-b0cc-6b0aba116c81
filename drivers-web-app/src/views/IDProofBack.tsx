import { Flex, useToast } from "@chakra-ui/react";
import { Button, FileUpload, Header, Layout } from "components";
import { useSetup } from "context/SetupContext";
import { AdmissionRequestController } from "core/controllers/admissionRequest";
import { Media, RequestDocument } from "core/domain/entities";
import { AdmissionRequestDocumentType, MediaType } from "core/domain/enums";
import { useSetupNavigate } from "hooks/useSetupNavigate";
import {
  FileUploadAcceptByMediaTypeCatalog,
  FileUploadMaxSizeByMediaTypeCatalog,
  FileUploadTotalFilesByMediaTypeCatalog,
} from "misc/data";
import { useMemo, useState } from "react";

export const IDProofBackView = () => {
  const { nextScreen } = useSetupNavigate();
  const toast = useToast();
  const { admissionRequest, setAdmissionRequest } = useSetup();
  const [isLoading, setIsLoading] = useState(false);
  const [mediaId, setMediaId] = useState<Media | null>(null);
  const mediaType = MediaType.identity_card_back;
  const maxFileSizeMB = FileUploadMaxSizeByMediaTypeCatalog[mediaType];

  const title = "INE Reverso";
  const subtitle = `Sube el reverso de tu INE en formato JPG o PNG, no mayor a ${maxFileSizeMB}MB`;
  const headingMarkup = <Header title={title} subtitle={subtitle} />;

  const admissionRequestController = new AdmissionRequestController();

  const isButtonDisabled = useMemo(() => {
    return isLoading || !mediaId;
  }, [isLoading, mediaId]);

  const handleSubmit = async () => {
    setIsLoading(true);
    if (admissionRequest && mediaId) {
      const document = new RequestDocument({
        mediaId: mediaId.id,
        type: AdmissionRequestDocumentType.identity_card_back,
      });
      const [err, data] = await admissionRequestController.updateDocuments(
        admissionRequest.id,
        [document],
      );
      if (err) {
        toast({
          title: "Error",
          description: "Ocurrió un error al subir el archivo",
          status: "error",
        });
      }
      if (data) {
        setAdmissionRequest(data);
        nextScreen();
      }
    }
    setIsLoading(false);
  };

  const handleUploadChange = (media: Media[]) => {
    setMediaId(media[0] || null);
  };

  const navigationMarkup = (
    <Flex w="full" flexDirection="column" gap={6}>
      <Button
        isDisabled={isButtonDisabled}
        variant="brand"
        w="full"
        onClick={handleSubmit}
      >
        Siguiente
      </Button>
    </Flex>
  );

  return (
    <Layout header={headingMarkup} navigation={navigationMarkup}>
      <FileUpload
        primaryButtonText="Subir Archivo"
        accept={FileUploadAcceptByMediaTypeCatalog[mediaType]}
        mediaType={mediaType}
        onUploadChange={handleUploadChange}
        totalFiles={FileUploadTotalFilesByMediaTypeCatalog[mediaType]}
        maxFileSize={1024 * 1024 * maxFileSizeMB}
      />
    </Layout>
  );
};
