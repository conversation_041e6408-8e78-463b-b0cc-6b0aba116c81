import { Flex, useToast } from "@chakra-ui/react";
import { But<PERSON>, FileUpload, Header, Layout } from "components";
import { useSetup } from "context/SetupContext";
import { AdmissionRequestController } from "core/controllers/admissionRequest";
import { Media, RequestDocument } from "core/domain/entities";
import {
  AdmissionRequestDocumentType,
  AdmissionRequestDocumentTypeUS,
  MediaType,
} from "core/domain/enums";
import { useSetupNavigate } from "hooks/useSetupNavigate";
import {
  FileUploadAcceptByMediaTypeCatalog,
  FileUploadMaxSizeByMediaTypeCatalog,
  FileUploadTotalFilesByMediaTypeCatalog,
  FileUploadTotalFilesByMediaTypeCatalogUS,
} from "misc/data";
import { useMemo, useState } from "react";
import { Trans, useTranslation } from "react-i18next";
import {
  CountriesShortNames,
  REQUIRED_BANK_STATEMENTS_MX,
  REQUIRED_BANK_STATEMENTS_US,
} from "utils";

export const StatementsView = () => {
  const { nextScreen } = useSetupNavigate();
  const toast = useToast();
  const { t } = useTranslation();

  const { admissionRequest, setAdmissionRequest, isCountryUSA } = useSetup();
  const [isLoading, setIsLoading] = useState(false);
  const [media, setMedia] = useState<Media[]>([]);
  const mediaType = MediaType.bank_statement;
  const maxFileSizeMB = FileUploadMaxSizeByMediaTypeCatalog[mediaType];

  const accountStatementNumber = isCountryUSA
    ? FileUploadTotalFilesByMediaTypeCatalogUS[mediaType]
    : FileUploadTotalFilesByMediaTypeCatalog[mediaType];

  const headingMarkup = (
    <Header
      title={t("BankAccountStatementsTitle")}
      subtitle={
        <Trans
          i18nKey={"BankAccountStatementsSubtitle"}
          values={{
            accountStatementNumber: accountStatementNumber,
            maxFileSizeMB: maxFileSizeMB,
          }}
        />
      }
    />
  );

  const admissionRequestController = new AdmissionRequestController();

  const isButtonDisabled = useMemo(() => {
    return (
      isLoading ||
      (isCountryUSA
        ? media.length < REQUIRED_BANK_STATEMENTS_US
        : media.length < REQUIRED_BANK_STATEMENTS_MX)
    );
  }, [isLoading, media, isCountryUSA]);

  const primaryButtonText = useMemo(() => {
    const uploadFile = t("UploadFile");
    return uploadFile + ` (${media.length + 1}/${accountStatementNumber})`;
  }, [media, t, accountStatementNumber]);

  const handleSubmit = async () => {
    setIsLoading(true);
    if (admissionRequest && media) {
      const documentUpdate: RequestDocument[] = [];

      media.forEach((mediaItem, index) => {
        const document = new RequestDocument({
          mediaId: mediaItem.id,
          type: isCountryUSA
            ? AdmissionRequestDocumentTypeUS[
                `bank_statement_month_${
                  index + 1
                }` as keyof typeof AdmissionRequestDocumentTypeUS
              ]
            : AdmissionRequestDocumentType[
                `bank_statement_month_${
                  index + 1
                }` as keyof typeof AdmissionRequestDocumentType
              ],
        });
        documentUpdate.push(document);
      });

      const [err, data] = await admissionRequestController.updateDocuments(
        admissionRequest.id,
        documentUpdate,
        CountriesShortNames["United States"],
      );
      if (err) {
        toast({
          title: "Error",
          description: "Ocurrió un error al subir el archivo",
          status: "error",
        });
      }
      if (data) {
        setAdmissionRequest(data);
        nextScreen();
      }
    }
    setIsLoading(false);
  };

  const handleUploadChange = (media: Media[]) => {
    setMedia(media);
  };

  const navigationMarkup = (
    <Flex w="full" flexDirection="column" gap={6}>
      <Button
        isDisabled={isButtonDisabled}
        variant="brand"
        w="full"
        onClick={handleSubmit}
      >
        {t("Continue")}
      </Button>
    </Flex>
  );

  return (
    <Layout header={headingMarkup} navigation={navigationMarkup}>
      <FileUpload
        primaryButtonText={primaryButtonText}
        accept={FileUploadAcceptByMediaTypeCatalog[mediaType]}
        mediaType={mediaType}
        onUploadChange={handleUploadChange}
        totalFiles={accountStatementNumber}
        maxFileSize={1024 * 1024 * maxFileSizeMB}
      />
    </Layout>
  );
};
