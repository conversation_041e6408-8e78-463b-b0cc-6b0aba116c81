import { Flex, Image, Text, VStack } from "@chakra-ui/react";
import { Button, Layout } from "components";
import { useSetup } from "context/SetupContext";
import { RequestDocumentStatus } from "core/domain/enums";
import { useSetupNavigate } from "hooks/useSetupNavigate";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export const SuccessDataSentView = () => {
  const { t } = useTranslation();
  const { nextScreen } = useSetupNavigate();
  const { admissionRequest, clearSkipScreens } = useSetup();

  const [remainingDocument, setRemainingDocument] = useState<boolean>(false);

  useEffect(() => {
    if (admissionRequest) {
      for (const [
        ,
        document,
      ] of admissionRequest!.documentsAnalysis.documents.entries()) {
        if (document.status === RequestDocumentStatus.pending) {
          setRemainingDocument(true);
          break;
        }
      }
    }
  }, [admissionRequest]);

  const navigationMarkup = () => {
    return (
      <Flex w="full">
        <Button
          variant="brand"
          w="full"
          onClick={() => {
            clearSkipScreens();
            nextScreen();
          }}
        >
          {t("Continue")}
        </Button>
      </Flex>
    );
  };

  return (
    <Layout navigation={remainingDocument ? navigationMarkup() : undefined}>
      <Flex flexDirection="column" align="center" justify="center" h="full">
        <VStack gap={4}>
          <Image
            src="/assets/icons/check_circle_icon.svg"
            alt="Icono Círculo Verificación"
            boxSize={28}
          />
          <Text
            fontWeight="semibold"
            fontSize="3xl"
            mb={2}
            align="center"
            className="mt-4"
          >
            {t("DataSendCorrectly")}
          </Text>
          <Text mt="4" fontSize="md" textAlign="center">
            {t("DataSendSuccessMsg")}
          </Text>
          {remainingDocument && (
            <Text mt="12" fontSize="md" textAlign="center">
              {t("continueOnboarding")}
            </Text>
          )}
        </VStack>
      </Flex>
    </Layout>
  );
};
