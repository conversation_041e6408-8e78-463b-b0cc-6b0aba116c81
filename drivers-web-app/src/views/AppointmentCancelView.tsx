import { Box, Text } from "@chakra-ui/react";
import { CarCancelSvg } from "components/CarCancelSvg";
import { Steps } from "context/ScheduleViewContext";
import { useTranslation } from "react-i18next";

interface IAppointmentCancelView {
  step: number;
}

export default function AppointmentCancelView(props: IAppointmentCancelView) {
  const { step } = props;

  const { t } = useTranslation();

  const message =
    step === Steps.Success
      ? t("AppointmentPageSuccessMessage")
      : t("AppointmentPageCancelMessage");

  return (
    <Box
      background="#FAFAFF"
      px={{ base: 0, md: 12 }}
      py={{ base: 0, md: 10 }}
      display={{ md: "flex" }}
      justifyContent={{ md: "center" }}
    >
      <Box
        display={"flex"}
        flexDirection={"column"}
        justifyItems={"center"}
        alignItems={"center"}
        gap={8}
        maxWidth={"400px"}
      >
        <Text
          textAlign={"center"}
          fontSize={"x-large"}
          fontWeight={"bold"}
          py={6}
        >
          {message}
        </Text>
        <CarCancelSvg />
      </Box>
    </Box>
  );
}
