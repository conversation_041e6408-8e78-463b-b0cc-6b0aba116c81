import { Box, Button, Flex, Image, Text } from "@chakra-ui/react";
import { ImageUploadSection } from "components";
import {
  HeaderImage,
  HomeViewImageUploadHeader,
} from "components/HomeViewImageUpload";
import {
  homeImagesFrontTotalImagesRequired,
  homeImagesGarageTotalImagesRequired,
  homeImagesSurroundingsTotalImagesRequired,
} from "components/Steppers/utils";
import { useSetup } from "context/SetupContext";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

enum HomeImageUploadEnum {
  HomeView = 0,
  ImageUploadSection = 1,
  SuccessView = 2,
}

export const HomeImageUpload = () => {
  const [steps, setSteps] = useState(HomeImageUploadEnum.HomeView);

  const handleSteps = (step: number) => {
    setSteps(step);
  };

  return steps === HomeImageUploadEnum.HomeView ? (
    <HomeView handleSteps={handleSteps} />
  ) : steps === HomeImageUploadEnum.ImageUploadSection ? (
    <ImageUploadSection handleSteps={handleSteps}>
      <HomeViewImageUploadHeader />
    </ImageUploadSection>
  ) : steps === HomeImageUploadEnum.SuccessView ? (
    <SuccessView />
  ) : null;
};

const HomeView = (props: { handleSteps: (step: number) => void }) => {
  const { handleSteps } = props;
  const { t } = useTranslation();

  const { admissionRequest } = useSetup();
  const homeVisit = admissionRequest?.homeVisit;

  const homeImagesFront = homeVisit?.homeImages?.homeImagesFront?.length;
  const homeImagesGarage = homeVisit?.homeImages?.homeImagesGarage?.length;
  const homeImagesSurroundings =
    homeVisit?.homeImages?.homeImagesSurroundings?.length;

  useEffect(() => {
    if (
      homeImagesFront &&
      homeImagesGarage &&
      homeImagesSurroundings &&
      homeImagesFront >= homeImagesFrontTotalImagesRequired &&
      homeImagesGarage >= homeImagesGarageTotalImagesRequired &&
      homeImagesSurroundings >= homeImagesSurroundingsTotalImagesRequired
    ) {
      handleSteps(HomeImageUploadEnum.SuccessView);
    }
  }, [handleSteps, homeImagesFront, homeImagesGarage, homeImagesSurroundings]);

  return (
    <Box maxW="lg" mx="auto">
      <HeaderImage />
      <Flex
        justifyContent={"center"}
        alignItems={"center"}
        flexDirection={"column"}
        height={"90vh"}
        px={4}
        gap={2}
      >
        <Text
          fontSize={{ base: "xx-large", md: "x-large" }}
          fontWeight={700}
          textAlign={"center"}
        >
          {t("WelcomeToOCN")}
        </Text>
        <Text fontSize={{ base: "large", md: "medium" }} textAlign={"center"}>
          {t("FollowInstructionsOnEachPage")}
        </Text>
        <Image
          src={"/assets/icons/camera-1989.png"}
          alt="Upload Image"
          width={36}
        />
        <Box py={8} width={"100%"} display={"flex"} justifyContent={"center"}>
          <Button
            variant="brand"
            width={"85%"}
            height={10}
            fontSize={{ base: "medium", md: "sm" }}
            color={"white"}
            backgroundColor={"#5800F7"}
            borderRadius={50}
            _hover={{ backgroundColor: "#6210FF" }}
            onClick={() => handleSteps(1)}
          >
            {t("Continue")}
          </Button>
        </Box>
      </Flex>
    </Box>
  );
};

const SuccessView = () => {
  const { t } = useTranslation();

  return (
    <Box maxW="lg" mx="auto">
      <HeaderImage />
      <Flex
        justifyContent={"center"}
        alignItems={"center"}
        flexDirection={"column"}
        height={"90vh"}
        px={4}
        gap={2}
      >
        <Image
          src={"/assets/icons/check_circle_icon.svg"}
          alt="Upload Image"
          width={40}
          pt={12}
          pb={20}
        />
        <Text
          fontSize={"xx-large"}
          fontWeight={700}
          textAlign={"center"}
          pt={6}
          pb={2}
        >
          {t("Success")}
        </Text>
        <Text fontSize={"large"} textAlign={"center"} pb={4}>
          {t("ImagesUploadedSuccessfully")}
        </Text>
        <Box mt={28} width={"100%"} display={"flex"} justifyContent={"center"}>
          {/* <Button
            variant="brand"
            width={"90%"}
            height={10}
            fontSize={"large"}
            color={"white"}
            backgroundColor={"#5800F7"}
            borderRadius={50}
            _hover={{ backgroundColor: "#6210FF" }}
            onClick={() => {
              // redirect to new tab
            }}
          >
            {t("CloseThisWindow")}
          </Button> */}
        </Box>
      </Flex>
    </Box>
  );
};
