import { Flex, Image, Text, VStack } from "@chakra-ui/react";
import { Layout } from "components";
import { useSetup } from "context/SetupContext";
import { EarningsAnalysisStatus } from "core/domain/enums";
import { POLL_INTERVAL_MS } from "misc/settings";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

enum Views {
  waiting = "waiting",
  usedVehicle = "usedVehicle",
  rejected = "rejectes",
}

export const EarningsAnalysisView = () => {
  const navigate = useNavigate();
  const { admissionRequest, checkEarningsAnalysis } = useSetup();
  const admissionRequestRef = useRef(admissionRequest);
  const [view, setView] = useState<Views>(Views.waiting);

  const { t } = useTranslation();

  useEffect(() => {
    function evaluateAdmissionRequestStatus() {
      if (admissionRequestRef.current?.earningsAnalysis) {
        const { status } = admissionRequestRef.current.earningsAnalysis;
        if (status === EarningsAnalysisStatus.pending) {
          setView(Views.waiting);
        }
        if (status === EarningsAnalysisStatus.approved) {
          navigate("/personal-data");
        }
        if (status === EarningsAnalysisStatus.approved_with_conditions) {
          setView(Views.usedVehicle);
        }

        if (status === EarningsAnalysisStatus.rejected) {
          setView(Views.rejected);
        }
      }
    }
    admissionRequestRef.current = admissionRequest;
    evaluateAdmissionRequestStatus();
  }, [admissionRequest, navigate]);

  useEffect(() => {
    const interval = setInterval(async () => {
      const currentStatus =
        admissionRequestRef.current?.earningsAnalysis.status || null;
      if (shouldStopPooling(currentStatus)) {
        clearInterval(interval);
      } else {
        console.log("Checking earnings analysis...");
      }
    }, POLL_INTERVAL_MS);
    return () => {
      clearInterval(interval);
    };
  }, [checkEarningsAnalysis]);

  function shouldStopPooling(status: EarningsAnalysisStatus | null) {
    return status !== EarningsAnalysisStatus.pending && status !== null;
  }

  // const handleContinue = () => {
  //   navigate("/personal-data");
  // };

  // const handleContactAdvisor = () => {
  //   alert("Contactar a un asesor OCN");
  // };

  const waitingMarkUp = (
    <Layout>
      {/* <Flex flexDirection="column" align="center" justify="center" h="full">
        <VStack gap={4}>
          <Spinner
            speed="0.65s"
            thickness="0.27em"
            emptyColor="gray.100"
            color="brand"
            boxSize={16}
          />
          <Text
            fontWeight="semibold"
            fontSize="3xl"
            align="center"
            className="mt-4"
          >
            Espera un momento
          </Text>
          <Text mt="2" fontSize="md" textAlign="center">
            No cierres el navegador, estamos revisando tus datos...
          </Text>
        </VStack>
      </Flex> */}
      <Flex flexDirection="column" align="center" justify="center" h="full">
        <VStack gap={4}>
          <Image
            src="/assets/icons/check_circle_icon.svg"
            alt="Icono Círculo Verificación"
            className="h-28"
          />
          <Text
            fontWeight="semibold"
            fontSize="3xl"
            mb={2}
            align="center"
            className="mt-4"
          >
            {t("EarningAnalysisDataSendSuccessfully")}
          </Text>
          <Text mt="2" fontSize="md" textAlign="center">
            {t("EarningAnalysisDataSendSubMsg")}
          </Text>
        </VStack>
      </Flex>
    </Layout>
  );

  const rejectedMarkup = (
    <Layout>
      <Flex flexDirection="column" align="center" justify="center" h="full">
        <VStack gap={4}>
          <Image
            src="/assets/icons/check_circle_icon.svg"
            alt="Icono Círculo Verificación"
            className="h-28"
          />
          <Text
            fontWeight="semibold"
            fontSize="3xl"
            mb={2}
            align="center"
            className="mt-4"
          >
            {t("EarningAnalysisDataSendSuccessfully")}
          </Text>
          <Text mt="2" fontSize="md" textAlign="center">
            {t("EarningAnalysisDataSendSubMsg")}
          </Text>
        </VStack>
      </Flex>
    </Layout>
  );

  const usedVehicleNavigationMarkup = (
    //   <Flex w="full">
    //     <Button variant="brand" w="full" onClick={handleContinue}>
    //       Aceptar y completar solicitud
    //     </Button>
    //     {/* <Button
    //   variant='outline'
    //   className="mt-8"
    //   width="100%"
    //   onClick={handleContactAdvisor}
    // >
    //   Contactar a un asesor OCN
    // </Button> */}
    //   </Flex>
    <Flex flexDirection="column" align="center" justify="center" h="full">
      <VStack gap={4}>
        <Image
          src="/assets/icons/check_circle_icon.svg"
          alt="Icono Círculo Verificación"
          className="h-28"
        />
        <Text
          fontWeight="semibold"
          fontSize="3xl"
          mb={2}
          align="center"
          className="mt-4"
        >
          {t("EarningAnalysisDataSendSuccessfully")}
        </Text>
        <Text mt="2" fontSize="md" textAlign="center">
          {t("EarningAnalysisDataSendSubMsg")}
        </Text>
      </VStack>
    </Flex>
  );

  const usedVehicleMarkUp = (
    <Layout navigation={usedVehicleNavigationMarkup}>
      {/* <Flex flexDirection="column" align="center" justify="center" h="full">
        <VStack gap={4}>
          <Image
            src="/assets/icons/check_circle_icon.svg"
            alt="Icono Círculo Verificación"
            boxSize={28}
          />
          <Text
            fontWeight="semibold"
            fontSize="3xl"
            align="center"
            className="mt-4"
          >
            Se te pre-aprobó un vehículo
          </Text>
          <Text color="primary" fontWeight="bold" fontSize="3xl" align="center">
            SEMI-NUEVO
          </Text>
          <Text mt="4" fontSize="md" textAlign="center">
            Esto como resultado del análisis de tus ingresos y plataformas. Este
            vehículo se encuentra en perfectas condiciones.
          </Text>
          <Text mt="4" fontSize="md" textAlign="center">
            Puedes aceptarlo, lo cual contribuirá a subir tus posibilidades de
            obtener un vehículo nuevo a futuro, o puedes contactar a un asesor
            OCN para reevaluar tu perfil.
          </Text>
        </VStack>
      </Flex> */}
      <Flex flexDirection="column" align="center" justify="center" h="full">
        <VStack gap={4}>
          <Image
            src="/assets/icons/check_circle_icon.svg"
            alt="Icono Círculo Verificación"
            className="h-28"
          />
          <Text
            fontWeight="semibold"
            fontSize="3xl"
            mb={2}
            align="center"
            className="mt-4"
          >
            {t("EarningAnalysisDataSendSuccessfully")}
          </Text>
          <Text mt="2" fontSize="md" textAlign="center">
            {t("EarningAnalysisDataSendSuccessfully")}
          </Text>
        </VStack>
      </Flex>
    </Layout>
  );

  return (
    <>
      {view === Views.waiting && waitingMarkUp}
      {view === Views.usedVehicle && usedVehicleMarkUp}
      {view === Views.rejected && rejectedMarkup}
    </>
  );
};
