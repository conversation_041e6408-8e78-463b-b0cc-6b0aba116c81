import { <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, Text } from "@chakra-ui/react";
import axios from "axios";
import { Layout } from "components";
import { API_URL, VITE_ONBOARDING_URL } from "misc/settings";
import { useEffect } from "react";

export const PalencaVerifyView = () => {
  const url = new URL(window.location.href);
  const accountId = url.searchParams.get("account_id");
  const externalId = url.searchParams.get("external_id");

  const redirectToOCRFlow = () => {
    window.location.href = `${VITE_ONBOARDING_URL}/?id=${externalId}`;
  };

  async function getPalencaData(account_id: string) {
    try {
      const res = await axios.get(
        `${API_URL}/palenca/palencaData?account_id=${account_id}`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      );
      console.log("res", res);
      if (res.status !== 200) {
        console.log("failed to fetch palenca data");
        redirectToOCRFlow();
        return;
      }

      const { platform } = res.data;
      console.log("fplatform", platform);
      // Palenca login worked and data fetched from APIS continue with document analysis
      const response = await axios.get(
        `${API_URL}/palenca/updateProfilAndEarning?account_id=${account_id}&external_id=${externalId}&platform=${platform}`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      );
      console.log("updateProfilAndEarning response", JSON.stringify(response));

      if (response.status !== 200) {
        console.log("failed to updated Profile and Earning Metrics");
        redirectToOCRFlow();
        return;
      }

      // Perform earnings analysis
      const getEarnings = await fetch(
        `${API_URL}/ocr/get-earnings-analysis/${externalId}`,
      );
      console.log("getEarnings", getEarnings);

      if (getEarnings.ok) {
        //redirect to welcome screen which will redirect itself to document analysis screen
        window.location.href = `${VITE_ONBOARDING_URL}/?id=${externalId}`;
      } else {
        console.log("getEarnings failed", getEarnings);
        redirectToOCRFlow();
        return;
      }
    } catch (error) {
      console.error("Error in handling Palenca data:", error);
      redirectToOCRFlow();
    }
  }

  useEffect(() => {
    if (accountId) {
      const timer = setTimeout(() => {
        getPalencaData(accountId);
      }, 5000); // 5 seconds

      return () => clearTimeout(timer); // Cleanup the timeout if the component unmounts or accountId changes
    }
  }, [accountId]); // Make sure to include accountId in the dependency array

  console.log("accountId", accountId);
  return (
    <Layout>
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        height="100vh"
      >
        <Spinner
          speed="0.65s"
          thickness="3px"
          emptyColor="gray.100"
          color="primary"
          size="xl"
        />
        <Heading mb={3}>Procesando tu solicitud...</Heading>
        <Center>
          <Text fontSize="xl" align={"center"}>
            Por favor, no cierres esta ventana ni recargues la página. Esto
            tomar unos segundos.
          </Text>
        </Center>
      </Box>
    </Layout>
  );
};
