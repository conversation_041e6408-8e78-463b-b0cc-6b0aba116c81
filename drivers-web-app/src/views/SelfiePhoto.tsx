import { Flex, useToast } from "@chakra-ui/react";
import { Button, FileUpload, Header, Layout } from "components";
import { useSetup } from "context/SetupContext";
import { AdmissionRequestController } from "core/controllers/admissionRequest";
import { Media, RequestDocument } from "core/domain/entities";
import {
  AdmissionRequestAdditionalDocumentType,
  MediaType,
} from "core/domain/enums";
import { useSetupNavigate } from "hooks/useSetupNavigate";
import {
  FileUploadAcceptByMediaTypeCatalog,
  FileUploadMaxSizeByMediaTypeCatalog,
  FileUploadTotalFilesByMediaTypeCatalog,
} from "misc/data";
import { useState } from "react";
import { Trans, useTranslation } from "react-i18next";
import { Countries, CountriesShortNames } from "utils";

export const SelfiePhotoView = () => {
  const { nextScreen } = useSetupNavigate();
  const { t } = useTranslation();
  const toast = useToast();
  const {
    admissionRequest,
    setAdmissionRequest,
    addSkipScreen,
    isDocumentOptional,
  } = useSetup();
  const [isLoading, setIsLoading] = useState(false);
  const [mediaId, setMediaId] = useState<Media | null>(null);
  const mediaType = MediaType.selfie_photo;
  const maxFileSizeMB = FileUploadMaxSizeByMediaTypeCatalog[mediaType];

  const title = isDocumentOptional
    ? `${t("SelfiePhoto")} (${t("Optional")})`
    : `${t("SelfiePhoto")} `;

  const headingMarkup = (
    <Header
      title={title}
      subtitle={
        <Trans
          i18nKey={"SelfiePhotoSubtitle"}
          values={{ maxFileSizeMB: maxFileSizeMB }}
        />
      }
    />
  );

  const admissionRequestController = new AdmissionRequestController();

  const handleSubmit = async () => {
    setIsLoading(true);
    if (admissionRequest && mediaId) {
      const document = new RequestDocument({
        mediaId: mediaId.id,
        type: AdmissionRequestAdditionalDocumentType.selfie_photo,
      });
      const [err, data] = await admissionRequestController.updateDocuments(
        admissionRequest.id,
        [document],
        CountriesShortNames[Countries.Mexico],
      );
      if (err) {
        toast({
          title: "Error",
          description: t("AnErrorOccurredWhileUploadingTheFile"),
          status: "error",
        });
      }
      if (data) {
        setAdmissionRequest(data);
        nextScreen();
      }
    }
    setIsLoading(false);
  };

  const handleSubmitLater = () => {
    addSkipScreen(AdmissionRequestAdditionalDocumentType.selfie_photo);
  };

  const handleUploadChange = (media: Media[]) => {
    setMediaId(media[0] || null);
  };

  const navigationMarkup = (
    <Flex w="full" flexDirection="column" gap={6}>
      <Button
        isDisabled={!mediaId || isLoading}
        variant="brand"
        w="full"
        onClick={handleSubmit}
      >
        {t("Continue")}
      </Button>
      {isDocumentOptional && (
        <Button
          isDisabled={!!mediaId || isLoading}
          variant="brand"
          w="full"
          onClick={handleSubmitLater}
        >
          {t("SubmitLater")}
        </Button>
      )}
    </Flex>
  );

  return (
    <Layout header={headingMarkup} navigation={navigationMarkup}>
      <FileUpload
        primaryButtonText={t("UploadFile")}
        accept={FileUploadAcceptByMediaTypeCatalog[mediaType]}
        mediaType={mediaType}
        onUploadChange={handleUploadChange}
        totalFiles={FileUploadTotalFilesByMediaTypeCatalog[mediaType]}
        maxFileSize={1024 * 1024 * maxFileSizeMB}
      />
    </Layout>
  );
};
