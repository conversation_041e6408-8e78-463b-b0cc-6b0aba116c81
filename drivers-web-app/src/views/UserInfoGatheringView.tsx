import { Box, Flex, Image, Text, useToast, VStack } from "@chakra-ui/react";
import { Layout } from "components";
import {
  AdmissionRequestController,
  IAdmissionRequest,
} from "core/controllers/admissionRequest";
import { IpifyController } from "core/controllers/ipify";
import { RequestLatLongData } from "core/domain/entities";
import { getIdFromPath } from "core/misc/searchParams";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export const UserInfoGatheringView = () => {
  const { t } = useTranslation();
  const toast = useToast();

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [requestId, setRequestId] = useState<string>();
  const [admissionRequest, setAdmissionRequest] = useState<
    IAdmissionRequest | undefined
  >();

  const admissionRequestController = new AdmissionRequestController();
  const ipifyController = new IpifyController();

  const ipLookUp = async () => {
    const [, err, ipAddress] = await ipifyController.getIP();
    if (err) {
      toast({
        title: "Error",
        description: t("unableToGetIPAddress"),
        status: "error",
      });
      return;
    }
    await admissionRequestController.ipAddressLookup(
      requestId!,
      new RequestLatLongData({ ipAddress: ipAddress }),
    );
    setIsLoading(false);
  };

  const onSuccess = async (position: GeolocationPosition) => {
    const { latitude, longitude } = position.coords;
    try {
      const [, error, ipAddress] = await ipifyController.getIP();
      if (error) {
        toast({
          title: "Error",
          description: t("unableToGetIPAddress"),
          status: "error",
        });
        return;
      }
      await admissionRequestController.latLongLookup(
        requestId!,
        new RequestLatLongData({
          latitude,
          longitude,
          ipAddress,
        }),
      );
      setIsLoading(false);
    } catch (error) {
      ipLookUp();
    }
  };

  const onError = async () => {
    ipLookUp();
  };

  useEffect(() => {
    if (!requestId) {
      const id = getIdFromPath(location.href);
      admissionRequestController.getAdmissionRequest(id).then((value) => {
        const [, data, response] = value;
        if (response?.success) {
          setAdmissionRequest(data);
        }
      });
      setRequestId(id);
      return;
    }
    if (admissionRequest) {
      if (admissionRequest.locationData) {
        setIsLoading(false);
        return;
      }
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(onSuccess, onError);
      } else {
        ipLookUp();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requestId, admissionRequest]);

  return (
    <Layout>
      {isLoading ? (
        <Box height="100%" justifyContent="center" alignContent="center">
          <Box justifyItems="center">
            <Image
              src="/assets/animations/user_info_gathering.gif"
              alt="Gif de recopilación de información del usuario"
              boxSize={294}
            />
          </Box>
          <Text mt="16" fontSize="30px" fontWeight="700" textAlign="center">
            {t("pleaseWait")}
          </Text>
          <Text fontSize="lg" textAlign="center">
            {t("userInfoGathering")}
          </Text>
        </Box>
      ) : (
        <Flex flexDirection="column" align="center" justify="center" h="full">
          <VStack gap={4}>
            <Image
              src="/assets/icons/check_circle_icon.svg"
              alt="Icono Círculo Verificación"
              boxSize={294}
            />
            <Text mt="16" fontSize="30px" fontWeight="700" textAlign="center">
              {t("Success")}
            </Text>
            <Text fontSize="md" textAlign="center">
              {t("userInfoGatheringSuccess")}
            </Text>
          </VStack>
        </Flex>
      )}
    </Layout>
  );
};
