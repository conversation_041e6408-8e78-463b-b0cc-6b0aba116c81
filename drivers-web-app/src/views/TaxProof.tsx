import { Flex, useToast } from "@chakra-ui/react";
import { Button, FileUpload, Header, Layout } from "components";
import { useSetup } from "context/SetupContext";
import { AdmissionRequestController } from "core/controllers/admissionRequest";
import { Media, RequestDocument } from "core/domain/entities";
import {
  AdmissionRequestAdditionalDocumentType,
  MediaType,
} from "core/domain/enums";
import { useSetupNavigate } from "hooks/useSetupNavigate";
import {
  FileUploadAcceptByMediaTypeCatalog,
  FileUploadMaxSizeByMediaTypeCatalog,
  FileUploadTotalFilesByMediaTypeCatalog,
} from "misc/data";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Countries, CountriesShortNames } from "utils";

export const TaxProofView = () => {
  const { nextScreen } = useSetupNavigate();
  const toast = useToast();
  const { t } = useTranslation();
  const {
    admissionRequest,
    setAdmissionRequest,
    addSkipScreen,
    isCountryUSA,
    isDocumentOptional,
  } = useSetup();
  const [isLoading, setIsLoading] = useState(false);
  const [mediaId, setMediaId] = useState<Media | null>(null);
  const mediaType = MediaType.proof_of_tax_situation;
  const maxFileSizeMB = FileUploadMaxSizeByMediaTypeCatalog[mediaType];
  const subtitle = `Sube tu constancia de situación fiscal en formato PDF no mayor a ${maxFileSizeMB}MB`;

  const titleContent = isDocumentOptional
    ? `${t("ProofOfTaxSituation")} (${t("Optional")})`
    : `${t("ProofOfTaxSituation")}`;

  const headingMarkup = <Header title={titleContent} subtitle={subtitle} />;

  const admissionRequestController = new AdmissionRequestController();

  const handleSubmit = async () => {
    setIsLoading(true);
    if (admissionRequest && mediaId) {
      const document = new RequestDocument({
        mediaId: mediaId.id,
        type: AdmissionRequestAdditionalDocumentType.proof_of_tax_situation,
      });
      const [err, data] = await admissionRequestController.updateDocuments(
        admissionRequest.id,
        [document],
        isCountryUSA
          ? CountriesShortNames["United States"]
          : CountriesShortNames[Countries.Mexico],
      );
      if (err) {
        toast({
          title: "Error",
          description: t("AnErrorOccurredWhileUploadingTheFile"),
          status: "error",
        });
      }
      if (data) {
        setAdmissionRequest(data);
        nextScreen();
      }
    }
    setIsLoading(false);
  };

  const handleSubmitLater = () => {
    addSkipScreen(
      AdmissionRequestAdditionalDocumentType.proof_of_tax_situation,
    );
  };

  const handleUploadChange = (media: Media[]) => {
    setMediaId(media[0] || null);
  };

  const navigationMarkup = (
    <Flex w="full" flexDirection="column" gap={6}>
      <Button
        isDisabled={!mediaId || isLoading}
        variant="brand"
        w="full"
        onClick={handleSubmit}
      >
        Siguiente
      </Button>
      {isDocumentOptional && (
        <Button
          isDisabled={!!mediaId || isLoading}
          variant="brand"
          w="full"
          onClick={handleSubmitLater}
        >
          {t("SubmitLater")}
        </Button>
      )}
    </Flex>
  );

  return (
    <Layout header={headingMarkup} navigation={navigationMarkup}>
      <FileUpload
        primaryButtonText="Subir Archivo"
        accept={FileUploadAcceptByMediaTypeCatalog[mediaType]}
        mediaType={mediaType}
        onUploadChange={handleUploadChange}
        totalFiles={FileUploadTotalFilesByMediaTypeCatalog[mediaType]}
        maxFileSize={1024 * 1024 * maxFileSizeMB}
      />
    </Layout>
  );
};
