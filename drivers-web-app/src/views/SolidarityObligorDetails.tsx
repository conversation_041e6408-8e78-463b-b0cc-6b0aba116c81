import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  SimpleGrid,
  useToast,
} from "@chakra-ui/react";
import { Button, FileUpload, Header, Layout } from "components";
import { useSetup } from "context/SetupContext";
import {
  AdmissionRequestController,
  IRequestAvalData,
} from "core/controllers/admissionRequest";
import { AvalData, Media, RequestDocument } from "core/domain/entities";
import {
  AdmissionRequestAdditionalDocumentType,
  MediaType,
} from "core/domain/enums";
import { Field, Form, Formik } from "formik";
import { useSetupNavigate } from "hooks/useSetupNavigate";
import {
  FileUploadAcceptByMediaTypeCatalog,
  FileUploadMaxSizeByMediaTypeCatalog,
  FileUploadTotalFilesByMediaTypeCatalog,
} from "misc/data";
import { solidarityObligorDetailsValidationSchema } from "misc/validationSchemas";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Countries, CountriesShortNames } from "utils";

export const SolidarityObligorDetails = () => {
  const { nextScreen } = useSetupNavigate();
  const { t } = useTranslation();
  const toast = useToast();
  const {
    admissionRequest,
    setAdmissionRequest,
    addSkipScreen,
    isDocumentOptional,
  } = useSetup();
  const [isLoading, setIsLoading] = useState(false);
  const [mediaIdINEFront, setMediaIdINEFront] = useState<Media | null>(null);
  const [mediaIdINEBack, setMediaIdINEBack] = useState<Media | null>(null);
  const mediaType = MediaType.identity_card_back;
  const maxFileSizeMB = FileUploadMaxSizeByMediaTypeCatalog[mediaType];
  const admissionRequestController = new AdmissionRequestController();

  const avalData = admissionRequest?.avalData;

  const title = isDocumentOptional
    ? `${t("SolidarityObligorDetails")} (${t("Optional")})`
    : `${t("SolidarityObligorDetails")}`;

  const headingMarkup = <Header title={title} />;

  const initialValues: AvalData = {
    name: "",
    phone: "",
    email: "",
    location: "",
  };

  if (avalData) {
    initialValues.name = avalData.name;
    initialValues.phone = avalData.phone;
    initialValues.email = avalData.email;
    initialValues.location = avalData.location;
  }

  const handleSubmitSolidarityObligorINE = async (value: AvalData) => {
    setIsLoading(true);
    if (admissionRequest && mediaIdINEFront && mediaIdINEBack) {
      const payload = {
        name: value.name,
        phone: value.phone,
        email: value.email,
        location: value.location,
      } as IRequestAvalData;
      const responses = await Promise.all([
        admissionRequestController.addAvalData(admissionRequest.id, payload),
        admissionRequestController.updateDocuments(
          admissionRequest.id,
          [
            new RequestDocument({
              mediaId: mediaIdINEFront.id,
              type: AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front,
            }),
            new RequestDocument({
              mediaId: mediaIdINEBack.id,
              type: AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back,
            }),
          ],
          CountriesShortNames[Countries.Mexico], // setting country mx static because taking this document for Mexico users
        ),
      ]);
      if (responses[0][1]) {
        setAdmissionRequest(responses[1][1]);
        nextScreen();
      } else {
        toast({
          title: "Error",
          description: t("AnErrorOccurredWhileUploadingTheFile"),
          status: "error",
        });
      }
    }
    setIsLoading(false);
  };

  const handleSubmitLater = () => {
    addSkipScreen(
      AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front,
    );
  };

  const handleUploadINE = (
    media: Media[],
    type:
      | AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front
      | AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back,
  ) => {
    if (
      type ===
      AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front
    ) {
      setMediaIdINEFront(media[0] || null);
    } else {
      setMediaIdINEBack(media[0] || null);
    }
  };

  const navigationMarkup = ({ isValid, dirty }) => {
    return (
      <Flex w="full" flexDirection="column" gap={6}>
        <Button
          isDisabled={
            !(mediaIdINEFront && mediaIdINEBack) ||
            isLoading ||
            !(dirty && isValid)
          }
          variant="brand"
          w="full"
          type="submit"
        >
          {t("Continue")}
        </Button>
        {isDocumentOptional && (
          <Button
            isDisabled={
              (!!(mediaIdINEFront && mediaIdINEBack) || isLoading) &&
              dirty &&
              isValid
            }
            variant="brand"
            w="full"
            onClick={handleSubmitLater}
          >
            {t("SubmitLater")}
          </Button>
        )}
      </Flex>
    );
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={solidarityObligorDetailsValidationSchema}
      onSubmit={handleSubmitSolidarityObligorINE}
    >
      {({ errors, isValid, dirty, touched }) => (
        <Form style={{ width: "100%" }}>
          <Layout
            header={headingMarkup}
            navigation={navigationMarkup({ isValid, dirty })}
          >
            <SimpleGrid columns={2} gap={2} pb={4}>
              <Box>
                <FormControl isInvalid={!!(touched.name && errors.name)}>
                  <FormLabel>{t("name")}</FormLabel>
                  <Field name="name" as={Input} />
                  <FormErrorMessage>{t(`${errors.name}`)}</FormErrorMessage>
                </FormControl>
              </Box>
              <Box>
                <FormControl isInvalid={!!(touched.phone && errors.phone)}>
                  <FormLabel>{t("Phone")}</FormLabel>
                  <Field name="phone" as={Input} />
                  <FormErrorMessage>{t(`${errors.phone}`)}</FormErrorMessage>
                </FormControl>
              </Box>
              <Box>
                <FormControl isInvalid={!!(touched.email && errors.email)}>
                  <FormLabel>{t("Email")}</FormLabel>
                  <Field name="email" as={Input} />
                  <FormErrorMessage>{t(`${errors.email}`)}</FormErrorMessage>
                </FormControl>
              </Box>
              <Box>
                <FormControl
                  isInvalid={!!(touched.location && errors.location)}
                >
                  <FormLabel>{t("location")}</FormLabel>
                  <Field name="location" as={Input} />
                  <FormErrorMessage>{t(`${errors.location}`)}</FormErrorMessage>
                </FormControl>
              </Box>
            </SimpleGrid>
            <Box
              id="solidarity-obligor-ine-front-box"
              key={"solidarity-obligor-ine-front-box-key"}
              pb={4}
            >
              <FormLabel>{t("SolidarityObligorINEFrontPhoto")}</FormLabel>
              <FileUpload
                key={"SolidarityObligorINEFrontPhoto"}
                identifier={
                  AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front
                }
                primaryButtonText={t("UploadFile")}
                accept={FileUploadAcceptByMediaTypeCatalog[mediaType]}
                mediaType={mediaType}
                onUploadChange={(media) => {
                  handleUploadINE(
                    media,
                    AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front,
                  );
                }}
                totalFiles={FileUploadTotalFilesByMediaTypeCatalog[mediaType]}
                maxFileSize={1024 * 1024 * maxFileSizeMB}
              />
            </Box>
            <Box
              id="solidarity-obligor-ine-back-box"
              key={"solidarity-obligor-ine-back-box-key"}
            >
              <FormLabel>{t("SolidarityObligorINEBackPhoto")}</FormLabel>
              <FileUpload
                key={"SolidarityObligorINEBackPhoto"}
                identifier={
                  AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back
                }
                primaryButtonText={t("UploadFile")}
                accept={FileUploadAcceptByMediaTypeCatalog[mediaType]}
                mediaType={mediaType}
                onUploadChange={(media) => {
                  handleUploadINE(
                    media,
                    AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back,
                  );
                }}
                totalFiles={FileUploadTotalFilesByMediaTypeCatalog[mediaType]}
                maxFileSize={1024 * 1024 * maxFileSizeMB}
              />
            </Box>
          </Layout>
        </Form>
      )}
    </Formik>
  );
};
