import { Flex, useToast } from "@chakra-ui/react";
import { Button, FileUpload, Header, Layout } from "components";
import { useSetup } from "context/SetupContext";
import { AdmissionRequestController } from "core/controllers/admissionRequest";
import { Media, RequestDocument } from "core/domain/entities";
import { AdmissionRequestDocumentType, MediaType } from "core/domain/enums";
import { useSetupNavigate } from "hooks/useSetupNavigate";
import {
  FileUploadAcceptByMediaTypeCatalog,
  FileUploadMaxSizeByMediaTypeCatalog,
  FileUploadTotalFilesByMediaTypeCatalog,
} from "misc/data";
import { useMemo, useState } from "react";
import { Trans, useTranslation } from "react-i18next";
import { Countries, CountriesShortNames } from "utils";

export const AddressProofView = () => {
  const { nextScreen } = useSetupNavigate();
  const toast = useToast();
  const { t } = useTranslation();
  const { admissionRequest, setAdmissionRequest, isCountryUSA } = useSetup();
  const [isLoading, setIsLoading] = useState(false);
  const [mediaId, setMediaId] = useState<Media | null>(null);

  const mediaType = MediaType.proof_of_address;
  const maxFileSizeMB = FileUploadMaxSizeByMediaTypeCatalog[mediaType];

  const headingMarkup = (
    <Header
      title={t("ProofOfAddressTitle")}
      subtitle={
        <Trans
          i18nKey={"ProofOfAddressSubtitle"}
          values={{ maxFileSizeMB: maxFileSizeMB }}
        />
      }
      note={t("ProofOfAddressNote")}
    />
  );
  const admissionRequestController = new AdmissionRequestController();

  const isButtonDisabled = useMemo(() => {
    return isLoading || !mediaId;
  }, [isLoading, mediaId]);

  const handleSubmit = async () => {
    setIsLoading(true);

    if (admissionRequest && mediaId) {
      const document = new RequestDocument({
        mediaId: mediaId.id,
        type: AdmissionRequestDocumentType.proof_of_address,
      });
      const [err, data] = await admissionRequestController.updateDocuments(
        admissionRequest.id,
        [document],
        isCountryUSA
          ? CountriesShortNames["United States"]
          : CountriesShortNames[Countries.Mexico],
      );

      if (err) {
        toast({
          title: "Error",
          description: "Ocurrió un error al subir el archivo",
          status: "error",
        });
      }
      if (data) {
        setAdmissionRequest(data);
        nextScreen();
      }
    }
    setIsLoading(false);
  };

  const handleUploadChange = (media: Media[]) => {
    setMediaId(media[0] || null);
  };

  const navigationMarkup = (
    <Flex w="full" flexDirection="column" gap={6}>
      <Button
        isDisabled={isButtonDisabled}
        variant="brand"
        w="full"
        onClick={handleSubmit}
      >
        {t("Continue")}
      </Button>
    </Flex>
  );

  return (
    <Layout header={headingMarkup} navigation={navigationMarkup}>
      <FileUpload
        primaryButtonText={t("UploadFile")}
        accept={FileUploadAcceptByMediaTypeCatalog[mediaType]}
        mediaType={mediaType}
        onUploadChange={handleUploadChange}
        totalFiles={FileUploadTotalFilesByMediaTypeCatalog[mediaType]}
        maxFileSize={1024 * 1024 * maxFileSizeMB}
      />
    </Layout>
  );
};
