import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Select,
  SimpleGrid,
  useToast,
} from "@chakra-ui/react";
import { <PERSON><PERSON>, Header, Layout } from "components";
import { useSetup } from "context/SetupContext";
import {
  AdmissionRequestController,
  IRequestPersonalData,
} from "core/controllers/admissionRequest";
import { RequestPersonalDataStepStatus } from "core/domain/enums";
import { Field, Form, Formik } from "formik";
import { useSetupNavigate } from "hooks/useSetupNavigate";
import { mexicanStates } from "misc/data";
import { personalDataValidationSchema } from "misc/validationSchemas";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { PersonalDataViewUS } from "./PersonalDataUS";

export const PersonalDataView = () => {
  const { isCountryUSA } = useSetup();

  if (isCountryUSA) {
    return <PersonalDataViewUS />;
  }
  return <PersonalDataViewMX />;
};

export const PersonalDataViewMX = () => {
  const toast = useToast();
  const { nextScreen } = useSetupNavigate();
  const { t } = useTranslation();
  const { admissionRequest, setAdmissionRequest } = useSetup();
  const admissionRequestController = new AdmissionRequestController();

  const personalData = admissionRequest?.personalData;
  const headingMarkup = (
    <Header
      title={t("PersonalDataTitle")}
      subtitle={t("PersonalDataSubtitle")}
    />
  );

  const initialValues = {
    birthdate: "",
    taxId: "",
    nationalId: "",
    postalCode: "",
    city: "",
    state: "",
    neighborhood: "",
    street: "",
    streetNumber: "",
    department: "",
    status: RequestPersonalDataStepStatus.pending,
  };

  if (personalData) {
    initialValues.birthdate = personalData.birthdate;
    initialValues.taxId = personalData.taxId;
    initialValues.nationalId = personalData.nationalId;
    initialValues.postalCode = personalData.postalCode;
    initialValues.city = personalData.city;
    initialValues.state = personalData.state;
    initialValues.neighborhood = personalData.neighborhood;
    initialValues.street = personalData.street;
    initialValues.streetNumber = personalData.streetNumber;
    initialValues.department = personalData.department;
    initialValues.status = personalData.status as RequestPersonalDataStepStatus;
  }

  const handleSubmit = async (values) => {
    // Create a payload object containing only the fields that have changed
    const payload: Partial<IRequestPersonalData> = Object.keys(values).reduce(
      (acc, key) => {
        // Check if the current field value is different from the initial value
        if (values[key] !== initialValues[key]) {
          // If different, add it to the payload
          acc[key] = values[key];
        }
        return acc;
      },
      {},
    );

    // Explicitly adding nationalId value here if it's not exist
    // It can be happen when value is fetched and not updated and the form submitted
    if (!payload.nationalId) {
      payload.nationalId = values["nationalId"];
    }

    if (admissionRequest) {
      const [err, data] = await admissionRequestController.updatePersonalData(
        admissionRequest?.id,
        payload,
      );
      if (!err) {
        setAdmissionRequest(data);
        nextScreen();
      } else {
        if (err.response && err.response.error.code === "already_exist") {
          toast({
            title: "Error",
            description: t("UserWithCURPAlreadyExist"),
            status: "error",
          });
        } else {
          toast({
            title: "Error",
            description: t("PleaseCheckTheFieldsAndTryAgain"),
            status: "error",
          });
        }
      }
    }
  };

  const navigationMarkup = ({ isSubmitting, isValid, dirty }) => {
    const initialDisabled = dirty && !isValid;
    return (
      <Flex w="full">
        <Button
          variant="brand"
          w="full"
          type="submit"
          isDisabled={isSubmitting || initialDisabled}
        >
          {t("Continue")}
        </Button>
      </Flex>
    );
  };

  /**
   * Half screen navigation is happing in SetupContext.tsx @see [redirectBasedOnStatus] and
   * half navigation is happing in useSetupNavigate hook @see [useSetupNavigate]
   * Using this useEffect to navigate to next screen because it's the simplest way this navigation decision
   * should be in @see [SetupContext] file but @see [SetupContext] is high level module and @see [useSetupNavigate] is level
   * we cannot use @see [useSetupNavigate] hook for navigation.
   */
  useEffect(() => {
    if (
      personalData &&
      (personalData.status == RequestPersonalDataStepStatus.completed ||
        personalData.status == RequestPersonalDataStepStatus.approved)
    ) {
      nextScreen();
    }
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={personalDataValidationSchema}
      onSubmit={handleSubmit}
    >
      {({ errors, setFieldValue, isSubmitting, isValid, dirty, touched }) => (
        <Form style={{ width: "100%" }}>
          <Layout
            header={headingMarkup}
            navigation={navigationMarkup({
              isSubmitting,
              isValid,
              dirty,
            })}
          >
            <SimpleGrid gap={4} pb={24}>
              <Box>
                <FormControl
                  isInvalid={
                    touched.birthdate && errors.birthdate ? true : false
                  }
                >
                  <FormLabel>{t("Birthdate")}</FormLabel>
                  <Field name="birthdate" as={Input} type="date" />
                  <FormErrorMessage>{errors.birthdate}</FormErrorMessage>
                </FormControl>
              </Box>
              <Box>
                <FormControl
                  isInvalid={touched.taxId && errors.taxId ? true : false}
                >
                  <FormLabel>RFC</FormLabel>
                  <Field
                    name="taxId"
                    as={Input}
                    onChange={(e) => {
                      // Convert the value to uppercase
                      const upperCaseValue = e.target.value.toUpperCase();
                      // Set the value in Formik
                      setFieldValue("taxId", upperCaseValue);
                    }}
                  />
                  <FormErrorMessage>{errors.taxId}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box>
                <FormControl
                  isInvalid={
                    touched.nationalId && errors.nationalId ? true : false
                  }
                >
                  <FormLabel>CURP</FormLabel>
                  <Field
                    name="nationalId"
                    as={Input}
                    onChange={(e) => {
                      // Convert the value to uppercase
                      const upperCaseValue = e.target.value.toUpperCase();
                      // Set the value in Formik
                      setFieldValue("nationalId", upperCaseValue);
                    }}
                  />
                  <FormErrorMessage>{errors.nationalId}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box>
                <FormControl
                  isInvalid={
                    touched.postalCode && errors.postalCode ? true : false
                  }
                >
                  <FormLabel>{t("ZipCode")}</FormLabel>
                  <Field name="postalCode" as={Input} />
                  <FormErrorMessage>{errors.postalCode}</FormErrorMessage>
                </FormControl>
              </Box>
              <Box>
                <FormControl
                  isInvalid={touched.state && errors.state ? true : false}
                >
                  <FormLabel>{t("State")}</FormLabel>
                  <Field name="state" as={Select} placeholder="Estado">
                    {mexicanStates.map((state, index) => (
                      <option key={index} value={state}>
                        {state}
                      </option>
                    ))}
                  </Field>
                  <FormErrorMessage>{errors.state}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box>
                <FormControl
                  isInvalid={touched.city && errors.city ? true : false}
                >
                  <FormLabel>{t("City")}</FormLabel>
                  <Field name="city" as={Input} />
                  <FormErrorMessage>{errors.city}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box>
                <FormControl
                  isInvalid={
                    touched.neighborhood && errors.neighborhood ? true : false
                  }
                >
                  <FormLabel>{t("Colonia")}</FormLabel>
                  <Field name="neighborhood" as={Input} />
                  <FormErrorMessage>{errors.neighborhood}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box>
                <FormControl
                  isInvalid={touched.street && errors.street ? true : false}
                >
                  <FormLabel>{t("Street")}</FormLabel>
                  <Field name="street" as={Input} />
                  <FormErrorMessage>{errors.street}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box>
                <FormControl
                  isInvalid={
                    touched.streetNumber && errors.streetNumber ? true : false
                  }
                >
                  <FormLabel>{t("Address1")}</FormLabel>
                  <Field name="streetNumber" as={Input} />
                  <FormErrorMessage>{errors.streetNumber}</FormErrorMessage>
                </FormControl>
              </Box>

              <Box>
                <FormControl
                  isInvalid={
                    touched.department && errors.department ? true : false
                  }
                >
                  <FormLabel>{t("Address2")}</FormLabel>
                  <Field name="department" as={Input} />
                  <FormErrorMessage>{errors.department}</FormErrorMessage>
                </FormControl>
              </Box>
            </SimpleGrid>
          </Layout>
        </Form>
      )}
    </Formik>
  );
};
