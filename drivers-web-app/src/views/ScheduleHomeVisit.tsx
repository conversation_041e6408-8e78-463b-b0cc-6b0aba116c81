import { ArrowBackIcon } from "@chakra-ui/icons";
import { Box, Text } from "@chakra-ui/react";
import { AppointmentScheduler } from "components/AppointmentScheduler";
import {
  AppointmentReScheduler,
  ReScheduleHeading,
  ReScheduleView,
} from "components/ReSchedule";
import {
  ScheduleViewContextProvider,
  Steps,
  useScheduleView,
} from "context/ScheduleViewContext";
import { Fragment } from "react";
import { useTranslation } from "react-i18next";

import AppointmentCancelView from "./AppointmentCancelView";

const ScheduleBackButton = () => {
  const { step, setStep } = useScheduleView();
  const { t } = useTranslation();

  return (
    step > Steps.Date &&
    step !== Steps.Reschedule && (
      <Box display={"flex"} alignItems={"center"} gap={2} px={4}>
        <ArrowBackIcon
          h={8}
          w={8}
          color={"#6210FF"}
          onClick={() => {
            setStep(step - 1);
          }}
        />
        <Text fontWeight={"bold"} color="#6210FF">
          {t("Back")}
        </Text>
      </Box>
    )
  );
};

const ScheduleView = () => {
  const { step } = useScheduleView();

  return (
    <Fragment>
      {step === Steps.Success ? (
        <AppointmentCancelView step={step} />
      ) : step === Steps.Cancel ? (
        <AppointmentCancelView step={step} />
      ) : (
        <Box
          background={"#FFFFFF"}
          shadow={"md"}
          borderRadius={"md"}
          maxW={{ base: "auto", md: "1200px" }}
          py={4}
        >
          <ScheduleBackButton />
          <ReScheduleHeading />
          <Box
            display={{ base: "block", md: "flex" }}
            gap={2}
            px={{ base: 2, md: 8 }}
            py={{ base: 4, md: 6 }}
          >
            {step === Steps.Reschedule ? (
              <AppointmentReScheduler />
            ) : (
              <AppointmentScheduler />
            )}
          </Box>
          <ReScheduleView />
        </Box>
      )}
    </Fragment>
  );
};

export const ScheduleHomeVisitView = () => {
  return (
    <Box
      background="#fdfbfb"
      px={{ base: 0, md: 12 }}
      py={{ base: 0, md: 10 }}
      display={{ md: "flex" }}
      justifyContent={{ md: "center" }}
    >
      <ScheduleViewContextProvider>
        <ScheduleView />
      </ScheduleViewContextProvider>
    </Box>
  );
};
