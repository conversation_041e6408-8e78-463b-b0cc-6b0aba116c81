import { Image, Stack, Text } from "@chakra-ui/react";
import { Button, Layout } from "components";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

export const WelcomeView = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleStart = () => {
    navigate("/customer");
  };

  const navigationMarkup = (
    <Button
      id="getStartedButton"
      variant="brand"
      w="full"
      onClick={handleStart}
    >
      {t("GetStarted")}
    </Button>
  );

  const pageMarkup = (
    <Stack spacing="3">
      <Image mx="auto" boxSize={28} src="/assets/logo_ocn.svg" alt="Logo OCN" />
      <Text
        id="welcomeText"
        color="brand"
        fontSize="4xl"
        fontWeight="semibold"
        textAlign="center"
      >
        {t("Welcome")}
      </Text>
      <Text id="welcomeText1" mt="6" fontSize="lg" textAlign="center">
        {t("WelcomeText1")}
      </Text>
      <Text id="welcomeText2" mt="4" fontSize="lg" textAlign="center">
        {t("WelcomeText2")}
      </Text>
      <Text id="thankYouText" mt="4" fontSize="lg" textAlign="center">
        {t("ThankYou")}
      </Text>
    </Stack>
  );

  return <Layout navigation={navigationMarkup}>{pageMarkup}</Layout>;
};
