import "@fontsource/plus-jakarta-sans/400.css";
import "./i18n/i18n";
import "react-photo-view/dist/react-photo-view.css";

import { ChakraProvider } from "@chakra-ui/react";
import { createRoot } from "react-dom/client";
import { MemoryRouter } from "react-router-dom";

import { ProtectRoute, SetupProvider } from "./context/SetupContext";
import { Routing } from "./Routing";
import theme from "./theme";

const container = document.getElementById("root") as HTMLDivElement;
const root = createRoot(container);

function App() {
  return (
    <MemoryRouter>
      <SetupProvider>
        <ProtectRoute>
          <Routing />
        </ProtectRoute>
      </SetupProvider>
    </MemoryRouter>
  );
}

root.render(
  <ChakraProvider
    theme={theme}
    toastOptions={{ defaultOptions: { position: "top-right" } }}
  >
    <App />
  </ChakraProvider>,
);
