import { Route, Routes } from "react-router-dom";
import { CurpView } from "views/Curp";
import { HomeImageUpload } from "views/HomeImageUpload";
import { PalencaView } from "views/Palenca";
import { PalencaVerifyView } from "views/PalencaVerify";
import { ScheduleHomeVisitView } from "views/ScheduleHomeVisit";
import { SolidarityObligorDetails } from "views/SolidarityObligorDetails";
import { UserInfoGatheringView } from "views/UserInfoGatheringView";

import { AddressProofView } from "./views/AddressProof";
import { ConnectPlatformView } from "./views/ConnectPlatform";
import { ConnectPlatformSuccessView } from "./views/ConnectPlatformSuccess";
import { CustomerView } from "./views/Customer";
import { GaragePhotoView } from "./views/GaragePhoto";
import { HomeView } from "./views/Home";
import { IDProofBackView } from "./views/IDProofBack";
import { IDProofFrontView } from "./views/IDProofFront";
import { LicenceProofBackView } from "./views/LicenceProofBack";
import { LicenceProofFrontView } from "./views/LicenceProofFront";
import { PersonalDataView } from "./views/PersonalData";
import { PlatformsView } from "./views/Platforms";
import { RequestNotFoundView } from "./views/RequestNotFound";
import { SelfiePhotoView } from "./views/SelfiePhoto";
import { ServerErrorView } from "./views/ServerError";
import { StatementsView } from "./views/Statements";
import { SuccessDataSentView } from "./views/SuccessDataSent";
import { TaxProofView } from "./views/TaxProof";
import { WelcomeView } from "./views/Welcome";

export const Routing = () => {
  return (
    <Routes>
      <Route path="/" element={<HomeView />} />
      <Route path="/welcome" element={<WelcomeView />} />
      <Route path="/customer" element={<CustomerView />} />
      <Route path="/platforms" element={<PlatformsView />} />
      <Route path="/palenca" element={<PalencaView></PalencaView>} />
      <Route
        path="/palenca-verify"
        element={<PalencaVerifyView></PalencaVerifyView>}
      />
      <Route
        path="/platforms/:slug/connect"
        element={<ConnectPlatformView />}
      />
      <Route
        path="/platforms/:slug/success"
        element={<ConnectPlatformSuccessView />}
      />
      {/* <Route path="/earnings-analysis" element={<EarningsAnalysisView />} /> */}
      <Route path="/personal-data" element={<PersonalDataView />} />
      <Route
        path="/documents/identity_card_front"
        element={<IDProofFrontView />}
      />
      <Route
        path="/documents/identity_card_back"
        element={<IDProofBackView />}
      />
      <Route
        path="/documents/drivers_license_front"
        element={<LicenceProofFrontView />}
      />
      <Route
        path="/documents/drivers_license_back"
        element={<LicenceProofBackView />}
      />
      <Route
        path="/documents/proof_of_address"
        element={<AddressProofView />}
      />
      <Route path="/documents/bank_statements" element={<StatementsView />} />
      <Route
        path="/documents/proof_of_tax_situation"
        element={<TaxProofView />}
      />
      <Route path="/documents/selfie_photo" element={<SelfiePhotoView />} />
      <Route path="/documents/garage_photo" element={<GaragePhotoView />} />
      <Route
        path="/documents/solidarity_obligor_identity_card_front"
        element={<SolidarityObligorDetails />}
      />
      <Route path="/documents/curp" element={<CurpView />} />
      <Route path="/documents-success" element={<SuccessDataSentView />} />
      <Route path="/errors/server" element={<ServerErrorView />} />

      <Route path="/schedule-home-visit" element={<ScheduleHomeVisitView />} />
      <Route path="/user-info-gathering" element={<UserInfoGatheringView />} />
      <Route path="/home-image-upload" element={<HomeImageUpload />} />

      {/* Errors */}
      <Route
        path="/errors/request-not-found"
        element={<RequestNotFoundView />}
      />
    </Routes>
  );
};
