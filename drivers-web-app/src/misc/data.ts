import { MediaType } from "core/domain/enums";
export interface Platform {
  slug: string;
  name: string;
  logo: string;
}

export const platforms: Platform[] = [
  {
    slug: "uber",
    name: "Uber",
    logo: "/assets/platforms/uber.jpeg",
  },
  {
    slug: "didi",
    name: "<PERSON><PERSON>",
    logo: "/assets/platforms/didi.jpeg",
  },
  // removing InDrive SCRUM-128
  // {
  //   slug: "indriver",
  //   name: "InDriver",
  //   logo: "/assets/platforms/indriver.jpeg",
  // },
];

export const platformsUS: Platform[] = [
  {
    slug: "uber",
    name: "Uber",
    logo: "/assets/platforms/uber.jpeg",
  },
  {
    slug: "lyft",
    name: "Lyft",
    logo: "/assets/platforms/lyft.jpeg",
  },
  {
    slug: "other",
    name: "Other",
    logo: "/assets/platforms/other.png",
  },
];

export const FileUploadAcceptByMediaTypeCatalog = {
  [MediaType.proof_of_tax_situation]: {
    text: "jpg, jpeg o png",
    types: ["image/jpg", "image/jpeg", "image/png"],
  },
  [MediaType.identity_card_front]: {
    text: "jpg, jpeg o png",
    types: ["image/jpg", "image/jpeg", "image/png"],
  },
  [MediaType.identity_card_back]: {
    text: "jpg, jpeg o png",
    types: ["image/jpg", "image/jpeg", "image/png"],
  },
  [MediaType.drivers_license_front]: {
    text: "jpg, jpeg o png",
    types: ["image/jpg", "image/jpeg", "image/png"],
  },
  [MediaType.drivers_license_back]: {
    text: "jpg, jpeg o png",
    types: ["image/jpg", "image/jpeg", "image/png"],
  },

  [MediaType.proof_of_address]: {
    text: "jpg, jpeg, png o pdf",
    types: ["image/jpg", "image/jpeg", "image/png", "application/pdf"],
  },

  [MediaType.bank_statement]: {
    text: "pdf",
    types: ["application/pdf"],
  },

  [MediaType.selfie_photo]: {
    text: "jpg, jpeg o png",
    types: ["image/jpg", "image/jpeg", "image/png"],
  },

  [MediaType.garage_photo]: {
    text: "jpg, jpeg o png",
    types: ["image/jpg", "image/jpeg", "image/png"],
  },
  [MediaType.curp]: {
    text: "pdf",
    types: ["application/pdf"],
  },
};

export const FileUploadMaxSizeByMediaTypeCatalog = {
  [MediaType.proof_of_tax_situation]: 5,
  [MediaType.identity_card_front]: 5,
  [MediaType.identity_card_back]: 5,
  [MediaType.drivers_license_front]: 5,
  [MediaType.drivers_license_back]: 5,
  [MediaType.proof_of_address]: 5,
  [MediaType.bank_statement]: 5,
  [MediaType.curp]: 5,
};

export const FileUploadTotalFilesByMediaTypeCatalog = {
  [MediaType.proof_of_tax_situation]: 1,
  [MediaType.identity_card_front]: 1,
  [MediaType.identity_card_back]: 1,
  [MediaType.drivers_license_front]: 1,
  [MediaType.drivers_license_back]: 1,
  [MediaType.proof_of_address]: 1,
  [MediaType.bank_statement]: 3,
  [MediaType.selfie_photo]: 1,
  [MediaType.garage_photo]: 1,
  [MediaType.curp]: 1,
};

export const FileUploadTotalFilesByMediaTypeCatalogUS = {
  [MediaType.drivers_license_front]: 1,
  [MediaType.drivers_license_back]: 1,
  [MediaType.proof_of_address]: 1,
  [MediaType.bank_statement]: 6,
  [MediaType.proof_of_completion_of_safety_courses]: 1,
};

export const mexicanStates = [
  "Aguascalientes",
  "Baja California",
  "Baja California Sur",
  "Campeche",
  "Chiapas",
  "Chihuahua",
  "Ciudad de México",
  "Coahuila",
  "Colima",
  "Durango",
  "Estado de México",
  "Guanajuato",
  "Guerrero",
  "Hidalgo",
  "Jalisco",
  "Michoacán",
  "Morelos",
  "Nayarit",
  "Nuevo León",
  "Oaxaca",
  "Puebla",
  "Querétaro",
  "Quintana Roo",
  "San Luis Potosí",
  "Sinaloa",
  "Sonora",
  "Tabasco",
  "Tamaulipas",
  "Tlaxcala",
  "Veracruz",
  "Yucatán",
  "Zacatecas",
];

export const unknownError =
  "Ocurrió un error inesperado, por favor intenta más tarde";
