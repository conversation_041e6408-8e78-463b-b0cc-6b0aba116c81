import * as Yup from "yup";

const curpRegExp = /^[A-Z]{4}\d{6}[HM][A-Z]{5}[A-Z0-9]\d$/;
const rfcRegExp = /^[A-Z]{4}[0-9]{6}[A-Z0-9]{3}$/;
const postalCodeRegExp = /^[0-9]{5}$/;
export const ssnRegExp = /^\d{3}-\d{2}-\d{4}$/; // us field

export const personalDataValidationSchema = Yup.object().shape({
  birthdate: Yup.date()
    .required("Por favor, ingresa tu fecha de nacimiento válida")
    .test({
      name: "age-validation",
      message: "Age must be greater than or equal to 18",
      test: (value) => {
        if (value === null) return false;
        const date = new Date(value as unknown as Date);
        const now = new Date();
        const diff = now.getFullYear() - date.getFullYear();
        return diff >= 18;
      },
    }),
  taxId: Yup.string()
    .matches(rfcRegExp, "Por favor, ingresa un RFC válido")
    .required("Por favor, ingresa tu RFC"),
  nationalId: Yup.string()
    .matches(curpRegExp, "Por favor, ingresa un CURP válido")
    .required("Por favor, ingresa tu CURP"),
  postalCode: Yup.string()
    .matches(postalCodeRegExp, "Por favor, ingresa un código postal válido")
    .required("Por favor, ingresa tu código postal"),
  city: Yup.string()
    .min(3, "Por favor, ingresa una ciudad válida")
    .required("Por favor, ingresa tu ciudad"),
  state: Yup.string().required("Por favor, selecciona un estado"),
  neighborhood: Yup.string()
    .min(3, "Por favor, ingresa una colonia válida")
    .required("Por favor, ingresa tu colonia"),
  street: Yup.string()
    .min(3, "Por favor, ingresa una calle válida")
    .required("Por favor, ingresa tu calle"),
  streetNumber: Yup.string()
    .min(1, "Por favor, ingresa un número exterior válido")
    .required("Por favor, ingresa tu número exterior"),
});

export const customerValidationSchema = Yup.object().shape({
  firstName: Yup.string()
    .min(3, "FirstNameMinMsg")
    .required("FirstNameRequired"),
  lastName: Yup.string().min(3, "LastNameMinMsg").required("LastNameRequired"),
  email: Yup.string().email("EmailValidMsg").required("EmailRequired"),
  phone: Yup.string()
    .min(10, "PhoneNumberMin")
    .max(14, "PhoneNumberMax")
    .required("PhoneNumberRequired"),
});

export const personalDataValidationSchemaUS = Yup.object().shape({
  birthdate: Yup.string()
    .test({
      name: "age-validation",
      message: "Age must be greater than 21",
      test: (value) => {
        const date = new Date(value as unknown as Date);
        const now = new Date();
        const diff = now.getFullYear() - date.getFullYear();
        return diff >= 21;
      },
    })
    .required("birthdate is required"),
  postalCode: Yup.string()
    .matches(postalCodeRegExp, "Please enter a valid zip code")
    .required("Please enter your zip code"),

  ssn: Yup.string()
    .required("This field is required")
    .matches(ssnRegExp, "Invalid SSN, format should be xxx-xx-xxxx"),
  city: Yup.string().required("Please select your city"),
  state: Yup.string().required("Please select a state"),
  street: Yup.string()
    .min(1, "Please enter a valid Street address")
    .required("Please enter your Street address"),
  streetNumber: Yup.string().optional(),
  rideShareTotalRides: Yup.number()
    .nullable()
    .typeError("Ride Share Total Rides is a number")
    .required("Ride Share Total Rides is required"),
  avgEarningPerWeek: Yup.number()
    .nullable()
    .typeError("Avg Earning Per Week is a number")
    .required("Avg Earning Per Week is required"),
  ocnBackgroundAndCreditCheckForApplication: Yup.boolean().oneOf(
    [true],
    "You must accept the OCN background and credit check for application",
  ),
  privacyPolicy: Yup.boolean().oneOf([true], "You must accept privacy policy"),
});

export const calendarAppointmentValidationSchema = Yup.object().shape({
  firstName: Yup.string(),
  lastName: Yup.string(),
  email: Yup.string().email("EmailValidMsg"),
  phone: Yup.string(),
});

export const solidarityObligorDetailsValidationSchema = Yup.object().shape({
  name: Yup.string().required("Este campo es obligatorio"),
  phone: Yup.string()
    .min(10, "PhoneNumberMin")
    .max(14, "PhoneNumberMax")
    .required("Este campo es obligatorio"),
  email: Yup.string()
    .email("EmailValidMsg")
    .required("TEste campo es obligatorio"),
  location: Yup.string().required("Este campo es obligatorio"),
});
