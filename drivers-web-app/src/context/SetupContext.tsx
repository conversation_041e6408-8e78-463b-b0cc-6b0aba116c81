import { <PERSON><PERSON>, Spinner } from "@chakra-ui/react";
import { AdmissionRequestController } from "core/controllers/admissionRequest";
import { AdmissionRequest } from "core/domain/entities";
import { AdmissionRequestStatus, HomeVisitStatus } from "core/domain/enums";
import { getIdFromPath, getSearchParams } from "core/misc/searchParams";
import { Languages } from "i18n/langs";
import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { CountriesShortNames } from "utils";

export interface ISetupContext {
  isLoading: boolean;
  admissionRequest: AdmissionRequest | null;
  retrieveAdmissionRequest: () => void;
  checkEarningsAnalysis: () => void;
  setAdmissionRequest: (admissionRequest: AdmissionRequest | null) => void;
  isCountryUSA: boolean;
  addSkipScreen: (screenName: string) => void;
  skipScreens: string[];
  clearSkipScreens: () => void;
  isDocumentOptional: boolean;
}

const SetupContext = createContext({} as ISetupContext);

export const SetupProvider = ({ children }: { children: ReactNode }) => {
  const navigate = useNavigate();
  const { i18n } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [admissionRequest, setAdmissionRequest] =
    useState<AdmissionRequest | null>(null);
  const mounted = useRef(false);
  const [skipScreens, setSkipScreens] = useState<string[]>([]);
  const [isDocumentOptional, setIsDocumentOptional] = useState<boolean>(true);

  const admissionRequestController = useMemo(() => {
    return new AdmissionRequestController();
  }, []);

  async function retrieveAdmissionRequest() {
    const id = admissionRequest?.id ?? "";
    const [err, data] =
      await admissionRequestController.getAdmissionRequest(id);

    if (!err) {
      setAdmissionRequest(data);
    }
  }

  const redirectBasedOnStatus = useCallback(
    (status: AdmissionRequestStatus) => {
      const statusRedirect = {
        [AdmissionRequestStatus.created]: "/welcome",
        [AdmissionRequestStatus.documents_analysis]: "/personal-data",
        [AdmissionRequestStatus.earnings_analysis]: "/earnings-analysis",
      };

      if (location.pathname.includes("schedule-home-visit")) {
        navigate("/schedule-home-visit");
        return;
      }
      if (status === AdmissionRequestStatus.home_visit) {
        if (location.pathname.includes("user-info-gathering")) {
          navigate("/user-info-gathering");
          return;
        } else if (location.pathname.includes("home-image-upload")) {
          navigate("/home-image-upload");
          return;
        }
      }

      const lastRoute = "/documents-success";

      const redirect = statusRedirect[status] || lastRoute;

      if (redirect) {
        navigate(redirect);
      }
    },
    [navigate],
  );

  async function checkEarningsAnalysis() {
    const id = admissionRequest?.id ?? "";
    const [err, data] =
      await admissionRequestController.checkEarningsAnalysis(id);

    if (!err) {
      redirectBasedOnStatus(data.status);
    }
  }

  useEffect(() => {
    async function getAdmissionRequest() {
      const requestIdFromPath = getIdFromPath(location.href);
      const searchParams = getSearchParams();
      const { id, account_id, skipPalenca } = searchParams;
      if (account_id) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const [_err, data] =
          await admissionRequestController.getAdmissionRequest(
            id || requestIdFromPath,
          );
        if (data) {
          setAdmissionRequest(data);
        }
        navigate("/palenca-verify");
        setIsLoading(false);
        return;
      }
      if (id && skipPalenca) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const [_err, data] =
          await admissionRequestController.getAdmissionRequest(
            id || requestIdFromPath,
          );
        if (data) {
          setAdmissionRequest(data);
        }
        navigate("/platforms");
        setIsLoading(false);
        return;
      }
      if (!id && !requestIdFromPath) {
        navigate("/errors/request-not-found");
        setIsLoading(false);
      } else {
        const [err, data] =
          await admissionRequestController.getAdmissionRequest(
            id || requestIdFromPath,
          );
        if (data) {
          redirectBasedOnStatus(data.status);
        }

        if (err) {
          if (err.details.code === "admission_request_not_found") {
            navigate("/errors/request-not-found");
          } else {
            navigate("/errors/server");
          }
        }

        const isCountryUSA =
          data?.personalData?.country === CountriesShortNames["United States"];
        if (isCountryUSA) {
          i18n.changeLanguage(Languages.en);
        }

        setAdmissionRequest(data);
        setIsDocumentOptional(
          (data &&
            data.homeVisit &&
            data?.homeVisit?.status !== HomeVisitStatus.approved) ??
            true,
        );
        setIsLoading(false);
      }
    }
    if (!mounted.current) {
      getAdmissionRequest();
      mounted.current = true;
    }
  }, [admissionRequestController, i18n, navigate, redirectBasedOnStatus]);

  const addSkipScreen = (screenName: string) => {
    setSkipScreens([screenName, ...skipScreens]);
  };

  const clearSkipScreens = () => {
    setSkipScreens([]);
  };

  return (
    <SetupContext.Provider
      value={{
        isLoading,
        admissionRequest,
        retrieveAdmissionRequest,
        checkEarningsAnalysis,
        setAdmissionRequest,
        isCountryUSA:
          admissionRequest?.personalData?.country ===
          CountriesShortNames["United States"],
        addSkipScreen,
        skipScreens,
        clearSkipScreens,
        isDocumentOptional,
      }}
    >
      {children}
    </SetupContext.Provider>
  );
};

export const useSetup = () => useContext(SetupContext);

export const ProtectRoute = ({ children }: { children: React.ReactNode }) => {
  const { isLoading } = useSetup();

  if (isLoading) {
    return (
      <Flex h="100vh" align="center" justify="center" w="100vw">
        <Spinner
          speed="0.65s"
          thickness="3px"
          emptyColor="gray.100"
          color="primary"
          size="xl"
        />
      </Flex>
    );
  }

  return children;
};
