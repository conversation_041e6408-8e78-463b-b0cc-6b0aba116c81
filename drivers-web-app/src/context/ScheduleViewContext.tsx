import {
  AdmissionRequestController,
  IAdmissionRequest,
  IAppointment,
} from "core/controllers/admissionRequest";
import { getIdFromPath } from "core/misc/searchParams";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

interface IScheduleViewContext {
  step: number;
  setStep: (step: number) => void;
  appointment: IAppointment | null;
  setAppointment: (appointment: IAppointment | null) => void;
  admissionRequest: IAdmissionRequest | undefined;
  isStepReschedule: boolean;
}

export const Steps = {
  Date: 1,
  Time: 2,
  Form: 3,
  Reschedule: 4,
  Cancel: 5,
  Success: 6,
};

const ScheduleViewContext = createContext<IScheduleViewContext>(
  null as unknown as IScheduleViewContext,
);

export const useScheduleView = () => {
  return useContext(ScheduleViewContext);
};

interface IScheduleViewContextProvider {
  children: ReactNode;
}

export const ScheduleViewContextProvider = (
  props: IScheduleViewContextProvider,
) => {
  const searchParams = new URLSearchParams(window.location.search);
  const searchStep = searchParams.get("step");

  const stepNum = searchStep === "reschedule" ? Steps.Reschedule : Steps.Date;

  const [isStepReschedule] = useState<boolean>(searchStep === "reschedule");

  const [step, setStep] = useState<number>(stepNum);
  const [appointment, setAppointment] = useState<IAppointment | null>(null);

  useEffect(() => {
    const admissionRequestController = new AdmissionRequestController();
    const fetchAppointment = async (): Promise<void> => {
      const requestId = getIdFromPath(location.href);
      const [, appointment, response] =
        await admissionRequestController.getHomeVisitAppointment(requestId);
      if (response?.success) {
        setAppointment(appointment);
      }
    };
    if (!appointment) {
      fetchAppointment();
    }
  }, [appointment]);

  const [admissionRequest, setAdmissionRequest] = useState<
    IAdmissionRequest | undefined
  >();

  useEffect(() => {
    const admissionRequestController = new AdmissionRequestController();
    async function getAdmissionRequest() {
      const requestId = getIdFromPath(location.href);
      const [, data, response] =
        await admissionRequestController.getAdmissionRequest(requestId);
      if (response?.success) {
        setAdmissionRequest(data);
      }
    }
    getAdmissionRequest();
  }, []);

  return (
    <ScheduleViewContext.Provider
      value={{
        step,
        setStep,
        appointment,
        setAppointment,
        admissionRequest,
        isStepReschedule,
      }}
    >
      {props.children}
    </ScheduleViewContext.Provider>
  );
};
