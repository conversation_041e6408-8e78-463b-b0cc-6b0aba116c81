
> vendor-platform@0.1.41 build
> next build

  ▲ Next.js 14.2.8
  - Environments: .env

   Creating an optimized production build ...
Browserslist: caniuse-lite is outdated. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
 ✓ Compiled successfully
   Linting and checking validity of types ...
 ⚠ No ESLint configuration detected. Run next lint to begin setup
   Collecting page data ...
   Generating static pages (0/10) ...
   Generating static pages (2/10) 
   Generating static pages (4/10) 
   Generating static pages (7/10) 
 ✓ Generating static pages (10/10)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                                                                            Size     First Load JS
┌ ○ /_not-found                                                                        879 B          88.4 kB
├ ƒ /[locale]                                                                          4.26 kB         151 kB
├ ƒ /[locale]/accept-invitation                                                        4.28 kB         213 kB
├ ƒ /[locale]/changePassword                                                           3.63 kB         213 kB
├ ƒ /[locale]/dashboard                                                                165 B          87.7 kB
├ ƒ /[locale]/dashboard/administrador/aprobacion-cotizaciones                          8.5 kB          110 kB
├ ƒ /[locale]/dashboard/administrador/gestores/tramites                                16.4 kB         235 kB
├ ƒ /[locale]/dashboard/administrador/verificaciones                                   7.66 kB         507 kB
├ ƒ /[locale]/dashboard/appointments                                                   20.3 kB         418 kB
├ ƒ /[locale]/dashboard/cities                                                         592 B           191 kB
├ ƒ /[locale]/dashboard/cities/[cityId]                                                2.6 kB          193 kB
├ ƒ /[locale]/dashboard/cities/[cityId]/crews/[crewId]                                 4.83 kB         171 kB
├ ƒ /[locale]/dashboard/cities/[cityId]/crews/[crewId]/neighborhoods/[neighborhoodId]  3.78 kB         256 kB
├ ƒ /[locale]/dashboard/company-users                                                  7.26 kB         189 kB
├ ƒ /[locale]/dashboard/corrective-maintenance                                         3.21 kB         180 kB
├ ƒ /[locale]/dashboard/corrective-maintenance/[orderId]                               37.1 kB         231 kB
├ ƒ /[locale]/dashboard/corrective-maintenance/dashboard                               11.9 kB         191 kB
├ ƒ /[locale]/dashboard/gestor                                                         2.58 kB         109 kB
├ ƒ /[locale]/dashboard/gestor/inicio                                                  884 B           113 kB
├ ƒ /[locale]/dashboard/gestor/tramites                                                2.05 kB         176 kB
├ ƒ /[locale]/dashboard/gestor/tramites/[cityId]                                       121 kB          325 kB
├ ƒ /[locale]/dashboard/home                                                           9.78 kB         125 kB
├ ƒ /[locale]/dashboard/installation-list                                              31.9 kB         452 kB
├ ƒ /[locale]/dashboard/installation-list-old-(ignore)                                 2.94 kB         133 kB
├ ƒ /[locale]/dashboard/installation-schedule                                          2.59 kB         385 kB
├ ƒ /[locale]/dashboard/installation-schedule/export                                   98.3 kB         321 kB
├ ƒ /[locale]/dashboard/main                                                           164 B          87.7 kB
├ ƒ /[locale]/dashboard/perfil                                                         10.2 kB         233 kB
├ ƒ /[locale]/dashboard/services                                                       5.41 kB         298 kB
├ ƒ /[locale]/dashboard/vehicles                                                       12.2 kB         116 kB
├ ƒ /[locale]/dashboard/vehicles/[stockId]                                             26.4 kB         354 kB
├ ƒ /[locale]/dashboard/vehicles/[stockId]/verificacion                                4.94 kB         508 kB
├ ƒ /[locale]/dashboard/verificaciones                                                 7.19 kB         177 kB
├ ƒ /[locale]/dashboard/verificaciones/centros                                         11.9 kB         457 kB
├ ƒ /[locale]/dashboard/verificaciones/dashboard                                       7.25 kB         402 kB
├ ƒ /[locale]/recover-password                                                         4.2 kB          392 kB
├ ƒ /[locale]/send-pdf                                                                 477 kB          583 kB
├ ƒ /[locale]/send-pdf/api                                                             0 B                0 B
├ ƒ /[locale]/verification/[uniqueLink]                                                6.64 kB         447 kB
├ ƒ /[locale]/verification/customer/[token]                                            6.98 kB         473 kB
├ ƒ /api/auth/[...nextauth]                                                            0 B                0 B
├ ƒ /api/auth/user                                                                     0 B                0 B
├ ƒ /api/corrective-maintenance/inventory/check-availability                           0 B                0 B
├ ƒ /api/corrective-maintenance/orders/[orderId]                                       0 B                0 B
├ ƒ /api/corrective-maintenance/orders/[orderId]/progress                              0 B                0 B
├ ƒ /api/corrective-maintenance/orders/[orderId]/quotation                             0 B                0 B
├ ƒ /api/corrective-maintenance/services/[serviceId]/parts                             0 B                0 B
├ ƒ /api/corrective-maintenance/services/[serviceId]/progress                          0 B                0 B
├ ƒ /api/corrective-maintenance/services/[serviceId]/start-tracking                    0 B                0 B
├ ƒ /api/debug/corrective-maintenance/[orderId]                                        0 B                0 B
├ ƒ /api/download-file                                                                 0 B                0 B
├ ƒ /api/notifications/slack/send                                                      0 B                0 B
├ ƒ /api/revalidate-paths                                                              0 B                0 B
├ ƒ /api/vehicles/[vehicleId]/qr-scan                                                  0 B                0 B
├ ƒ /api/vehicles/[vehicleId]/qr-status-change                                         0 B                0 B
├ ƒ /api/vendor-platform/corrective-maintenance/orders                                 0 B                0 B
├ ƒ /api/vendor-platform/vehicles/[vehicleId]                                          0 B                0 B
├ ƒ /api/verification                                                                  0 B                0 B
├ ƒ /api/verification/centers                                                          0 B                0 B
├ ƒ /api/verification/create                                                           0 B                0 B
├ ƒ /api/verification/customer/[token]                                                 0 B                0 B
└ ƒ /api/verification/search-vehicle                                                   0 B                0 B
+ First Load JS shared by all                                                          87.5 kB
  ├ chunks/7023-1da68ed34f1a280a.js                                                    31.7 kB
  ├ chunks/fd9d1056-8b893d7fd403209e.js                                                53.6 kB
  └ other shared chunks (total)                                                        2.2 kB


ƒ Middleware                                                                           26.8 kB

○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

