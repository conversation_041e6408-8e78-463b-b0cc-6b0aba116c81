"use server"

import { getSession } from "@/actions/getUserById"
import { apiCompanyPlatform, companyService, type UserPermissions, type CompanyUser } from "@/constants/companyService"
import { CompanyUserRole, CompanyUserRoleType } from "./user-types"
import { revalidatePath } from "next/cache"

async function setHeaders() {
  try {
    const session = await getSession();
    if (session?.user?.accessToken) {
      apiCompanyPlatform.defaults.headers.common['Authorization'] = `Bearer ${session.user.accessToken}`;
      apiCompanyPlatform.defaults.headers.common['Content-Type'] = 'application/json';
    } else {
      console.error("No access token found in session");
      throw new Error("No access token available");
    }

    apiCompanyPlatform.defaults.headers.common['Authorization'] = `Bearer ${session.user.accessToken}`;
    apiCompanyPlatform.defaults.headers.common['Content-Type'] = 'application/json';
  } catch (error) {
    console.error("Error setting headers:", error);
    throw error;
  }
}

// Get all users
export async function getUsers(companyId: string) {
  try {
    if (!companyId) {
      console.error("Invalid companyId:", companyId);
      throw new Error("Invalid company ID");
    }

    await setHeaders();

    // Get session to check if we have a valid token
    const session = await getSession();
    if (!session?.user?.accessToken) {
      console.error("No access token available. User might not be logged in.");
      return [];
    }

    // Try to get users from the company endpoint
    const response = await companyService.getUsersByCompany(companyId);

    if (!response.data) {
      console.error("Invalid response data:", response);
      throw new Error("Invalid response from server");
    }

    // Create an array to store all users
    let allUsers = [...response.data];

    // Try to get all users to find the company owner
    try {
      const allUsersResponse = await companyService.getAllUsers();

      if (allUsersResponse.data && Array.isArray(allUsersResponse.data)) {

        // Filter users that belong to this company but are not in the response
        const additionalUsers = allUsersResponse.data.filter(user => {
          // Check if user belongs to this company
          const userBelongsToCompany =
            (user as any).organizationId === companyId ||
            (user as any).companyId === companyId;

          // Check if user is not already in the response
          const userNotInResponse = !response.data.some(
            (respUser: any) => respUser._id === user._id
          );

          return userBelongsToCompany && userNotInResponse;
        });

        if (additionalUsers.length > 0) {
          allUsers = [...allUsers, ...additionalUsers];
        }
      }
    } catch (error) {
      console.error("Error fetching all users:", error);
    }

    // If no users found, include the current user
    if (allUsers.length === 0) {

      // Get the current session to check if we can include the current user
      if (session?.user) {
        const currentUser = session.user;

        // Check if the current user belongs to this company
        if ((currentUser as any).organizationId === companyId) {
          // Create a user object with the current user's data
          return [{
            id: currentUser._id || (currentUser as any).sub,
            role: (currentUser as any).role,
            userId: currentUser._id || (currentUser as any).sub,
            name: currentUser.name,
            email: currentUser.email,
            status: (currentUser as any).status,
            companyId: companyId,
            created: new Date((currentUser as any).created || Date.now()),
            updated: new Date((currentUser as any).updated || Date.now()),
            allowedCities: [],
            allowedCrews: [],
          }];
        }
      }

      return [];
    }

    // Transform all users to match the expected format
    return allUsers.map((user: CompanyUser) => {
      try {
        return {
          id: user._id,
          role: (user as any).permissions?.role || (user as any).role || 'operator',
          userId: user._id,
          name: user.name,
          email: user.email,
          // Use permission status for company users
          status: user.status,
          companyId: companyId,
          created: new Date(user.createdAt || Date.now()),
          updated: new Date(user.updatedAt || Date.now()),
          allowedCities: (user as any).permissions?.allowedCities || [],
          allowedCrews: (user as any).permissions?.allowedCrews || [],
          permissions: (user as any).permissions || null,
        };
      } catch (err) {
        console.error("Error transforming user:", user, err);
        return null;
      }
    }).filter(Boolean);
  } catch (error: any) {
    console.error("Error fetching users:", error);
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", error.response.data);
    }
    throw error;
  }
}

// Invite a new user
export async function inviteUser(userData: {
  name: string
  email: string
  role: CompanyUserRole
  allowedCities: string[]
  allowedCrews: string[]
  companyId: string
}): Promise<UserPermissions> {
  await setHeaders()
  try {
    const response = await companyService.inviteUserToCompany(userData.companyId, {
      email: userData.email,
      name: userData.name,
      role: userData.role,
    })

    // After creating the user, update their permissions
    await companyService.updateUserCompanyPermissions(
      userData.companyId,
      response.data._id,
      {
        role: userData.role,
        allowedCities: userData.allowedCities,
        allowedCrews: userData.allowedCrews,
      }
    )

    revalidatePath("/usuarios")
    return response.data
  } catch (error: any) {
    console.error("Error inviting user:", error.response?.data || error)
    throw error
  }
}

// Update user
export async function updateUser(
  id: string,
  userData: {
    name: string
    email: string
    role: CompanyUserRole
    companyId: string
  }
): Promise<UserPermissions | undefined> {
  await setHeaders()
  try {
    const response = await companyService.updateUserCompanyPermissions(
      userData.companyId,
      id,
      {
        role: userData.role,
        allowedCities: [], // Maintain existing values
        allowedCrews: [], // Maintain existing values
      }
    )

    revalidatePath("/usuarios")
    return response.data
  } catch (error: any) {
    console.error("Error updating user:", error.response?.data || error)
    return undefined
  }
}

// Update user permissions
export async function updateUserPermissions(
  id: string,
  permissionsData: {
    role: CompanyUserRole
    allowedCities: string[]
    allowedCrews: string[]
    companyId: string
  }
): Promise<UserPermissions | undefined> {
  await setHeaders()
  try {
    const response = await companyService.updateUserCompanyPermissions(
      permissionsData.companyId,
      id,
      {
        role: permissionsData.role,
        allowedCities: permissionsData.allowedCities,
        allowedCrews: permissionsData.allowedCrews
      }
    )

    revalidatePath("/usuarios")
    return response.data
  } catch (error: any) {
    console.error("Error updating user permissions:", error.response?.data || error)
    return undefined
  }
}

// Change user status
export async function changeUserStatus(
  id: string,
  status: "active" | "invited" | "suspended",
  companyId: string
): Promise<UserPermissions | undefined> {
  await setHeaders()
  try {
    // Since there's no direct status update endpoint, we'll update the permissions
    // while maintaining the existing role and permissions
    const currentUser = await companyService.getUserCompanyPermissions(companyId, id)

    const response = await companyService.updateUserCompanyPermissions(
      companyId,
      id,
      {
        role: currentUser.data.role,
        allowedCities: currentUser.data.allowedCities.map(city => city._id),
        allowedCrews: currentUser.data.allowedCrews.map(crew => crew._id),
        // status: status,
      }
    )

    revalidatePath("/usuarios")
    return response.data
  } catch (error: any) {
    console.error("Error changing user status:", error.response?.data || error)
    return undefined
  }
}