import { VerificationCenter, VerificationRecord, VerificationHistory } from '@/types';
import getCurrentUser from '@/actions/getCurrentUser';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
const EMISSIONS_API_BASE = '/vendor-platform/emissions-verification';

// Validar que la variable de entorno esté definida
if (!API_BASE_URL) {
  throw new Error('NEXT_PUBLIC_API_URL environment variable is not defined');
}

// Función auxiliar para verificar permisos de admin
function hasAdminPermissions(userType: string): boolean {
  return userType === 'superAdmin' || userType === 'company-gestor';
}

export async function getVerificationCenters(state?: 'CDMX' | 'EDOMEX'): Promise<VerificationCenter[]> {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Permitir acceso a superAdmin, workshop y gestores
    if (!['superAdmin', 'workshop', 'gestor'].includes(user.userType)) {
      throw new Error('User not authorized for verification operations');
    }

    const url = new URL(`${EMISSIONS_API_BASE}/vendor/verification-centers`, API_BASE_URL);
    if (state) url.searchParams.set('state', state);

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch verification centers: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching verification centers:', error);
    throw error;
  }
}

export async function getVerificationHistory(
  vehicleId?: string,
  plateNumber?: string
): Promise<VerificationHistory | null> {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    if (!vehicleId && !plateNumber) {
      throw new Error('Either vehicleId or plateNumber is required');
    }

    const url = new URL('/api/verification', API_BASE_URL);
    if (vehicleId) url.searchParams.set('vehicleId', vehicleId);
    if (plateNumber) url.searchParams.set('plateNumber', plateNumber);

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error(`Failed to fetch verification history: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching verification history:', error);
    throw error;
  }
}

export async function createVerificationRecord(data: {
  vehiclePlate: string;
  verificationCenterId: string;
  verificationDate: string;
  vehiclePhoto?: string;
  circulationCard?: string;
}): Promise<VerificationRecord> {
  try {
    console.log('🔑 Getting current user...');
    const user = await getCurrentUser();
    console.log('🔑 Current user:', user ? `${user.userType} (${user.email})` : 'null');

    if (!user) throw new Error('User not authenticated');

    // Permitir acceso a superAdmin, workshop y gestores
    if (!['superAdmin', 'workshop', 'gestor'].includes(user.userType)) {
      throw new Error('User not authorized for verification operations');
    }

    console.log('📤 Making API request to:', `${API_BASE_URL}${EMISSIONS_API_BASE}/vendor/verifications`);
    console.log('📤 Request data:', data);

    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/vendor/verifications`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    console.log('📤 Response status:', response.status);
    console.log('📤 Response ok:', response.ok);

    if (!response.ok) {
      const responseText = await response.text();
      console.log('❌ Response error text:', responseText);
      throw new Error(`Failed to create verification record: ${response.statusText} - ${responseText}`);
    }

    const result = await response.json();
    console.log('✅ API Response result:', result);
    return result.data;
  } catch (error) {
    console.error('❌ Error creating verification record:', error);
    throw error;
  }
}

export async function updateVerificationRecord(
  verificationId: string,
  updates: Partial<VerificationRecord>
): Promise<VerificationRecord> {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const response = await fetch(`/api/verification/${verificationId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      throw new Error(`Failed to update verification record: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating verification record:', error);
    throw error;
  }
}

export async function getCustomerVerificationData(uniqueLink: string) {
  try {
    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/customer/${uniqueLink}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error(`Failed to fetch customer verification data: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching customer verification data:', error);
    throw error;
  }
}

export async function updateCustomerVerification(
  uniqueLink: string,
  data: {
    verificationCertificate?: string;
    hologramType: '00' | '0' | '1' | '2';
    hologramPhoto?: string;
    isExempt?: boolean;
    customerNotes?: string;
  }
) {
  try {
    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/customer/${uniqueLink}/evidence`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to update customer verification: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error updating customer verification:', error);
    throw error;
  }
}

export async function searchVehicleByPlate(plateNumber: string) {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Permitir acceso a superAdmin, workshop y gestores
    if (!['superAdmin', 'workshop', 'gestor'].includes(user.userType)) {
      throw new Error('User not authorized for verification operations');
    }

    const cleanPlate = plateNumber.replace(/\s/g, '').toUpperCase();

    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/vendor/search-vehicle/${cleanPlate}`, {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error(`Failed to search vehicle: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error searching vehicle by plate:', error);
    throw error;
  }
}

export async function createVerificationCenter(data: {
  name: string;
  code: string;
  state: 'CDMX' | 'EDOMEX';
  organizationId: string;
  location: {
    address: string;
    city: string;
    state: string;
  };
  authorizedFor: string[];
}): Promise<VerificationCenter> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('❌ No se pudo obtener el usuario actual');
      throw new Error('User not authenticated');
    }

    console.log('🔍 Usuario actual:', {
      id: user._id,
      email: user.email,
      userType: user.userType,
      hasToken: !!user.accessToken
    });

    // Solo usuarios con permisos de admin pueden crear centros de verificación
    if (!hasAdminPermissions(user.userType)) {
      console.error('❌ Usuario sin permisos de admin:', {
        userType: user.userType,
        allowedTypes: ['superAdmin', 'company-gestor']
      });
      throw new Error('User not authorized for admin operations');
    }

    console.log('📡 Enviando petición a:', `${API_BASE_URL}${EMISSIONS_API_BASE}/admin/verification-centers`);
    console.log('📦 Datos enviados:', data);

    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/admin/verification-centers`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
        'adpt': 'true',
      },
      body: JSON.stringify(data),
    });

    console.log('📡 Respuesta recibida:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error de la API:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to create verification center: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error creating verification center:', error);
    throw error;
  }
}

// Función para obtener verificaciones de un centro específico
export async function getCenterVerifications(
  centerId: string,
  options?: {
    page?: number;
    limit?: number;
    status?: string;
  }
) {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Permitir acceso a superAdmin, workshop y gestores
    if (!['superAdmin', 'workshop', 'gestor'].includes(user.userType)) {
      throw new Error('User not authorized for verification operations');
    }

    const url = new URL(`${EMISSIONS_API_BASE}/vendor/centers/${centerId}/verifications`, API_BASE_URL);
    if (options?.page) url.searchParams.set('page', options.page.toString());
    if (options?.limit) url.searchParams.set('limit', options.limit.toString());
    if (options?.status) url.searchParams.set('status', options.status);

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch center verifications: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching center verifications:', error);
    throw error;
  }
}

// Función para obtener estadísticas de un centro
export async function getCenterStats(centerId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Permitir acceso a superAdmin, workshop y gestores
    if (!['superAdmin', 'workshop', 'gestor'].includes(user.userType)) {
      throw new Error('User not authorized for verification operations');
    }

    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/vendor/centers/${centerId}/stats`, {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch center stats: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching center stats:', error);
    throw error;
  }
}

// Función para obtener opciones de hologramas (customer)
export async function getHologramOptions() {
  try {
    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/customer/hologram-options/list`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch hologram options: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching hologram options:', error);
    throw error;
  }
}

// Función para validar estado de completitud (customer)
export async function getCompletionStatus(uniqueLink: string) {
  try {
    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/customer/${uniqueLink}/completion-status`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch completion status: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching completion status:', error);
    throw error;
  }
}

// ADMIN ENDPOINTS

// Función para obtener dashboard de estadísticas (admin)
export async function getAdminDashboard() {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Solo superAdmin puede acceder al dashboard administrativo
    if (user.userType !== 'superAdmin') {
      throw new Error('User not authorized for admin operations');
    }

    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/admin/dashboard`, {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
        'adpt': 'true',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch admin dashboard: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching admin dashboard:', error);
    throw error;
  }
}

// Función para obtener todas las verificaciones (admin)
export async function getAllVerifications(options?: {
  page?: number;
  limit?: number;
  status?: string;
  search?: string;
}) {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Solo superAdmin puede ver todas las verificaciones
    if (user.userType !== 'superAdmin') {
      throw new Error('User not authorized for admin operations');
    }

    const url = new URL(`${EMISSIONS_API_BASE}/admin/verifications`, API_BASE_URL);
    if (options?.page) url.searchParams.set('page', options.page.toString());
    if (options?.limit) url.searchParams.set('limit', options.limit.toString());
    if (options?.status) url.searchParams.set('status', options.status);
    if (options?.search) url.searchParams.set('search', options.search);

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
        'adpt': 'true',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch all verifications: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching all verifications:', error);
    throw error;
  }
}

// Función para subir verificación pasada (admin)
export async function uploadPastVerification(data: {
  vehiclePlate: string;
  verificationCenterId: string;
  verificationDate: string;
  hologramType: string;
  verificationCertificate?: string;
  hologramPhoto?: string;
  notes?: string;
}) {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Solo superAdmin puede subir verificaciones pasadas
    if (user.userType !== 'superAdmin') {
      throw new Error('User not authorized for admin operations');
    }

    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/admin/verifications/past`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
        'adpt': 'true',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to upload past verification: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error uploading past verification:', error);
    throw error;
  }
}

// Función para obtener historial completo de vehículo (admin)
export async function getVehicleFullHistory(plateNumber: string) {
  try {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Solo superAdmin puede ver historial completo
    if (user.userType !== 'superAdmin') {
      throw new Error('User not authorized for admin operations');
    }

    const response = await fetch(`${API_BASE_URL}${EMISSIONS_API_BASE}/admin/vehicles/${plateNumber}/full-history`, {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
        'adpt': 'true',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch vehicle full history: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching vehicle full history:', error);
    throw error;
  }
}