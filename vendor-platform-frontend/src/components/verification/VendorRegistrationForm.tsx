'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Button,
  Card,
  CardHeader,
  CardBody,
  Heading,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Alert,
  AlertIcon,
  Stepper,
  Step,
  StepIndicator,
  StepStatus,
  StepIcon,
  StepNumber,
  StepTitle,
  StepDescription,
  StepSeparator,
  useToast,
  Badge,
  Divider,
  SimpleGrid,
  useColorModeValue,
  Spinner,
} from '@chakra-ui/react';
import { FiSearch, FiCheck, FiUpload, FiSend } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FileUploader } from './FileUploader';
import { createVerificationRecord } from '@/actions/verification';
// Define VerificationCenter type here if not exported from '@/types'
interface VerificationCenter {
  _id: string;
  code: string;
  name: string;
  state: string;
  location?: {
    address?: string;
  };
}

interface VehicleData {
  _id: string;
  plates: string[];
  brand: string;
  model: string;
  year: number;
  color: string;
  isHybrid: boolean;
  isElectric: boolean;
  registrationDate: string;
}

export function VendorRegistrationForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const [plateNumber, setPlateNumber] = useState('');
  const [selectedVehicle, setSelectedVehicle] = useState<VehicleData | null>(null);
  const [selectedCenter, setSelectedCenter] = useState('');
  const [verificationDate, setVerificationDate] = useState(() => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  });
  const [vehiclePhotoFile, setVehiclePhotoFile] = useState<File | null>(null);
  const [circulationCardFile, setCirculationCardFile] = useState<File | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const bgColor = useColorModeValue('white', 'gray.800');
  const toast = useToast();
  const queryClient = useQueryClient();

  // Fetch verification centers
  const { data: centers } = useQuery({
    queryKey: ['verification-centers'],
    queryFn: async () => {
      const response = await fetch('/api/verification/centers');
      if (!response.ok) {
        throw new Error('Failed to fetch verification centers');
      }
      return response.json();
    },
  });

  // Create verification mutation
  const createVerificationMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch('/api/verification/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al crear verificación');
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: (result) => {
      toast({
        title: 'Verificación registrada exitosamente',
        description: `Link del cliente: ${result.customerLink || 'Verificación creada'}`,
        status: 'success',
        duration: 10000,
        isClosable: true,
      });

      // Reset form
      handleReset();

      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: ['all-verifications'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error al registrar verificación',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const steps = [
    {
      title: 'Buscar Vehículo',
      description: 'Ingresa la placa del vehículo'
    },
    {
      title: 'Seleccionar Centro',
      description: 'Elige el centro de verificación'
    },
    {
      title: 'Subir Documentos',
      description: 'Foto del vehículo y tarjeta de circulación'
    },
    {
      title: 'Confirmar',
      description: 'Revisar y enviar registro'
    }
  ];

  const handleSearchVehicle = async () => {
    if (!plateNumber.trim()) {
      setErrors({ plate: 'Por favor ingresa una placa' });
      return;
    }

    setIsSearching(true);
    setErrors({});

    try {
      const response = await fetch(`/api/verification/search-vehicle?plate=${encodeURIComponent(plateNumber)}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al buscar vehículo');
      }

      const result = await response.json();

      if (result.success && result.data && result.data.vehicle) {
        setSelectedVehicle(result.data.vehicle);
        setCurrentStep(1);
        toast({
          title: 'Vehículo encontrado',
          description: `${result.data.vehicle.brand} ${result.data.vehicle.model} ${result.data.vehicle.year}`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        setErrors({ plate: 'Vehículo no encontrado en el sistema' });
      }
    } catch (error: any) {
      setErrors({ plate: error.message || 'Error al buscar vehículo' });
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectCenter = () => {
    if (!selectedCenter) {
      setErrors({ center: 'Por favor selecciona un centro de verificación' });
      return;
    }
    if (!verificationDate) {
      setErrors({ date: 'Por favor selecciona la fecha de verificación' });
      return;
    }

    setErrors({});
    setCurrentStep(2);
  };

  const handleDocumentsComplete = () => {
    if (!vehiclePhotoFile) {
      setErrors({ vehiclePhoto: 'La foto del vehículo es requerida' });
      return;
    }
    if (!circulationCardFile) {
      setErrors({ circulationCard: 'La tarjeta de circulación es requerida' });
      return;
    }

    setErrors({});
    setCurrentStep(3);
  };

  const handleSubmit = async () => {
    if (!selectedVehicle || !selectedCenter || !verificationDate) {
      toast({
        title: 'Información incompleta',
        description: 'Por favor completa todos los campos',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Mock URLs for files - in real implementation, upload files first
    const vehiclePhotoUrl = vehiclePhotoFile ? `https://cdn.onecar.com/uploads/${vehiclePhotoFile.name}` : '';
    const circulationCardUrl = circulationCardFile ? `https://cdn.onecar.com/uploads/${circulationCardFile.name}` : '';

    const verificationData = {
      vehiclePlate: plateNumber,
      verificationCenterId: selectedCenter,
      verificationDate: verificationDate,
      vehiclePhoto: vehiclePhotoUrl,
      circulationCard: circulationCardUrl,
    };

    createVerificationMutation.mutate(verificationData);
  };

  const handleReset = () => {
    setCurrentStep(0);
    setPlateNumber('');
    setSelectedVehicle(null);
    setSelectedCenter('');
    setVerificationDate('');
    setVehiclePhotoFile(null);
    setCirculationCardFile(null);
    setErrors({});
  };

  // Mock file upload function
  const mockFileUpload = async (file: File): Promise<string> => {
    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Return mock URL
    return `https://cdn.onecar.com/uploads/${file.name}`;
  };

  const getCurrentDate = () => {
    return new Date().toISOString().split('T')[0];
  };

  const getCurrentDateTime = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  interface SelectedCenterData extends VerificationCenter {}

  const selectedCenterData: SelectedCenterData | undefined = centers?.find(
    (center: VerificationCenter) => center._id === selectedCenter
  );

  return (
    <Card bg={bgColor}>
      <CardHeader>
        <Heading size="md">Registro de Verificación - Vendor</Heading>
      </CardHeader>

      <CardBody>
        <VStack spacing={8}>
          {/* Stepper */}
          <Stepper index={currentStep} colorScheme="orange" w="full">
            {steps.map((step, index) => (
              <Step key={index}>
                <StepIndicator>
                  <StepStatus
                    complete={<StepIcon />}
                    incomplete={<StepNumber />}
                    active={<StepNumber />}
                  />
                </StepIndicator>

                <Box flexShrink="0">
                  <StepTitle>{step.title}</StepTitle>
                  <StepDescription>{step.description}</StepDescription>
                </Box>

                <StepSeparator />
              </Step>
            ))}
          </Stepper>

          {/* Step Content */}
          <Box w="full" minH="400px">
            {/* Step 1: Search Vehicle */}
            {currentStep === 0 && (
              <VStack spacing={6}>
                <Box textAlign="center">
                  <Heading size="sm" mb={2}>Buscar Vehículo por Placa</Heading>
                  <Text fontSize="sm" color="gray.600">
                    Ingresa el número de placa para verificar que el vehículo esté registrado en el sistema
                  </Text>
                </Box>

                <FormControl isInvalid={!!errors.plate} maxW="400px">
                  <FormLabel>Número de Placa</FormLabel>
                  <HStack>
                    <Input
                      value={plateNumber}
                      onChange={(e) => setPlateNumber(e.target.value.toUpperCase())}
                      placeholder="ABC-123-D"
                      maxLength={10}
                      onKeyPress={(e) => e.key === 'Enter' && handleSearchVehicle()}
                    />
                    <Button
                      leftIcon={isSearching ? <Spinner size="sm" /> : <FiSearch />}
                      variant="outline"
                      colorScheme="blue"
                      borderWidth="2px"
                      color="blue.500"
                      onClick={handleSearchVehicle}
                      isLoading={isSearching}
                      loadingText="Buscando..."
                      minW="120px"
                      _hover={{ bg: "blue.50" }}
                    >
                      Buscar
                    </Button>
                  </HStack>
                  <FormErrorMessage>{errors.plate}</FormErrorMessage>
                </FormControl>

                {selectedVehicle && (
                  <Alert status="success" maxW="500px">
                    <AlertIcon />
                    <VStack align="start" spacing={1}>
                      <Text fontWeight="bold">
                        Vehículo encontrado: {selectedVehicle.brand} {selectedVehicle.model}
                      </Text>
                      <Text fontSize="sm">
                        Año: {selectedVehicle.year} • Color: {selectedVehicle.color}
                      </Text>
                      {selectedVehicle.isHybrid && <Badge colorScheme="green">Híbrido</Badge>}
                      {selectedVehicle.isElectric && <Badge colorScheme="blue">Eléctrico</Badge>}
                    </VStack>
                  </Alert>
                )}

                {errors.plate && (
                  <VStack spacing={3} maxW="500px">
                    <Alert status="error">
                      <AlertIcon />
                      <Text fontSize="sm">{errors.plate}</Text>
                    </Alert>
                    <Button
                      variant="outline"
                      colorScheme="blue"
                      borderWidth="2px"
                      color="blue.500"
                      size="sm"
                      onClick={() => {
                        setErrors({});
                        setSelectedVehicle(null);
                        setPlateNumber('');
                      }}
                      minW="120px"
                      _hover={{ bg: "blue.50" }}
                    >
                      Buscar otra placa
                    </Button>
                  </VStack>
                )}
              </VStack>
            )}

            {/* Step 2: Select Center */}
            {currentStep === 1 && (
              <VStack spacing={6}>
                <Box textAlign="center">
                  <Heading size="sm" mb={2}>Seleccionar Centro de Verificación</Heading>
                  <Text fontSize="sm" color="gray.600">
                    Elige el centro donde se realizará la verificación
                  </Text>
                </Box>

                <SimpleGrid columns={[1, 2]} spacing={6} w="full" maxW="600px">
                  <FormControl isInvalid={!!errors.center}>
                    <FormLabel>Centro de Verificación</FormLabel>
                    <Select
                      value={selectedCenter}
                      onChange={(e) => setSelectedCenter(e.target.value)}
                      placeholder="Selecciona un centro"
                    >
                        {centers?.map((center: VerificationCenter) => (
                        <option key={center._id} value={center._id}>
                          {center.code} - {center.name}
                        </option>
                        ))}
                    </Select>
                    <FormErrorMessage>{errors.center}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.date}>
                    <FormLabel>Fecha de Verificación</FormLabel>
                    <Input
                      type="datetime-local"
                      value={verificationDate}
                      readOnly
                      bg="gray.50"
                      cursor="not-allowed"
                      _hover={{ bg: "gray.50" }}
                    />
                    <FormErrorMessage>{errors.date}</FormErrorMessage>
                  </FormControl>
                </SimpleGrid>

                {selectedCenterData && (
                  <Alert status="info" maxW="500px">
                    <AlertIcon />
                    <VStack align="start" spacing={1}>
                      <Text fontWeight="bold">{selectedCenterData.name}</Text>
                      <Text fontSize="sm">
                        {selectedCenterData.location?.address || 'Dirección no disponible'}
                      </Text>
                      <Text fontSize="sm">Estado: {selectedCenterData.state}</Text>
                    </VStack>
                  </Alert>
                )}

                <Button
                  variant="outline"
                  colorScheme="blue"
                  borderWidth="2px"
                  color="blue.500"
                  onClick={handleSelectCenter}
                  size="lg"
                  minW="120px"
                  _hover={{ bg: "blue.50" }}
                >
                  Continuar
                </Button>
              </VStack>
            )}

            {/* Step 3: Upload Documents */}
            {currentStep === 2 && (
              <VStack spacing={6}>
                <Box textAlign="center">
                  <Heading size="sm" mb={2}>Subir Documentos</Heading>
                  <Text fontSize="sm" color="gray.600">
                    Sube la foto del vehículo y la tarjeta de circulación
                  </Text>
                </Box>

                <SimpleGrid columns={[1, 2]} spacing={8} w="full">
                  <VStack spacing={3}>
                    <Text fontWeight="bold">📸 Foto del Vehículo</Text>
                    <Text fontSize="sm" color="gray.600">
                      Foto clara del vehículo durante la verificación
                    </Text>
                    <FileUploader
                      accept="image/*"
                      maxFileSize={10 * 1024 * 1024} // 10MB
                      onFileSelect={setVehiclePhotoFile}
                      selectedFile={vehiclePhotoFile}
                      error={errors.vehiclePhoto}
                    />
                  </VStack>

                  <VStack spacing={3}>
                    <Text fontWeight="bold">📄 Tarjeta de Circulación</Text>
                    <Text fontSize="sm" color="gray.600">
                      Documento de circulación del vehículo (TDC)
                    </Text>
                    <FileUploader
                      accept="image/*,.pdf"
                      maxFileSize={10 * 1024 * 1024} // 10MB
                      onFileSelect={setCirculationCardFile}
                      selectedFile={circulationCardFile}
                      error={errors.circulationCard}
                    />
                  </VStack>
                </SimpleGrid>

                {(errors.vehiclePhoto || errors.circulationCard) && (
                  <Alert status="error" maxW="500px">
                    <AlertIcon />
                    <VStack align="start" spacing={1}>
                      {errors.vehiclePhoto && <Text fontSize="sm">{errors.vehiclePhoto}</Text>}
                      {errors.circulationCard && <Text fontSize="sm">{errors.circulationCard}</Text>}
                    </VStack>
                  </Alert>
                )}

                <Button
                  variant="outline"
                  colorScheme="blue"
                  borderWidth="2px"
                  color="blue.500"
                  onClick={handleDocumentsComplete}
                  size="lg"
                  leftIcon={<FiUpload />}
                  minW="120px"
                  _hover={{ bg: "blue.50" }}
                >
                  Documentos Completos
                </Button>
              </VStack>
            )}

            {/* Step 4: Confirm */}
            {currentStep === 3 && (
              <VStack spacing={6}>
                <Box textAlign="center">
                  <Heading size="sm" mb={2}>Confirmar Registro</Heading>
                  <Text fontSize="sm" color="gray.600">
                    Revisa la información antes de enviar
                  </Text>
                </Box>

                <Card maxW="600px" w="full">
                  <CardBody>
                    <VStack spacing={4} align="stretch">
                      <Box>
                        <Text fontWeight="bold" mb={2}>Información del Vehículo</Text>
                        <VStack align="start" spacing={1}>
                          <Text>Placa: {plateNumber}</Text>
                          <Text>Vehículo: {selectedVehicle?.brand} {selectedVehicle?.model} {selectedVehicle?.year}</Text>
                          <Text>Color: {selectedVehicle?.color}</Text>
                        </VStack>
                      </Box>

                      <Divider />

                      <Box>
                        <Text fontWeight="bold" mb={2}>Centro de Verificación</Text>
                        <VStack align="start" spacing={1}>
                          <Text>Centro: {selectedCenterData?.name}</Text>
                          <Text>Código: {selectedCenterData?.code}</Text>
                          <Text>Fecha: {new Date(verificationDate).toLocaleString()}</Text>
                        </VStack>
                      </Box>

                      <Divider />

                      <Box>
                        <Text fontWeight="bold" mb={2}>Documentos Subidos</Text>
                        <VStack align="start" spacing={1}>
                          <HStack>
                            <FiCheck color="green" />
                            <Text>Foto del vehículo: {vehiclePhotoFile?.name}</Text>
                          </HStack>
                          <HStack>
                            <FiCheck color="green" />
                            <Text>Tarjeta de circulación: {circulationCardFile?.name}</Text>
                          </HStack>
                        </VStack>
                      </Box>
                    </VStack>
                  </CardBody>
                </Card>

                <Alert status="info" maxW="600px">
                  <AlertIcon />
                  <Text fontSize="sm">
                    Al confirmar se enviará automáticamente un link único al cliente para que complete su parte de la verificación.
                  </Text>
                </Alert>

                <HStack spacing={4}>
                  <Button
                    variant="outline"
                    colorScheme="blue"
                    borderWidth="2px"
                    color="blue.500"
                    onClick={handleReset}
                    minW="120px"
                    _hover={{ bg: "blue.50" }}
                  >
                    Cancelar
                  </Button>
                  <Button
                    variant="outline"
                    colorScheme="blue"
                    borderWidth="2px"
                    color="blue.500"
                    size="lg"
                    leftIcon={<FiSend />}
                    onClick={handleSubmit}
                    isLoading={createVerificationMutation.isPending}
                    loadingText="Registrando..."
                    minW="120px"
                    _hover={{ bg: "blue.50" }}
                  >
                    Registrar Verificación
                  </Button>
                </HStack>
              </VStack>
            )}
          </Box>

          {/* Navigation buttons for steps 1-2 */}
          {currentStep > 0 && currentStep < 3 && (
            <HStack>
              <Button
                variant="outline"
                colorScheme="blue"
                borderWidth="2px"
                color="blue.500"
                onClick={() => setCurrentStep(currentStep - 1)}
                minW="120px"
                _hover={{ bg: "blue.50" }}
              >
                Anterior
              </Button>
            </HStack>
          )}
        </VStack>
      </CardBody>
    </Card>
  );
}