import { UserPermissions } from "@/constants/companyService";

export interface Address {
  street: string;
  number: string;
  colony: string;
  city: string;
  postalCode: string;
}

export interface WeeklySchedule {
  monday?: DaySchedule;
  tuesday?: DaySchedule;
  wednesday?: DaySchedule;
  thursday?: DaySchedule;
  friday?: DaySchedule;
  saturday?: DaySchedule;
  sunday?: DaySchedule;
}

export interface DaySchedule {
  start: string;
  end: string;
}

export interface City {
  _id: string;
  name: string;
  state: string;
  country: string;
  timezone: string;
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Crew {
  _id: string;
  name: string;
  cityId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Neighborhood {
  _id: string;
  name: string;
  crewId: string;
  cityId: string;
  scheduleConfig: {
    maxSimultaneousInstallations: number;
    installationDuration: number;
    weeklySchedule: WeeklySchedule;
  };
  address: Address;
  createdAt: string;
  updatedAt: string;
}


export type BaseUser = {
  _id: string;
  name: string;
  email: string;
  role: string;
  accessToken: string;
  userType: 'workshop' | 'company' | 'gestor' | 'superAdmin' | 'company-gestor';
  // image: string;
  city: string;
  image: {
    url: string;
  }
};

export type CompanyUser = BaseUser & {
  type: 'company';
  companyId: string;
  permissions: UserPermissions;
};

export type WorkshopUser = BaseUser & {
  type: 'workshop';
  organizationId: string;
};

export type GestorUser = BaseUser & {
  type: 'gestor';
  gestorId: string;
};

export type UserResponse = CompanyUser | WorkshopUser | GestorUser;