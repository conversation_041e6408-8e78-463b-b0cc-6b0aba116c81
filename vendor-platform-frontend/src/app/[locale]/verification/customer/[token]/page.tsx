import { Suspense } from 'react';
import { CustomerVerificationForm } from '@/components/verification/CustomerVerificationForm';

interface CustomerVerificationPageProps {
  params: {
    token: string;
    locale: string;
  };
}

export default function CustomerVerificationPage({ params }: CustomerVerificationPageProps) {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f7fafc', padding: '2rem 0' }}>
      <Suspense fallback={
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '50vh' 
        }}>
          <div>Cargando...</div>
        </div>
      }>
        <CustomerVerificationForm token={params.token} />
      </Suspense>
    </div>
  );
}