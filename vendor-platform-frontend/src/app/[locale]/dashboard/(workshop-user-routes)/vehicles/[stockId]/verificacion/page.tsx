import { Suspense } from 'react';
import { VehicleVerificationPage } from './client';

interface VerificationPageProps {
  params: {
    stockId: string;
    locale: string;
  };
}

export default function VerificationPage({ params }: VerificationPageProps) {
  return (
    <div>
      <Suspense fallback={
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '50vh' 
        }}>
          <div>Cargando...</div>
        </div>
      }>
        <VehicleVerificationPage stockId={params.stockId} />
      </Suspense>
    </div>
  );
}