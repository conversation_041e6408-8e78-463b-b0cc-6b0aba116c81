'use client';

import { useQuery } from '@tanstack/react-query';
import { 
  Box, 
  Container, 
  Tabs, 
  <PERSON>b<PERSON>ist, 
  TabPanels, 
  Tab, 
  TabPanel, 
  Spinner, 
  Alert, 
  AlertIcon,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  SimpleGrid,
  Badge,
  VStack,
} from '@chakra-ui/react';
import { VendorVerificationFlow } from '@/components/verification/VendorVerificationFlow';
import { AdminVerificationDashboard } from '@/components/verification/AdminVerificationDashboard';
import { getVehicleDetailById } from '../../_actions/getVehicleDetailById';
import { getVerificationHistory } from '@/actions/verification';
import { getVerificationStatus, formatVerificationStatus } from '@/utils/verification';

interface VehicleVerificationPageProps {
  stockId: string;
}

export function VehicleVerificationPage({ stockId }: VehicleVerificationPageProps) {
  const { data: vehicle, isLoading: vehicleLoading, error: vehicleError } = useQuery({
    queryKey: ['vehicle', stockId],
    queryFn: () => getVehicleDetailById(stockId),
  });

  const { data: verificationHistory, isLoading: verificationLoading } = useQuery({
    queryKey: ['verification-history', stockId],
    queryFn: () => getVerificationHistory(stockId),
    enabled: !!stockId,
  });

  const getCurrentVerificationStatus = () => {
    if (!verificationHistory || verificationHistory.records.length === 0) {
      return { label: 'Sin verificaciones', color: 'gray' };
    }
    
    const latest = verificationHistory.records[0];
    const nextDate = verificationHistory.nextVerificationDue ? new Date(verificationHistory.nextVerificationDue) : null;
    const status = getVerificationStatus(
      new Date(latest.verificationDate), 
      nextDate
    );
    
    return formatVerificationStatus(status);
  };

  if (vehicleLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minH="50vh">
        <Spinner size="xl" />
      </Box>
    );
  }

  if (vehicleError) {
    return (
      <Container maxW="2xl" py={8}>
        <Alert status="error">
          <AlertIcon />
          Error al cargar los datos del vehículo
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxW="6xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Vehicle Information Header */}
        <Card>
          <CardHeader>
            <Heading size="lg">Verificación Vehicular</Heading>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={[1, 2, 4]} spacing={4}>
              <Box>
                <Text fontSize="sm" color="gray.600">Placa</Text>
                <Text fontWeight="bold" fontSize="lg">
                  {vehicle?.carPlates?.plates || 'N/A'}
                </Text>
              </Box>
              <Box>
                <Text fontSize="sm" color="gray.600">Marca y Modelo</Text>
                <Text fontWeight="bold">
                  {vehicle?.brand} {vehicle?.model}
                </Text>
              </Box>
              <Box>
                <Text fontSize="sm" color="gray.600">VIN</Text>
                <Text fontWeight="bold">{vehicle?.vin}</Text>
              </Box>
              <Box>
                <Text fontSize="sm" color="gray.600">Estado de Verificación</Text>
                <Badge colorScheme={getCurrentVerificationStatus().color} size="lg">
                  {getCurrentVerificationStatus().label}
                </Badge>
              </Box>
            </SimpleGrid>
            
            {verificationHistory?.nextVerificationDue && (
              <Box mt={4} p={3} bg="blue.50" borderRadius="md">
                <Text fontSize="sm" color="blue.700">
                  <strong>Próxima verificación:</strong> {' '}
                  {new Date(verificationHistory.nextVerificationDue).toLocaleDateString('es-MX')}
                </Text>
              </Box>
            )}
          </CardBody>
        </Card>

        {/* Tabs */}
        <Tabs colorScheme="blue" variant="enclosed">
          <TabList>
            <Tab>Registrar Verificación</Tab>
            <Tab>Historial y Seguimiento</Tab>
          </TabList>

          <TabPanels>
            <TabPanel>
              <VendorVerificationFlow />
            </TabPanel>
            
            <TabPanel>
              <AdminVerificationDashboard vehicleId={stockId} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </VStack>
    </Container>
  );
}