import { Suspense } from "react"
import { getUsers } from "@/lib/user-actions copy"
import { CreateUserButton } from "@/app/_components/company-users/create-user-button"
import { UserList } from "@/app/_components/company-users/user-list"
import getUserById, { getSession } from "@/actions/getUserById"
import { redirect } from 'next/navigation'
import { companyService } from "@/constants/companyService"

export default async function UsersPage() {
  try {
    const session = await getSession();
    if (!session?.user) {
      console.error("No session found. Redirecting to login.");
      redirect('/login');
    }

    const user = await getUserById();

    if (!user) {
      console.error("User not found.");
      redirect('/dashboard');
    }

    // Obtener el companyId según el tipo de usuario
    let effectiveCompanyId: string | undefined;

    if (user.userType === 'company' || user.userType === 'company-gestor') {
      // Para usuarios company y company-gestor, usar companyId
      effectiveCompanyId = user.companyId || user.organizationId;
    } else if (user.userType === 'superAdmin') {
      // Para superAdmin, podríamos permitir ver todas las compañías o seleccionar una
      // Por ahora, obtenemos la primera compañía disponible
      try {
        companyService.setHeaders({
          'Authorization': `Bearer ${user.accessToken}`
        });

        const companiesResponse = await companyService.getAllCompanies();
        if (companiesResponse.data && companiesResponse.data.length > 0) {
          effectiveCompanyId = companiesResponse.data[0]._id;
        }
      } catch (error) {
        console.error('Error fetching companies for superAdmin:', error);
      }
    }

    // Validar que tenemos un companyId válido
    if (!effectiveCompanyId) {
      console.error('No valid companyId found for user:', user.userType);

      // Si es company-gestor sin companyId, intentar obtener la primera compañía
      if (user.userType === 'company-gestor') {
        try {
          companyService.setHeaders({
            'Authorization': `Bearer ${user.accessToken}`
          });

          const companiesResponse = await companyService.getAllCompanies();
          if (companiesResponse.data && companiesResponse.data.length > 0) {
            effectiveCompanyId = companiesResponse.data[0]._id;
          }
        } catch (error) {
          console.error('Error fetching companies:', error);
        }
      }

      // Si aún no hay companyId, mostrar error
      if (!effectiveCompanyId) {
        return (
          <div className="container mx-auto p-4">
            <h1 className="text-2xl font-bold text-red-500">Error: No se encontró ID de compañía</h1>
            <p>No se pudo determinar la compañía del usuario.</p>
            <p className="mt-2 text-sm text-gray-600">
              {user.userType === 'company-gestor'
                ? 'Los usuarios company-gestor necesitan tener un companyId asignado en su perfil.'
                : 'Por favor, contacte al administrador.'}
            </p>
            <pre className="mt-4 p-4 bg-gray-100 rounded text-sm">
              {JSON.stringify({
                userType: user.userType,
                userId: user._id,
                companyId: user.companyId,
                organizationId: user.organizationId
              }, null, 2)}
            </pre>
          </div>
        );
      }
    }

    const users = await getUsers(effectiveCompanyId);

    return (
      <div className="md:container mx-auto md:py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Usuarios</h1>
          <CreateUserButton companyId={effectiveCompanyId} />
        </div>

        <Suspense fallback={<div>Cargando usuarios...</div>}>
          <UserList users={users} companyId={effectiveCompanyId} />
        </Suspense>
      </div>
    );
  } catch (error) {
    console.error("Error in UsersPage:", error);
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> No se pudieron cargar los usuarios. Por favor, intente nuevamente más tarde.</span>
        </div>
      </div>
    );
  }
}
