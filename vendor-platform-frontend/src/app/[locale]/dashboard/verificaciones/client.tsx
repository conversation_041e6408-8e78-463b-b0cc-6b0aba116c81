'use client';

import { Suspense } from 'react';
import {
  Container,
  <PERSON>ing,
  VStack,
  Card,
  CardHeader,
  CardBody,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useColorModeValue,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { VendorRegistrationForm } from '@/components/verification/VendorRegistrationForm';

export function VerificationManagementPage() {
  const bgColor = useColorModeValue('white', 'gray.800');

  // Fetch dashboard data

  return (
    <Suspense fallback={
      <Container maxW="7xl" py={8}>
        <Alert status="info">
          <AlertIcon />
          Cargando sistema de verificaciones...
        </Alert>
      </Container>
    }>
      <Container maxW="7xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Card bg={bgColor}>
          <CardHeader>
            <Heading size="lg">Sistema de Verificaciones de Emisiones</Heading>
          </CardHeader>
          {/* <CardBody>
            {dashboardData && (
              <SimpleGrid columns={[1, 2, 5]} spacing={4}>
                <Stat>
                  <StatLabel>Total Verificaciones</StatLabel>
                  <StatNumber>{dashboardData.summary.totalVerifications.toLocaleString()}</StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    Tasa de finalización: {dashboardData.summary.completionRate}%
                  </StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Pendientes Cliente</StatLabel>
                  <StatNumber color="yellow.500">{dashboardData.summary.pendingCustomer}</StatNumber>
                  <StatHelpText>Esperando evidencias</StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Completadas</StatLabel>
                  <StatNumber color="green.500">{dashboardData.summary.completedVerifications}</StatNumber>
                  <StatHelpText>
                    Total procesadas
                  </StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Últimos 30 días</StatLabel>
                  <StatNumber>{dashboardData.recent.last30Days}</StatNumber>
                  <StatHelpText>Nuevas verificaciones</StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Centros Activos</StatLabel>
                  <StatNumber color="blue.500">{dashboardData.centers.active}</StatNumber>
                  <StatHelpText>de {dashboardData.centers.total} total</StatHelpText>
                </Stat>
              </SimpleGrid>
            )}
          </CardBody> */}
        </Card>

        {/* Registro de Verificación */}
        <VendorRegistrationForm />
      </VStack>
      </Container>
    </Suspense>
  );
}