'use client';

import { useCurrentUser } from "@/Providers/CurrentUserProvider";
import { useI18n } from "@/i18n/client";
import MenuOptionCard from "./components/MenuOptionCard";
import { getMenuOptionsByUserType } from "./components/menuOptions";
import { useMemo } from "react";
import { MenuOption } from "./components/menuOptions";

export default function HomePage() {
  const t = useI18n();
  const { user, isSuperAdminOrAdmin } = useCurrentUser();

  // Use useMemo instead of useState + useEffect to avoid infinite loop
  const menuOptions = useMemo(() => {
    // Check if user is superAdmin
    const isSuperAdmin = user?.userType === 'superAdmin';

    // Get menu options based on user type
    return getMenuOptionsByUserType(user?.userType || 'workshop', isSuperAdmin);
  }, [user?.userType, user?.role]); // Only depend on specific properties that should trigger a recalculation

  // Get user type display name
  const getUserTypeDisplay = () => {
    switch(user?.userType) {
      case 'workshop':
        return t('UserTypeWorkshop');
      case 'company':
        return t('UserTypeCompany');
      case 'gestor':
        return t('UserTypeGestor');
      case 'company-gestor':
        return t('UserTypeCompanyGestor');
      case 'superAdmin':
        return t('UserTypeSuperAdmin');
      default:
        return user?.userType || '';
    }
  };

  // Group menu options by user type: Instalación de cargadores, Workshop, Gestor, Admin
  const groupedOptions = useMemo(() => {
    // Define our main categories
    const groups: Record<string, MenuOption[]> = {
      'Instalación de cargadores': [],
      'Workshop': [],
      'Gestor': [],
      'Admin': []
    };

    // Check if user is superAdmin
    const isSuperAdmin = user?.userType === 'superAdmin';

    // Determine which category each option belongs to based on its color
    menuOptions.forEach(option => {
      const color = option.color || '';

      if (color.includes('green')) {
        // Green colors are for Instalación de cargadores (formerly Company)
        groups['Instalación de cargadores'].push(option);
      } else if (color.includes('blue')) {
        // Blue colors are for Workshop
        groups['Workshop'].push(option);
      } else if (color.includes('purple')) {
        // Purple colors are for Gestor
        groups['Gestor'].push(option);
      } else if (color.includes('red')) {
        // Red colors are for Admin - only show for superAdmin users
        if (isSuperAdmin) {
          groups['Admin'].push(option);
        }
      } else {
        // Default to Workshop for any uncategorized items
        groups['Workshop'].push(option);
      }
    });

    // For company-gestor users, ensure both Instalación de cargadores and Gestor sections are shown
    // even if one of them might be empty from the color-based sorting
    if (user?.userType === 'company-gestor') {
      // Make sure both Instalación de cargadores and Gestor sections exist
      if (groups['Instalación de cargadores'].length === 0 && menuOptions.length > 0) {
        groups['Instalación de cargadores'] = []; // Ensure the section exists even if empty
      }
      if (groups['Gestor'].length === 0 && menuOptions.length > 0) {
        groups['Gestor'] = []; // Ensure the section exists even if empty
      }
    }

    // Remove empty categories
    Object.keys(groups).forEach(key => {
      if (groups[key].length === 0) {
        delete groups[key];
      }
    });

    return groups;
  }, [menuOptions, user?.userType, user?.role]);

  return (
    <div className="flex flex-col">
      <div className="mb-6">
        <h1 className="bg-primary-gradient bg-clip-text text-transparent font-bold text-[32px]">
          {t("Welcome")}, {user?.name}
        </h1>
        <div className="text-gray-600 mt-2">
          <p>
            {isSuperAdminOrAdmin
              ? t('AllFunctionalitiesAccess')
              : `${t('YourRoleIs')} ${getUserTypeDisplay()}`}
          </p>
          <p className="mt-1">{t('SelectOption')}</p>
        </div>
      </div>

      {/* Render each category group in a specific order */}
      {Object.entries(groupedOptions)
        .sort(([a], [b]) => {
          // Define the order: Instalación de cargadores, Workshop, Gestor, Admin
          const order = { 'Instalación de cargadores': 1, 'Workshop': 2, 'Gestor': 3, 'Admin': 4 };
          return (order[a as keyof typeof order] || 99) - (order[b as keyof typeof order] || 99);
        })
        .map(([category, options]) => {
        // Define category-specific styles
        let headerClass = "text-xl font-semibold mb-4 border-b pb-2";
        let sectionClass = "mb-10 p-6 rounded-lg";

        // Apply different styles based on category
        switch(category) {
          case 'Instalación de cargadores':
            headerClass += " text-green-800 border-green-300";
            sectionClass += " bg-green-50";
            break;
          case 'Workshop':
            headerClass += " text-blue-800 border-blue-300";
            sectionClass += " bg-blue-50";
            break;
          case 'Gestor':
            headerClass += " text-purple-800 border-purple-300";
            sectionClass += " bg-purple-50";
            break;
          case 'Admin':
            headerClass += " text-red-800 border-red-300";
            sectionClass += " bg-red-50";
            break;
          default:
            headerClass += " text-gray-800 border-gray-300";
            sectionClass += " bg-gray-50";
        }

        return (
          <div key={category} className={sectionClass}>
            <h2 className={headerClass}>{t(category as keyof typeof t) || category}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {options.map((option, index) => (
                <MenuOptionCard
                  key={index}
                  title={option.title}
                  description={option.description}
                  icon={option.icon}
                  link={option.link}
                  color={option.color}
                />
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
}
