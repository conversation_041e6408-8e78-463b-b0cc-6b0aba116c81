import { NextRequest, NextResponse } from 'next/server';
import { searchVehicleByPlate } from '@/actions/verification';

// This route is dynamic and should not be pre-rendered
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const plateNumber = searchParams.get('plate');

    if (!plateNumber) {
      return NextResponse.json(
        { error: 'Plate number is required' },
        { status: 400 }
      );
    }

    const result = await searchVehicleByPlate(plateNumber);
    
    return NextResponse.json({ success: true, data: result });
  } catch (error: any) {
    console.error('Error in search-vehicle API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}