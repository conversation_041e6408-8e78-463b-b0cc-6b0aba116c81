import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { VerificationCenter } from '@/types';

// This route is dynamic and should not be pre-rendered
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const state = searchParams.get('state');

    // TODO: Implement API call to backend to get verification centers
    // This is a placeholder for the actual API implementation
    const mockCenters: VerificationCenter[] = [
      {
        _id: 'center-1',
        name: 'Verificentro MH01',
        code: 'MH01',
        location: {
          address: 'Av. Insurgentes Sur 1234, Del Valle',
          city: 'Ciudad de México',
          state: 'CDMX',
        },
        state: 'CDMX',
        organizationId: 'org-1',
        authorizedFor: ['vehicles', 'motorcycles'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        _id: 'center-2',
        name: 'Verificentro MH02',
        code: 'MH02',
        location: {
          address: 'Av. Reforma 567, Juárez',
          city: 'Ciudad de México',
          state: 'CDMX',
        },
        state: 'CDMX',
        organizationId: 'org-1',
        authorizedFor: ['vehicles', 'motorcycles'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        _id: 'center-3',
        name: 'Verificentro EDO01',
        code: 'EDO01',
        location: {
          address: 'Av. Constituyentes 890, Centro',
          city: 'Toluca',
          state: 'EDOMEX',
        },
        state: 'EDOMEX',
        organizationId: 'org-2',
        authorizedFor: ['vehicles', 'motorcycles'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    const filteredCenters = state 
      ? mockCenters.filter(center => center.state === state)
      : mockCenters;

    return NextResponse.json(filteredCenters);
  } catch (error) {
    console.error('Error fetching verification centers:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || (session.user as any).userType !== 'superAdmin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, code, location, state, organizationId, authorizedFor } = body;

    if (!name || !code || !location || !state || !organizationId) {
      return NextResponse.json({ 
        error: 'name, code, location, state, and organizationId are required' 
      }, { status: 400 });
    }

    // TODO: Implement API call to backend to create verification center
    // This is a placeholder for the actual API implementation
    const mockCenter: VerificationCenter = {
      _id: 'new-center-id',
      name,
      code,
      location,
      state,
      organizationId,
      authorizedFor: authorizedFor || ['vehicles'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return NextResponse.json(mockCenter, { status: 201 });
  } catch (error) {
    console.error('Error creating verification center:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}