// OCR API integration utility functions
import axios from 'axios';
import { URL_API } from '@/constants';
import { OCR_CONFIG } from '@/constants/ocrConfig';

export type DocumentType =
  | 'FACTURE'
  | 'PLATES_ALTA_PLACAS'
  | 'PLATES_FRONT'
  | 'PLATES_BACK'
  | 'CIRCULATION_CARD_FRONT'
  | 'CIRCULATION_CARD_BACK'
  | 'INSURANCE_POLICY'
  | 'TENENCIA';

export interface OCRResult {
  success: boolean;
  data?: {
    // Placas fields
    plates?: string;

    // Insurance policy fields
    policyNumber?: string;
    insurer?: string;
    validity?: string;
    broker?: string;

    // Tenancy fields
    payment?: string;

    // Circulation card fields
    number?: string;

    // Facture/Invoice fields
    billNumber?: string;
    billDate?: string;
    billAmount?: string;
  };
  error?: string;
  validationErrors?: string[];
  isValidDocument?: boolean; // Whether the document passed validation
}

export interface OCRApiPayload {
  document: File;
  documentType: DocumentType;
  vehicleId?: string;
  plates?: string;
}

/**
 * Calls the backend OCR API to extract data from a document
 * Backend now always returns extracted data, even for invalid documents (400 response)
 */
export const extractDataFromDocument = async (
  payload: OCRApiPayload,
  accessToken: string
): Promise<OCRResult> => {
  try {
    const formData = new FormData();
    formData.append('file', payload.document);
    formData.append('documentType', payload.documentType);
    if (payload.vehicleId) {
      formData.append('vehicleId', payload.vehicleId);
    }
    if (payload.plates) {
      formData.append('plates', payload.plates);
    }

    const response = await axios.post(`${URL_API}/stock/documents/ocr/extract`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${accessToken}`,
      },
      timeout: OCR_CONFIG.requestTimeout,
    });

    // Success (200) - Document is valid and matches vehicle
    const extractedFields = response.data.data || {};
    const transformedData = transformExtractedData(extractedFields);

    return {
      success: true,
      data: transformedData,
      validationErrors: response.data.validationErrors || [],
      isValidDocument: true,
    };
  } catch (error: any) {
    // Handle 400 - Validation errors but with extracted data
    if (error.response?.status === 400) {
      const errorData = error.response.data;

      // Handle the new error response structure with nested error object
      if (errorData.error && typeof errorData.error === 'object') {
        const validationErrors = errorData.error.details?.map((detail: any) => detail.message) || [];
        const errorMessage = errorData.error.message || 'Request validation failed';

        return {
          success: false,
          data: {}, // No extracted data in this error format
          error: errorMessage,
          validationErrors,
          isValidDocument: false,
        };
      }

      // Handle the original error response structure (backward compatibility)
      const extractedFields = errorData.data || {};
      const transformedData = transformExtractedData(extractedFields);

      return {
        success: false,
        data: transformedData,
        error: errorData.error || 'Document validation failed',
        validationErrors: errorData.validationErrors || [],
        isValidDocument: false,
      };
    }

    // Handle other errors (500, network, etc.)
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'OCR extraction failed',
      validationErrors: error.response?.data?.validationErrors || [],
      isValidDocument: false,
    };
  }
};

/**
 * Transform extracted fields to our expected structure
 */
const transformExtractedData = (extractedFields: any): OCRResult['data'] => {
  const transformedData: OCRResult['data'] = {};

  // Map the API response fields to our expected structure
  if (extractedFields.number) {
    transformedData.number = extractedFields.number;
  }
  if (extractedFields.validity) {
    transformedData.validity = extractedFields.validity;
  }
  if (extractedFields.plates) {
    transformedData.plates = extractedFields.plates;
  }
  if (extractedFields.policyNumber) {
    transformedData.policyNumber = extractedFields.policyNumber;
  }
  if (extractedFields.insurer) {
    transformedData.insurer = extractedFields.insurer;
  }
  if (extractedFields.broker) {
    transformedData.broker = extractedFields.broker;
  }
  if (extractedFields.payment) {
    transformedData.payment = extractedFields.payment;
  }
  if (extractedFields.billNumber) {
    transformedData.billNumber = extractedFields.billNumber;
  }
  if (extractedFields.billDate) {
    transformedData.billDate = extractedFields.billDate;
  }
  if (extractedFields.billAmount) {
    transformedData.billAmount = extractedFields.billAmount;
  }

  return transformedData;
};

/**
 * Helper function to show OCR toast notifications
 */
export const showOCRToast = (toast: any, result: OCRResult, userAcceptedInvalidDocument?: boolean) => {
  console.log('OCR Result:', result);

  if (!OCR_CONFIG.showToastNotifications) {
    return;
  }

  if (result.isValidDocument) {
    toast({
      title: 'Extracción de Datos Exitosa',
      description: 'El documento es válido y los datos se han extraído automáticamente',
      status: 'success',
      duration: 3000,
    });
  } else if (userAcceptedInvalidDocument) {
    toast({
      title: 'Procediendo con Documento No Válido',
      description:
        'Datos del documento extraídos pero la validación falló. Verifique todos los campos cuidadosamente.',
      status: 'warning',
      duration: 4000,
    });
  } else if (result.validationErrors && result.validationErrors.length > 0) {
    toast({
      title: result.error || 'Errores de Validación',
      description: result.validationErrors[0],
      status: 'error',
      duration: 5000,
    });
  } else {
    toast({
      title: 'Extracción OCR Fallida',
      description: result.error || 'No se pudieron extraer los datos del documento',
      status: 'error',
      duration: 5000,
    });
  }
};
