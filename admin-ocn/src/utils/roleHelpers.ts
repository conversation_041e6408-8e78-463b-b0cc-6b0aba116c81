// utils/roleHelpers.ts

import {
  salesRoleOptions,
  auditRoleOptions,
  collectionRoleOptions,
  fleetRoleOptions,
  backOfficeRoleOptions,
  legalRoleOptions,
  cxRoleOptions,
  kycRoleOptions,
  visitorRoleOptions,
  financeRoleOptions,
  superAdminRoleOptions,
  Roles,
} from '@/constants';
import { Areas } from '@/constants';

export function getRolesByArea(area: string, role: string) {
  let roles: any[];

  switch (area) {
    case Areas.Sales:
      roles = salesRoleOptions;
      break;
    case Areas.Audit:
      roles = auditRoleOptions;
      break;
    case Areas.Collection:
      roles = collectionRoleOptions;
      break;
    case Areas.Fleet:
      roles = fleetRoleOptions;
      break;
    case Areas.Backoffice:
      roles = backOfficeRoleOptions;
      break;
    case Areas.Legal:
      roles = legalRoleOptions;
      break;
    case Areas.CX:
      roles = cxRoleOptions;
      break;
    case Areas.KYC:
      roles = kycRoleOptions;
      break;
    case Areas.HomeVisit:
      roles = visitorRoleOptions;
      break;
    case Areas.Finance:
      roles = financeRoleOptions;
      break;
    case Areas.Superadmin:
      roles = superAdminRoleOptions;
      break;
    default:
      roles = [];
  }

  switch (role) {
    case Roles.Agent:
      return roles.filter((x) => x.value === Roles.Agent);
    case Roles.RecoveryAgent:
      return roles.filter((x) => x.value === Roles.RecoveryAgent);
    case Roles.RecoveryManager:
      return roles.filter((x) => x.value === Roles.RecoveryAgent || x.value === Roles.RecoveryManager);
    case Roles.Lead:
      return roles.filter((x) => x.value !== Roles.Administrator);
    default:
      return roles;
  }
}
