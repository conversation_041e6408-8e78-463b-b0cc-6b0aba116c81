import { StockService } from '@/actions/getStockServices';
import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';
import { cache } from 'react';
import { getStockServices } from './getStockServices';
import { LegalProcess, getLegalProcesses } from './getLegalProcesses';
import { User } from '@/app/dashboard/pagos/types';
import { getClientDetails } from './getClientDetails';

interface DocumentData {
  url: string;
  docId: string;
  originalName: string;
}

interface Readmission {
  readmissionDate: string;
  readmissionReason: string;
  description: string;
  kmImgs: DocumentData[];
  evidenceImgs: DocumentData[];
  promissoryNote: DocumentData;
  readmissionDoc: DocumentData;
  deliveredDate: string;
  contractCanceled: DocumentData;
  terminationFiles: {
    promissoryNote: DocumentData | null;
    agreement: DocumentData | null;
    promissoryNoteSigned: DocumentData | null;
    agreementSigned: DocumentData | null;
  };
  associate: Driver;
  associateId: string;
  vehicleId: string;
  contractNumber: number;
  extensionContractNumber: number;
  km?: number;
}

interface SignDocs {
  contract: DocumentData;
  deliveryReceipt: DocumentData;
  invoice: DocumentData;
  privacy: DocumentData;
  promissoryNote: DocumentData;
  warranty: DocumentData;
  contactInfo: DocumentData;
}

interface ContractData {
  deliveredDate: string;
  finalPrice: number;
  totalPrice: number;
  weeklyRent: number;
  downPayment: number;
  allPayments: {
    day: string;
    number: string;
  }[];
  id: string;
  isActive: boolean;
  stockId: string;
  associatedId: string;
  contractNumber: number;
  deliveryData: {
    date: string; // UTC date
    timezone: string; // Timezone of the delivery that corresponds to the city
    isoStringRealDate: string; // ISO string of the real date
  };
}
enum ActionType {
  SCAN = 'SCAN',
  STATUS_CHANGE = 'STATUS_CHANGE',
}
export interface QRScanHistoryItem {
  _id: string;
  vehicleId: string;
  userId: {
    _id: string;
    name: string;
    email: string;
  };
  scanTime: string;
  statusChangedFrom: string;
  statusChangedTo: string;
  deviceInfo?: string;
  actionType: ActionType;
  notes?: string;
  location?: string;
  vendorRegion?: string;
  vendorWorkshopName?: string;
}

export interface VehicleResponse {
  _id: string;
  carNumber: string;
  canFinishProcess: boolean;
  firstDeliveryDate: string;
  extensionCarNumber: string;
  vehiclePhoto: DocumentData;
  model: string;
  brand: string;
  status: string;
  vehicleStatus: string;
  category: string;
  subCategory?: string;
  version: string;
  year: string;
  color: string;
  vin: string;
  km: number;
  gpsNumber: string;
  gpsSerie: string;
  gpsInstalled: boolean;
  vehicleState: string;
  owner: string;
  contract: DocumentData;
  billAmount: string;
  billDate?: string;
  receptionDate?: string;
  billNumber: string;
  readmissionDate: string | null;
  readmissionsReason: string | null;
  deliveredDate: string | undefined | null;
  readmissions: Readmission[];
  bill: {
    url: string;
    originalName: string;
  };
  downPaymentModified?: boolean;
  carPlates: {
    plates: string;
    frontImg: DocumentData;
    backImg: DocumentData;
    platesDocument: DocumentData;
  };
  tenancy: {
    payment: number;
    validity: string;
    tenancyDocument: DocumentData;
    _id: string;
  }[];
  circulationCard: {
    number: number;
    frontImg: DocumentData;
    backImg: DocumentData;
    validity: string;
  };
  policiesArray: {
    policyNumber: number;
    insurer: string;
    validity: string;
    broker: string;
    policyDocument: DocumentData;
    _id: string;
  }[];
  updateHistory: {
    time: string;
    name: string;
    image: DocumentData;
    step: string;
    description: string;
    user: {
      image: DocumentData;
      name: string;
    };
  }[];
  drivers: Driver[];
  newCar: boolean;
  platform?: string;
  isElectric: boolean;
  step: {
    stepName: string;
    stepNumber: number;
  };
  dischargedData?: {
    comments?: string;
    reason: string;
    date: string;
    platesDischargedDoc: DocumentData;
    dictamenDoc?: DocumentData;
    reportDoc?: DocumentData;
  };
  stockServices: StockService[];
  legalProcesses: LegalProcess[];
  transferredTo: string;
  isBlocked: boolean;
  country?: string;
  state?: string;
  purchaseAgreement?: DocumentData;
  soldInvoicePdf?: DocumentData;
  soldInvoiceXml?: DocumentData;

  lastViolationCheck: Date | null;
  violations: VoilationData[];
  qrCode?: {
    url: string;
  };
  physicalStatus?: string;
  qrScanHistory?: QRScanHistoryItem[];
}

export interface Documents {
  addressVerification: DocumentData;
  curp: DocumentData;
  ine: {
    ineBack: DocumentData;
    ineFront: DocumentData;
  };
  driverLicense: {
    driverLicenseFront: DocumentData;
    driverLicenseBack: DocumentData;
  };
  garage: DocumentData;
  taxStatus: DocumentData;
}

export interface Driver {
  _id: string;
  admissionRequestId?: string;
  birthDay: string;
  curp: string;
  clientId: string;
  adendumDocs: DocumentData[];
  documents: Documents;
  email: string;
  platforms: {
    uber: string | null | undefined;
    didi: string | null | undefined;
  };
  picture: DocumentData;
  deliveredImages: DocumentData[];
  signDocs: SignDocs;
  contractData: ContractData;
  unSignedContractDoc: DocumentData | null;
  address: {
    addressStreet: string;
    exterior: number;
    interior: number;
    postalCode: number;
    colony: string;
    state: string;
    city: string;
    delegation: string;
  };
  state: string;
  city: string;
  whatsApp: string | null | undefined;
  firstName: string;
  lastName: string;
  phone: string;
  rfc: string;
  avalData?: AvalData;
  digitalSignature: DigitalSignature;
  contacts: Contact[] | ContactUS[];
  country?: string;
  ssn?: string;
  active: boolean;
  userFromPaymentService: User | null;
}

export interface Contact {
  name: string;
  kinship: string;
  phone: string;
  address: string;
}

export interface ContactUS {
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelation: string;
}

export interface DigitalSignature {
  documentID: string | null;
  url: string | null;
  signed: boolean;
  isSent: boolean;
  participants: Participant[];
}

export interface Participant {
  name: string;
  email: string;
  signed: boolean;
  urlSign: string;
}
export interface AvalData {
  name: string;
  phone: string;
  email: string;
  address: string;
  ine: DocumentData;
}

interface VoilationData {
  folio: string;
  violationDate: Date;
  status: string;
  amount: number;
}

async function getVehicleDetail(id: string) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const response = await axios.get(`${URL_API}/stock/getById/${id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    // console.log(response.data.data.drivers[response.data.data.drivers.length - 1].contractData);
    const stockServices = response.data.data.status === 'in-service' ? await getStockServices(id) : [];
    const legalProcesses = response.data.data.status === 'legal-process' ? await getLegalProcesses(id) : null;
    const insuranceProcess =
      response.data.data.status === 'awaiting-insurance'
        ? await getLegalProcesses(id, 'getInsuranceServices')
        : null;

    const data = {
      ...response.data.data,
      stockServices,
      legalProcesses: legalProcesses || insuranceProcess || [],
    };

    const drivers = data.drivers;
    const lastDriver = drivers[drivers.length - 1];
    if (lastDriver && lastDriver.clientId && lastDriver.active) {
      const clientFromPaymentService = await getClientDetails(lastDriver.clientId);
      lastDriver.userFromPaymentService = clientFromPaymentService;
      drivers[drivers.length - 1] = lastDriver;
    }

    return data as VehicleResponse;
  } catch (error: any) {
    return null;
  }
}

const getVehicleDetailCache = cache(async (id: string) => {
  const vehicleDetail = await getVehicleDetail(id);
  return vehicleDetail;
});

export default getVehicleDetailCache;
