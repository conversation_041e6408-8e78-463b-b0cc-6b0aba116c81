import axios from 'axios';

const img = 'https://hilos-media.s3.amazonaws.com/public-media/88201ffa06bb49c3b813cf53b1e70c0d.png';

const HILOS_API = 'https://api.hilos.io/api';

const KEY = `Token ${process.env.HILOS_API_KEY}`;

interface DriverData {
  url: string;
  name: string;
  phone: string;
}

interface AvalData extends DriverData {
  associateName: string;
}

export const sendContractToDriver = async (data: DriverData) => {
  try {
    let { url, ...associate } = data;

    url = url.replace('https://api.weetrust.mx/', '');
    url = url.replace('https://app.weetrust.mx/', '');
    url = url.replace('https://sandbox.weetrust.com.mx/', '');

    await axios.post(
      `${HILOS_API}/channels/whatsapp/template/${'84093c04-3dc2-4b0e-b629-e6c0ec53e5a8'}/send`,
      {
        variables: [img, associate.name, url],
        phone: associate.phone.split('+').join(''),
      },
      {
        headers: {
          Authorization: `${KEY}`,
        },
      }
    );
  } catch (error: any) {
    console.error('[ERROR SENDING CONTRACT TO DRIVER]', error.response.data || error.message);
  }
};

export const sendContractToAval = async (data: AvalData) => {
  try {
    let { url, ...aval } = data;
    url = url.replace('https://api.weetrust.mx/', '');
    url = url.replace('https://app.weetrust.mx/', '');
    url = url.replace('https://sandbox.weetrust.com.mx/', '');

    console.log('URL', url, 'for ', aval.name);

    await axios.post(
      `${HILOS_API}/channels/whatsapp/template/${'802f9e26-3c54-4fd3-a5f2-67e69d290ccb'}/send`,
      {
        variables: [img, aval.name, aval.associateName, url],
        phone: aval.phone.split('+').join(''),
      },
      {
        headers: {
          Authorization: `${KEY}`,
        },
      }
    );
  } catch (error: any) {
    console.error('[ERROR SENDING CONTRACT TO AVAL]', error.response.data || error.message);
  }
};
