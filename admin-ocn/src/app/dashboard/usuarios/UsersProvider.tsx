'use client';
import { UserResponse } from '@/actions/getUserById';
import { createContext, useState, useContext, ChangeEvent, useCallback, useMemo } from 'react';

// Define allowed status filters
export type StatusFilterType = 'all' | 'active' | 'inactive' | 'pending';

// Context interface
interface ContextProps {
  users: UserResponse[];
  handleSearch: (event: ChangeEvent<HTMLInputElement>) => void;
  handleStatusFilter: (status: StatusFilterType) => void;
}

// Create context
const UsersContext = createContext<ContextProps | undefined>(undefined);

// Hook to use the context
export const useUsers = () => {
  const context = useContext(UsersContext);
  if (!context) throw new Error('useUsers must be used within a UsersProvider');
  return context;
};

interface ProviderProps {
  children: React.ReactNode;
  usersFetch: UserResponse[];
}

// Provider
export default function UsersProvider({ children, usersFetch }: ProviderProps) {
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilterType>('all');

  // Search input handler
  const handleSearch = useCallback((event: ChangeEvent<HTMLInputElement>) => {
    setSearchText(event.target.value.trim().toLowerCase());
  }, []);

  // Dropdown status filter handler
  const handleStatusFilter = useCallback((status: StatusFilterType) => {
    setStatusFilter(status);
  }, []);

  // Combined filter logic using useMemo for performance
  const users = useMemo(() => {
    return usersFetch.filter((user) => {
      // Filter by status
      if (statusFilter === 'active' && (!user.isActive || !user.isVerified)) return false;
      if (statusFilter === 'inactive' && user.isActive) return false;
      if (statusFilter === 'pending' && (user.isVerified || !user.isActive)) return false;

      // Filter by search
      if (searchText) {
        const text = searchText.toLowerCase();
        return (
          user.name.toLowerCase().includes(text) ||
          user.email.toLowerCase().includes(text) ||
          user.city.toLowerCase().includes(text)
        );
      }

      return true;
    });
  }, [searchText, statusFilter, usersFetch]);

  return (
    <UsersContext.Provider value={{ users, handleSearch, handleStatusFilter }}>
      {children}
    </UsersContext.Provider>
  );
}
