import { Capabilities, CONTRACT_REGIONS_IATA, Sections, Subsections } from '@/constants';
import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import FilterVehicles from './FilterVehicles';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import SortVehicles from './SortVehicles';
import useSidebarStore from '@/zustand/sidebarStore';
import SearchInput from './SearchInput';
import CountrySelector from '@/app/dashboard/clientes/_components/CountrySelector';
import {
  StatusTranslations,
  StatusTranslationsMX,
  VehicleCategoryTranslations,
  VehicleCategoryTranslationsMX,
} from '../translations/statusTranslations';
import VehicleRegistrationFlow from './VehicleRegistrationFlow';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import BulkUploadVehicleDocuments from './BulkUploadVehicleDocuments';

type VehicleStatusKey = 'active' | 'inactive';
type VehicleCategoryKey =
  | 'withdrawn'
  | 'sold'
  | 'insurance'
  | 'collection'
  | 'legal'
  | 'workshop'
  | 'revision'
  | 'adendum'
  | 'in-preparation'
  | 'stock'
  | 'assigned'
  | 'delivered'
  | 'utilitary';

export default function BarRevamp({
  page,
  subPage,
  category = null,
  subCategory = null,
  country = 'Mexico',
}: {
  page?: { name: string; api: string; count: number };
  subPage?: { name: string; api: string; count: number };
  category?: string | null;
  subCategory?: string | null;
  country?: string | null;
}) {
  const ability = usePermissions();
  const vehicleStatusCounts = useSidebarStore((state) => state.vehicleStatusCounts);
  const categoryCounts = useSidebarStore((state) => state.categoryCounts);

  const statusTranslations = country === 'Mexico' ? StatusTranslationsMX : StatusTranslations;
  const categoryTranslations =
    country === 'Mexico' ? VehicleCategoryTranslationsMX : VehicleCategoryTranslations;

  const pageName =
    page?.api && statusTranslations[page.api as VehicleStatusKey]
      ? statusTranslations[page.api as VehicleStatusKey]
      : page?.name;
  const subPageName =
    subPage?.api && categoryTranslations[subPage.api as VehicleCategoryKey]
      ? categoryTranslations[subPage.api as VehicleCategoryKey]
      : subPage?.name;

  const canAdd = canPerform(ability, Capabilities.Add, Sections.Fleet, Subsections.General);
  return (
    <div>
      <div className="flex items-center justify-between md:gap-0 flex-wrap pr-12 pb-4 ">
        <div className="flex items-center justify-between">
          <h1 className="font-bold text-interBold32 font-inter" style={{ fontWeight: '700 !important' }}>
            {pageName} {`(${vehicleStatusCounts?.[page?.api as VehicleStatusKey] || 0})`}
          </h1>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={'3px'}
            stroke="#CFD8E1"
            className="h-4 text-gray-400"
            width={'2rem'}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
          </svg>
          <button
            className="text-white px-4 py-1 text-sm font-medium"
            style={{ backgroundColor: '#5800F7', borderRadius: '4px' }}
          >
            {subPageName} {`(${categoryCounts?.[subPage?.api as VehicleCategoryKey] || 0})`}
          </button>
        </div>
        <CountrySelector />
        <div className="flex flex-row justify-end gap-2 lg:gap-0 lg:gap-x-2 flex-wrap">
          <SearchInput
            page={`${page?.api}/${subPage?.api}`}
            vehicleStatus={page?.api}
            category={category}
            subCategory={subCategory}
          />
          <FilterVehicles />
          <SortVehicles />
          {canAdd && <CreateVehicle />}
          {canAdd && <BulkUploadVehicleDocuments />}
        </div>
        {/* <CountrySelector /> */}
      </div>
    </div>
  );
}

function CreateVehicle() {
  const [nameFiles, setNameFiles] = useState({
    vehiclePhoto: '',
    bill: '',
  });

  const handleSetName = (name: string, value: string) => {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  };

  const search = useSearchParams();
  const paramCountry = search ? search.get('country') : '';
  const { user: currentUser } = useCurrentUser();
  const allowedRegions = CONTRACT_REGIONS_IATA.filter((r) => {
    const code = r.code as 'cdmx' | 'gdl' | 'mty' | 'qro' | 'tij' | 'pbc';
    return currentUser.settings.allowedRegions.includes(code);
  });

  return (
    <>
      <VehicleRegistrationFlow
        paramCountry={paramCountry}
        allowedRegions={allowedRegions}
        handleSetName={handleSetName}
        nameFiles={nameFiles}
      />
    </>
  );
}
