'use client';
import { RiArrowLeftSLine } from 'react-icons/ri';
import Link from 'next/link';
import { QRScanHistoryItem, VehicleResponse } from '@/actions/getVehicleData';
import AssociateModalData from '../Detail/AssociateModalData';
import GenerateContract from '../Detail/GenerateContract';
import { AssignAssociateModal } from '@/components/Modals/AssignAssociateModal';
import VehicleSteps from '../Detail/Steps';
import VehicleDetail from '../Detail/VehicleDetail';
import { MyUser } from '@/actions/getCurrentUser';
import Placas from '../Detail/Placas';
import CirculationCard from '../Detail/CirculationCard';
import Policy from '../Detail/Policy';
import History from '../Detail/History';
import Tenancy from '../Detail/Tenancy';
import UploadContractDocs from '../Detail/UploadContractDocs';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import ReadmissionButton from '../Detail/ReadmissionButton';
import SiniestrosBox from '../Detail/SiniestrosBox';
import GestoresBox from '../Detail/GestoresBox';
import SoldBox from '../Detail/SoldBox';
import PaymentsBox from '../Detail/PaymentsBox';
import DriversBox from '../Detail/DriversBox';
import StepsReadmissions from '../Detail/StepsReadmissions';
import ChangeRegion from '../Modals/ChangeRegion';
import GPSV2 from '../Detail/GPSV2';
import PhysicalStatusCard from '../Detail/PhysicalStatusCard';
import { useReturnStepModal, useReturnChangeRegion } from '@/zustand/modalStates';
import ReturnStepModal from '../Modals/ReturnStepModal';
import PrimaryButton from '@/components/PrimaryButton';
import {
  emailUsersAllowed,
  isProd,
  emailUsersAllowedRegion,
  allCategory,
  Capabilities,
  Sections,
  Subsections,
} from '@/constants';
import StartPayFlow from '../Buttons/StartPayFlow';
import SendDocToSign from '../Buttons/SendDocToSign';
import ResendContractToSign from '../Modals/ResendContractToSign';
import ReturnToStockModal from '../Modals/ReturnStockModal';
import VehicleDetailDataProvider, { AssociateData } from '../Providers/VehicleDetailDataProvider';
import { useSearchParams } from 'next/navigation';
import { CountryProvider } from '@/app/dashboard/providers/CountryProvider';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import ViolationsBox from '../Detail/ViolationsBox';

interface DetailProps {
  drivers: VehicleResponse['drivers'];
  vehicleDetail: VehicleResponse;
  lastDriver: VehicleResponse['drivers'][number];
  user: MyUser;
  page: string;
}

export default function VehicleDetailLayout({ drivers, vehicleDetail, lastDriver, user, page }: DetailProps) {
  const ability = usePermissions();
  const stepName = vehicleDetail.step.stepName;
  const stepNumber = vehicleDetail.step.stepNumber;
  const { user: currentUser } = useCurrentUser();
  const driversWithouthLast = drivers.slice(0 /* , -1 */);
  const isDischarged = vehicleDetail.category === allCategory.withdrawn;
  const associateData = { ...lastDriver, carNumber: vehicleDetail.carNumber, vehicle: vehicleDetail };
  const returnStepModal = useReturnStepModal();
  const returnChangeRegion = useReturnChangeRegion();
  const search = useSearchParams();

  const country = search ? search.get('country') : '';
  const isSoldCar = vehicleDetail.status === 'sold';
  const physicalStatus = (vehicleDetail as any).physicalStatus;

  const canRegenarateContract = canPerform(
    ability,
    Capabilities.RegenarateContract,
    Sections.Fleet,
    Subsections.General
  );

  const canSendContractToSignature = canPerform(
    ability,
    Capabilities.SendContractToSignature,
    Sections.Fleet,
    Subsections.General
  );

  const canAssignDriver = canPerform(ability, Capabilities.AssignDriver, Sections.Fleet, Subsections.General);

  const canGenerateContract = canPerform(
    ability,
    Capabilities.GenerateContract,
    Sections.Fleet,
    Subsections.General
  );

  const canTerminateContract = canPerform(
    ability,
    Capabilities.TerminateContract,
    Sections.Fleet,
    Subsections.General
  );

  return (
    <CountryProvider>
      <VehicleDetailDataProvider
        associateData={associateData as unknown as AssociateData}
        vehicleData={vehicleDetail}
      >
        <div className="flex flex-col h-full gap-3 ">
          {drivers.length > 0 && stepNumber >= 3 && <AssociateModalData {...associateData} />}

          <Link
            href={`/dashboard/flotilla/${page}${country ? `?country=${encodeURI(country)}` : ''}`}
            prefetch={false}
          >
            <div className="fixed top-[12px] z-30 text-[#5800F7] flex items-center cursor-pointer">
              <RiArrowLeftSLine color="#5800F7" size={32} />
              <button>Regresar</button>
            </div>
          </Link>
          <div className="flex justify-between mb-4 gap-2 flex-wrap">
            <h1 className="text-[32px] font-bold text-[#262D33]">
              <span data-cy="cy-carNumber">{vehicleDetail.carNumber}</span>
              {vehicleDetail.extensionCarNumber ? ` - ${vehicleDetail.extensionCarNumber}` : ''} |{' '}
              {vehicleDetail.brand} {vehicleDetail.model}
            </h1>
            <div className="flex items-center gap-x-3 gap-y-2 md:gap-y-0 flex-wrap">
              {returnChangeRegion.isOpen && <ChangeRegion />}
              {returnStepModal.isOpen && <ReturnStepModal />}

              {(!isProd ||
                (!isSoldCar &&
                  vehicleDetail.step.stepNumber > 2 &&
                  emailUsersAllowed.includes(currentUser.email))) &&
                currentUser.role !== 'auditor' && (
                  <PrimaryButton onClick={returnStepModal.onOpen}>Regresar paso</PrimaryButton>
                )}
              {stepName.toLowerCase() === 'vehiculo listo' &&
                emailUsersAllowedRegion.includes(currentUser.email) && (
                  <PrimaryButton onClick={returnChangeRegion.onOpen}>Cambiar región</PrimaryButton>
                )}
              {stepName.toLowerCase() === 'contrato generado' && canRegenarateContract && (
                <>
                  <ResendContractToSign
                    lastDriver={lastDriver}
                    associateId={lastDriver?._id ? lastDriver._id : ''}
                    vehicleId={vehicleDetail._id}
                    vehicleDetail={vehicleDetail}
                  />
                </>
              )}
              {stepName.toLowerCase() === 'contrato generado' && canSendContractToSignature && (
                <>
                  <StartPayFlow vehicle={vehicleDetail} driver={lastDriver} />
                  <SendDocToSign
                    driver={lastDriver}
                    associateId={lastDriver?._id ? lastDriver._id : ''}
                    vehicleId={vehicleDetail._id}
                  />
                </>
              )}

              {!isDischarged && !isSoldCar && (
                <>
                  {stepName.toLowerCase() === 'vehiculo listo' || stepNumber < 3 ? (
                    canAssignDriver ? (
                      <AssignAssociateModal
                        user={user}
                        vehicleId={vehicleDetail._id}
                        vehicleCountry={vehicleDetail?.country!}
                      />
                    ) : null
                  ) : null}
                  {stepName.toLowerCase() === 'conductor asignado' && canGenerateContract && (
                    <GenerateContract initValues={lastDriver} vehicleDetail={vehicleDetail} user={user} />
                  )}
                  {stepName.toLowerCase() === 'contrato generado' && lastDriver && (
                    <UploadContractDocs
                      user={user}
                      associateId={lastDriver?._id ? lastDriver._id : ''}
                      driver={lastDriver}
                      vehicleDetail={vehicleDetail}
                    />
                  )}
                  {stepName.toLowerCase() === 'entregado' &&
                    ['activo', 'active', 'inactive'].includes(vehicleDetail.vehicleStatus) &&
                    canTerminateContract && <ReadmissionButton />}
                  {stepName.toLowerCase() === 'solicitud de reingreso' && canTerminateContract && (
                    <ReturnToStockModal />
                  )}
                </>
              )}
            </div>
          </div>
          {(page === 'activos' ||
            page === 'active' ||
            page?.includes('stock') ||
            page?.includes('revision') ||
            page?.includes('workshop') ||
            page?.includes('legal') ||
            page?.includes('insurance') ||
            page?.includes('in-preparation') ||
            page?.includes('assigned') ||
            page?.includes('delivered') ||
            page?.includes('utilitary') ||
            page?.includes('sold') ||
            page?.includes('adendum')) && <VehicleSteps data={vehicleDetail} driver={lastDriver} />}
          {(page === 'reingresos' || page?.includes('collection')) && (
            <StepsReadmissions data={vehicleDetail} driver={lastDriver} />
          )}

          <VehicleDetail data={vehicleDetail} associateData={lastDriver} />

          <>
            <div className="flex flex-wrap lg:flex-nowrap gap-[20px]">
              <div
                id="left"
                className="flex flex-wrap xl:flex-nowrap flex-row xl:flex-col gap-[10px] lg:gap-[20px] text-[#464E5F]"
              >
                <Placas data={vehicleDetail} />
                <CirculationCard data={vehicleDetail} />
                {currentUser.role !== 'auditor' && (
                  <GPSV2
                    gpsNumber={vehicleDetail.carNumber}
                    gpsSerie={vehicleDetail.carNumber}
                    gpsInstalled={vehicleDetail.gpsInstalled}
                    isCarBlocked={vehicleDetail.status === 'bloqueo'}
                  />
                )}
                <PhysicalStatusCard
                  physicalStatus={physicalStatus}
                  qrScanHistory={vehicleDetail.qrScanHistory as QRScanHistoryItem[]}
                  vehicleId={vehicleDetail._id}
                />
              </div>
              <div id="rigth" className="flex flex-col w-full gap-[20px] text-[#464E5F] overflow-x-hidden">
                {currentUser.role !== 'auditor' && vehicleDetail.status === 'sold' && (
                  <SoldBox data={vehicleDetail} />
                )}
                <Policy
                  policiesArray={vehicleDetail.policiesArray}
                  vehicleId={vehicleDetail._id}
                  data={vehicleDetail}
                />
                <Tenancy
                  tenancies={vehicleDetail.tenancy}
                  vehicle={vehicleDetail}
                  isCarBlocked={vehicleDetail.status === 'bloqueo'}
                />
                {(page === 'activos' || page === 'active') && currentUser.role !== 'auditor' && (
                  <PaymentsBox
                    carNumber={
                      vehicleDetail.extensionCarNumber
                        ? vehicleDetail.carNumber + -+vehicleDetail.extensionCarNumber
                        : vehicleDetail.carNumber
                    }
                    associateId={associateData._id}
                    clientId={lastDriver?.clientId}
                  />
                )}

                {currentUser.role !== 'auditor' && (
                  <>
                    <SiniestrosBox />
                    <GestoresBox />
                  </>
                )}
                {vehicleDetail.readmissions.length > 0 && (
                  <DriversBox drivers={driversWithouthLast} vehicleData={vehicleDetail} />
                )}
                {currentUser.role !== 'auditor' && <ViolationsBox vehicleData={vehicleDetail} />}
              </div>
            </div>
            {currentUser.role !== 'auditor' && (
              <div className="w-full">
                <History updateHistory={vehicleDetail.updateHistory} />
              </div>
            )}
          </>
        </div>
      </VehicleDetailDataProvider>
    </CountryProvider>
  );
}
