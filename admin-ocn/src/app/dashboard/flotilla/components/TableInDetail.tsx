'use client';
import { flexRender, getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table';
// import CellAction from './CellAction';

export default function TableInDetail({
  data,
  columns,
  isDisplayPagination = false,
}: {
  data: any;
  columns: any;
  isDisplayPagination?: boolean;
}) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <>
      <table className="w-full h-[max-content] caption-bottom text-sm divide-y divide-gray-200">
        <thead className="[&_tr]:border-b">
          {table.getHeaderGroups().map((headerGroup, i) => (
            <tr
              key={i}
              className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
            >
              {headerGroup.headers.map((header, index) => {
                return (
                  <th
                    key={index}
                    className="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                );
              })}
            </tr>
          ))}
        </thead>
        <tbody className="[&_tr:last-child]:border-0">
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row, i) => {
              // console.log(row, index, rows);
              return (
                <tr
                  className="even:bg-transparent odd:bg-gray-200 "
                  key={i}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell, index, cells) => (
                    <td
                      key={cell.id}
                      className={`
                    ${index === cells.length - 1 && 'rounded-r-lg'} 
                    ${index === 0 && 'rounded-l-lg'}
                    py-2 px-4  text-left align-middle [&:has([role=checkbox])]:pr-0`}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              );
            })
          ) : (
            <tr>
              <td colSpan={columns.length} className="h-24 text-center">
                No hay resultados.
              </td>
            </tr>
          )}
        </tbody>
      </table>
      {isDisplayPagination && table.getPageCount() > 1 && (
        <div className="flex justify-between items-center mt-4">
          <button
            className="px-3 py-1 border rounded disabled:opacity-50"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Anterior
          </button>
          <span>
            Página {table.getState().pagination.pageIndex + 1} de {table.getPageCount()}
          </span>
          <button
            className="px-3 py-1 border rounded disabled:opacity-50"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Siguiente
          </button>
        </div>
      )}
    </>
  );
}
