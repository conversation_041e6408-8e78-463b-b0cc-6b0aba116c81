/* eslint-disable @typescript-eslint/no-use-before-define */
'use client';
import CustomInput from '@/components/Inputs/CustomInput';
import InputFile from '@/components/Inputs/InputFile';
import CustomDatePicker from '@/components/Inputs/CustomDatePicker';
import ZoomImage from '@/app/dashboard/flotilla/components/others/ZoomImage';
import CustomModal from '@/components/Modals/CustomModal';
import { addCirculationCard } from '@/validatorSchemas/vehicleDetailSchema';
import { useEffect, useState } from 'react';
import { VehicleResponse } from '@/actions/getVehicleData';
import { useSession } from 'next-auth/react';
import { FormikValues, useFormikContext } from 'formik';
import axios from 'axios';
import Spinner from '@/components/Loading/Spinner';
import { URL_API, editTextBtn, DocumentCategory, Capabilities, Sections, Subsections } from '@/constants';
import { useToast, Alert, AlertIcon, Text } from '@chakra-ui/react';
import SelectInput from '@/components/Inputs/SelectInput';
import { getBaseDocumentQualityMessage, getDocumentRequiredFieldsOnly } from '@/utils/documentInfoMessages';
import { useParams } from 'next/navigation';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import ModalContainer from '../Modals/ModalContainer';
import { useCirculationCardTijModal } from '@/zustand/modalStates';
import DeleteVehicleDocs from '../Buttons/DeleteVehicleDocs';
import RequiredBadge from '../others/RequiredBadge';
import ExpiredBadge from '../others/ExpiredBadge';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { extractDataFromDocument, showOCRToast } from '@/utils/ocrApi';
import OCRConfirmationModal from '@/components/Modals/OCRConfirmationModal';
import { ContractRegionCode } from '@/constants';

interface CirculationCardProps {
  data: VehicleResponse;
}
const initialValues = { number: '', validity: '', frontImg: '', backImg: '' };

const tijOptions = [
  {
    value: 'editar',
    label: 'Editar',
  },
  {
    value: 'agregar',
    label: 'Agregar nuevo',
  },
];

export default function CirculationCard({ data }: CirculationCardProps) {
  const [nameFiles, setNameFiles] = useState({
    number: '',
    validity: '',
    frontImg: '',
    backImg: '',
  });
  const [selectedOption, setSelectedOption] = useState({ value: 'editar', label: 'Editar' });
  const handleSetName = (name: string, value: string) => {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  };
  const { id } = useParams();

  const ability = usePermissions();
  const canAddCirculationCard = canPerform(
    ability,
    Capabilities.AddCirculationCard,
    Sections.Fleet,
    Subsections.General
  );
  const canEditCirculationCard = canPerform(
    ability,
    Capabilities.EditCirculationCard,
    Sections.Fleet,
    Subsections.General
  );

  const [circulationArray, setCirculationArray] = useState<VehicleResponse['circulationCard'][]>([]);
  const { user } = useCurrentUser();

  useEffect(() => {
    const getCirculationArray = async () => {
      try {
        const response = await axios.get(`${URL_API}/stock/circulationCard/tij/${id}`, {
          headers: {
            Authorization: `bearer ${user.accessToken}`,
          },
        });
        setCirculationArray(response.data.data);
      } catch (error) {
        console.error(error);
      }
    };
    if (data.circulationCard && data.vehicleState === 'tij') {
      getCirculationArray();
    }
  }, [data.circulationCard, data.vehicleState, id, user.accessToken]);

  const [isLoading, setIsLoading] = useState(false);

  const onClose = () => {
    setNameFiles({
      number: '',
      validity: '',
      frontImg: '',
      backImg: '',
    });
  };
  const backendValues = {
    number: data.circulationCard?.number || '',
    validity: data.circulationCard?.validity || '',
    frontImg: '',
    backImg: '',
  };

  const { data: session } = useSession();

  const toast = useToast();

  const onSubmit = async (values: FormikValues, isEditing?: boolean) => {
    setIsLoading(true);
    const formData = {
      ...values,
      historyData: {
        userId: user._id,
        step: 'DOCUMENTACIÓN',
        description: isEditing ? 'Tarjeta de circulación editado' : 'Tarjeta de circulación agregado',
      },
      // eslint-disable-next-line no-unneeded-ternary
      isEditing: isEditing ? true : false,
    };

    // if (values.op)
    if (values.option?.value === tijOptions[1].value) {
      // formData._id = data.circulationCard?._id;

      const response = await axios
        .patch(`${URL_API}/stock/update/circulationCard/tij/${data._id}`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `bearer ${session ? user.accessToken : null}`,
          },
        })
        .catch(() => {
          toast({
            title: 'Faltan campos',
            duration: 6000,
            status: 'error',
            position: 'top',
          });
        })
        .finally(() => setIsLoading(false));
      return response;
    }

    const response = await axios
      .patch(`${URL_API}/stock/update/circulationCard/${data._id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `bearer ${session ? user.accessToken : null}`,
        },
      })
      .catch((err) => {
        toast({
          title: err.response.data.message,
          duration: 6000,
          status: 'error',
          position: 'top',
        });
      })
      .finally(() => setIsLoading(false));
    setIsLoading(false);
    return response;
  };

  const circulationTijData = useCirculationCardTijModal();

  if (isLoading) return <Spinner />;

  return (
    <>
      {circulationArray.length > 0 && circulationTijData.isOpen && (
        <CirculationCardModalData circulationCard={circulationArray} />
      )}

      <div
        className={`
        w-[316px]
        bg-[white]
        flex flex-col
        gap-3
        py-[25px]
        ${data.circulationCard ? 'px-[20px]' : 'pl-[20px] pr-[15px]'}
        border-[1px]
        border-[#EAECEE]
        font-bold
        rounded`}
      >
        {data.circulationCard ? (
          <>
            <>
              <div className="flex justify-between">
                <p className="font-bold text-[24px] ">Tarjeta circulación</p>

                <div className="flex gap-2">
                  <DeleteVehicleDocs documentToDelete="circulationCard" vehicle={data} />
                  {canEditCirculationCard && (
                    <CustomModal
                      testId="circulationCard"
                      openButtonText="Editar tarjeta de circulación"
                      confirmButtonText={editTextBtn}
                      isPrimaryButton={true}
                      validatorSchema={addCirculationCard}
                      body={
                        data.vehicleState !== 'tij' ? (
                          <Body
                            handleSetName={handleSetName}
                            nameFiles={nameFiles}
                            isEdit
                            circulationCard={data.circulationCard}
                            vehicleId={data._id}
                            vehicleState={data.vehicleState}
                          />
                        ) : (
                          <BodyForTij
                            circulationCard={data.circulationCard}
                            isEdit
                            handleSetName={handleSetName}
                            selectedOption={selectedOption}
                            setSelectedOption={setSelectedOption}
                            nameFiles={nameFiles}
                            vehicleState={data.vehicleState}
                            vehicleId={data._id}
                          />
                        )
                      }
                      initialValues={{
                        ...backendValues,
                        isEditing: true,
                        vehicleState: data.vehicleState,
                        option: { value: 'editar', label: 'Editar' },
                      }}
                      onCloseModal={onClose}
                      header="Editar tarjeta de circulación"
                      isUpdate
                      updateIconColor="#5800F7"
                      onSubmit={(values) => onSubmit(values, true)}
                    />
                  )}
                </div>
              </div>
              <div className="grid gap-2">
                <div className="flex gap-3 items-center justify-between">
                  <p className="font-bold text-[18px] ">No. {data.circulationCard.number}</p>
                  {new Date(data.circulationCard?.validity) < new Date() && <ExpiredBadge />}
                  {circulationArray.length > 0 && (
                    <p className="font-normal cursor-pointer" onClick={circulationTijData.onOpen}>
                      Ver más
                    </p>
                  )}
                </div>
                <div className="flex justify-between">
                  <p className="font-normal">Vigencia</p>
                  <p className="font-bold text-[16px] ">{data.circulationCard.validity}</p>
                </div>
                <div className="flex justify-between">
                  {data.circulationCard.frontImg && (
                    <ZoomImage
                      imageUrl={data.circulationCard.frontImg.url}
                      name={data.circulationCard.frontImg.originalName}
                    />
                  )}
                  {data.circulationCard.backImg && (
                    <ZoomImage
                      imageUrl={data.circulationCard.backImg.url}
                      name={data.circulationCard.backImg.originalName}
                    />
                  )}
                </div>
              </div>
            </>
          </>
        ) : (
          <>
            <div className="flex justify-between items-center">
              <p className="font-bold text-[24px] ">Tarjeta circulación </p>
              <RequiredBadge />
            </div>
            <p className="text-[#9CA3AF]">Este vehículo aún no tiene una tarjeta de circulación asignada</p>
            {canAddCirculationCard && (
              <CustomModal
                testId="circulationCard"
                openButtonText="Agregar tarjeta de circulación"
                confirmButtonText="Agregar"
                isPrimaryButton={true}
                validatorSchema={addCirculationCard}
                body={
                  <Body
                    handleSetName={handleSetName}
                    nameFiles={nameFiles}
                    vehicleId={data._id}
                    allowOCRFields={true}
                    vehicleState={data.vehicleState}
                  />
                }
                initialValues={{ ...initialValues, vehicleState: data.vehicleState }}
                onCloseModal={onClose}
                header="Agregar tarjeta de circulación"
                onSubmit={async (values) => onSubmit(values, false)}
              />
            )}
          </>
        )}
      </div>
    </>
  );
}

interface BodyParams {
  circulationCard?: VehicleResponse['circulationCard'] | null;
  nameFiles: {
    number: string;
    validity: string;
    frontImg: string;
    backImg: string;
  };
  handleSetName: (name: string, value: string) => void;
  isEdit?: boolean;
  vehicleId?: string;
  allowOCRFields?: boolean; // New prop to explicitly control OCR field display
  vehicleState?: string; // Add vehicleState to determine if CDMX
}

function Body({
  circulationCard,
  nameFiles,
  handleSetName,
  isEdit = false,
  vehicleId,
  vehicleState,
}: BodyParams) {
  const { user } = useCurrentUser();
  const toast = useToast();
  const { setFieldValue } = useFormikContext();
  const [isProcessingOCR, setIsProcessingOCR] = useState({ frontImg: false, backImg: false });
  const [hasSuccessfulExtraction, setHasSuccessfulExtraction] = useState(false);

  // OCR Confirmation Modal state
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [pendingOCRResult, setPendingOCRResult] = useState<any>(null);
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  const [pendingFieldName, setPendingFieldName] = useState<string>('');

  // Determine if this is a CDMX vehicle
  const isCDMXVehicle = vehicleState === ContractRegionCode.CDMX;

  // Get base message and specific field messages using centralized utility
  const baseQualityMessage = getBaseDocumentQualityMessage(false);
  const frontCardFields = getDocumentRequiredFieldsOnly(DocumentCategory.CIRCULATION_CARD_FRONT, false);
  const backCardFields = getDocumentRequiredFieldsOnly(DocumentCategory.CIRCULATION_CARD_BACK, false);

  const handleCirculationCardUpload = async (fileList: FileList | null, fieldName: string) => {
    if (!fileList || !fileList[0] || !user?.accessToken) return;

    const file = fileList[0];

    // For non-CDMX vehicles, skip OCR verification for back image
    if (fieldName === 'backImg' && !isCDMXVehicle) {
      // Just set the file without OCR processing for non-CDMX back images
      setFieldValue(fieldName, file);
      handleSetName(fieldName, file.name);
      return;
    }

    // Process OCR for front images (all vehicles) and back images (CDMX only)
    if (fieldName === 'frontImg' || (fieldName === 'backImg' && isCDMXVehicle)) {
      setIsProcessingOCR((prev) => ({ ...prev, [fieldName]: true }));

      try {
        const documentType = fieldName === 'frontImg' ? 'CIRCULATION_CARD_FRONT' : 'CIRCULATION_CARD_BACK';

        const result = await extractDataFromDocument(
          {
            document: file,
            documentType: documentType,
            vehicleId: vehicleId,
          },
          user.accessToken
        );

        if (result.success) {
          // Process extraction (valid document)
          processExtractedData(result.data, fieldName);
          showOCRToast(toast, result);
        } else {
          // Show confirmation modal for errors instead of hard rejection
          setPendingOCRResult(result);
          setPendingFile(file);
          setPendingFieldName(fieldName);
          setIsConfirmationModalOpen(true);
          setIsProcessingOCR((prev) => ({ ...prev, [fieldName]: false }));
          return;
        }
      } catch (error) {
        console.error('OCR extraction failed:', error);

        // Show generic error for processing failures
        toast({
          title: 'Error al procesar documento',
          description: 'No se pudo procesar el documento. Por favor, intente con otro archivo.',
          status: 'error',
          duration: 5000,
          position: 'top',
        });

        // Clear the file from form and display on processing error
        setFieldValue(fieldName, null);
        handleSetName(fieldName, '');

        // Only reset front extraction success if this was a front image error
        if (fieldName === 'frontImg') {
          setHasSuccessfulExtraction(false);
        }
      } finally {
        setIsProcessingOCR((prev) => ({ ...prev, [fieldName]: false }));
      }
    }
  };

  // Handle confirmation modal actions
  const handleConfirmWithErrors = () => {
    if (pendingOCRResult && pendingFile && pendingFieldName) {
      // Process the document despite errors
      processExtractedData(pendingOCRResult.data, pendingFieldName);
      showOCRToast(toast, pendingOCRResult, true); // Show warning toast for accepted invalid document

      // Set the file in the form
      setFieldValue(pendingFieldName, pendingFile);
      handleSetName(pendingFieldName, pendingFile.name);

      // Reset modal state
      setIsConfirmationModalOpen(false);
      setPendingOCRResult(null);
      setPendingFile(null);
      setPendingFieldName('');
    }
  };

  const handleCancelWithErrors = () => {
    // Clear everything and reset modal state
    if (pendingFieldName) {
      setFieldValue(pendingFieldName, null);
      handleSetName(pendingFieldName, '');

      // Only reset front extraction success if this was a front image error
      if (pendingFieldName === 'frontImg') {
        setHasSuccessfulExtraction(false);
      }
    }

    setIsConfirmationModalOpen(false);
    setPendingOCRResult(null);
    setPendingFile(null);
    setPendingFieldName('');
  };

  // Helper function to process extracted data
  const processExtractedData = (data: any, fieldName: string) => {
    // Only auto-fill fields for front image OCR (back is for validation only)
    if (fieldName === 'frontImg' && data) {
      setHasSuccessfulExtraction(true);

      // Auto-fill the extracted fields
      if (data.number) {
        setFieldValue('number', data.number);
      }
      if (data.validity) {
        setFieldValue('validity', data.validity);
      }
    }
  };

  return (
    <div className="flex flex-col w-full gap-3 overflow-hidden">
      {/* Base info message about document quality and visibility - shown once at the top */}
      <Alert
        status="info"
        borderRadius="md"
        bg="#F4EEFF"
        color="#5800F7"
        sx={{
          '& .chakra-alert__icon': {
            color: '#5800F7',
          },
        }}
      >
        <AlertIcon />
        <Text fontWeight="400" color="#5800F7">
          {baseQualityMessage}
        </Text>
      </Alert>

      <>
        {/* Show fields only in edit mode or after successful OCR extraction */}
        {(isEdit || hasSuccessfulExtraction) && (
          <>
            <CustomInput label="No. de tarjeta de circulación" name="number" type="text" />
            <CustomDatePicker label="Vigencia" name="validity" limitPastDates={true} />
          </>
        )}

        {/* Front circulation card image section */}
        <div className="border-t pt-3">
          <InputFile
            nameFile={
              isEdit && nameFiles.frontImg === '' && circulationCard?.frontImg
                ? (circulationCard.frontImg.originalName as string)
                : nameFiles.frontImg
            }
            name="frontImg"
            label="Foto de circulación delantera"
            handleSetName={handleSetName}
            accept="all-images"
            buttonText={
              isProcessingOCR.frontImg ? 'procesando...' : isEdit ? 'Reemplazar foto' : 'Subir foto'
            }
            placeholder="Imagen no mayor a 2 MB"
            onChange={(fileList) => handleCirculationCardUpload(fileList, 'frontImg')}
            isLoading={isProcessingOCR.frontImg}
            loadingText="procesando..."
            infoMessage={frontCardFields ? `Datos requeridos: ${frontCardFields}` : undefined}
          />
        </div>

        {/* Back circulation card image section - Show for all vehicles, but with different behavior */}
        <div className="border-t pt-3">
          <InputFile
            nameFile={
              isEdit && nameFiles.backImg === '' && circulationCard?.backImg
                ? (circulationCard.backImg.originalName as string)
                : nameFiles.backImg
            }
            name="backImg"
            label={`Foto de circulación trasera${!isCDMXVehicle ? ' (opcional)' : ''}`}
            handleSetName={handleSetName}
            accept="all-images"
            buttonText={isProcessingOCR.backImg ? 'procesando...' : isEdit ? 'Reemplazar foto' : 'Subir foto'}
            placeholder="Imagen no mayor a 2 MB"
            onChange={(fileList) => handleCirculationCardUpload(fileList, 'backImg')}
            isLoading={isProcessingOCR.backImg}
            loadingText="procesando..."
            infoMessage={
              isCDMXVehicle && backCardFields
                ? `Datos requeridos: ${backCardFields}`
                : !isCDMXVehicle
                ? 'Imagen opcional - No requiere verificación'
                : undefined
            }
          />
        </div>
      </>

      {/* OCR Confirmation Modal */}
      <OCRConfirmationModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        onConfirm={handleConfirmWithErrors}
        onCancel={handleCancelWithErrors}
        ocrResult={pendingOCRResult}
        documentType={pendingFieldName === 'frontImg' ? 'CIRCULATION_CARD_FRONT' : 'CIRCULATION_CARD_BACK'}
      />
    </div>
  );
}

interface TijBodyProps extends BodyParams {
  vehicleState: string;
  selectedOption: { value: string; label: string };
  setSelectedOption: (option: { value: string; label: string }) => void;
}

function BodyForTij({
  circulationCard,
  nameFiles,
  handleSetName,
  setSelectedOption,
  selectedOption,
  vehicleId,
}: TijBodyProps) {
  //

  const formik = useFormikContext();

  return (
    <div className="flex flex-col gap-3">
      <SelectInput
        label="Seleccione una opción"
        name="option"
        options={tijOptions}
        onChange={(option) => {
          setSelectedOption(option);
          if (option.value === 'agregar') {
            formik.setFieldValue('number', '');
          } else {
            formik.setErrors({});
            formik.setTouched({});
            formik.setFieldValue('number', circulationCard?.number);
          }
        }}
      />
      {selectedOption.value === 'editar' && (
        <Body
          circulationCard={circulationCard}
          nameFiles={nameFiles}
          handleSetName={handleSetName}
          isEdit
          vehicleId={vehicleId}
          allowOCRFields={false} // Don't show OCR fields when editing existing
          vehicleState={ContractRegionCode.TIJ}
        />
      )}
      {selectedOption.value === 'agregar' && (
        <Body
          handleSetName={handleSetName}
          nameFiles={nameFiles}
          vehicleId={vehicleId}
          allowOCRFields={true} // Allow OCR fields when adding new
          vehicleState={ContractRegionCode.TIJ}
        />
      )}
    </div>
  );
}

function CirculationCardModalData({
  circulationCard,
}: {
  circulationCard: VehicleResponse['circulationCard'][];
}) {
  const circulationTijData = useCirculationCardTijModal();

  return (
    <ModalContainer
      className="min-w-[800px] w-[50%] "
      title="Información anterior de tarjetas de circulación"
      onClose={circulationTijData.onClose}
      removeScrollBar={false}
    >
      <p>Ordenadas por más recientes primero</p>
      <div className="grid grid-cols-2 gap-3">
        {circulationCard.map((card, index) => (
          <div
            key={index}
            className={`
            flex flex-col gap-3
            border-[1px]
            border-[#EAECEE]
            rounded
            p-[10px]
            `}
          >
            <div className="flex gap-3 items-center justify-between">
              <div className="flex items-center gap-2">
                {new Date(card.validity) < new Date() && <ExpiredBadge />}
                <p className="font-bold text-[18px]">No. {card.number}</p>
              </div>
            </div>
            <div className="flex justify-between">
              <p className="font-normal">Vigencia</p>
              <p className="font-bold text-[16px] ">{card.validity}</p>
            </div>
            <div className="flex justify-between">
              {card.frontImg && <ZoomImage imageUrl={card.frontImg.url} name={card.frontImg.originalName} />}
              {card.backImg && <ZoomImage imageUrl={card.backImg.url} name={card.backImg.originalName} />}
            </div>
          </div>
        ))}
      </div>
    </ModalContainer>
  );
}
