/* eslint-disable prettier/prettier */
'use client';
import { VehicleResponse } from '@/actions/getVehicleData';
import ContractModal from '@/components/Modals/ContractModal';
import { FormikValues } from 'formik';
import { createContext, useState, useContext, Dispatch, SetStateAction } from 'react';
import { pdf } from '@react-pdf/renderer';
import axios from 'axios';
import {
  CONTRACT_REGIONS,
  Countries,
  CountriesOptions,
  TIMEZONES_FOR_REGIONS_MX,
  URL_API,
  US_CITIES_OPTIONS,
  US_STATES_OBJ,
  cities,
  federalEntities,
  getFullAddressString,
} from '@/constants';
import { MyUser } from '@/actions/getCurrentUser';
import { DocumentComponent } from '@/pdfComponents/contract/ContractDocument';
import { getHeadContract } from '@/pdfComponents/contract/data/ContractHeadData';
import { paymentsArray, siguienteLunes } from '@/pdfComponents/contract/data/Lunes';
// import { SemiContractDocumentPDF } from '@/pdfComponents/contract/SemiNewContract';
import moment from 'moment';
import { useParams } from 'next/navigation';
import ContractModalUS from '@/components/Modals/ContractModalUs';
import { DocumentComponentUS } from '@/pdfComponents/contract/ContractDocumentUS';
// import { differenceInWeeks, parse } from 'date-fns';

interface ContextProps {
  form: FormikValues;
  setForm: Dispatch<SetStateAction<FormikValues>>;
}

const initialValues: FormikValues = {
  city: '',
  name: '',
  lastName: '',
  phone: '',
  tel: '',
  email: '',
  contractNumber: '',
  street: '',
  interior: '',
  exterior: '',
  colony: '',
  zip: '',
  townHall: '',
  federalEntity: '',
  rfc: '',
  sameAddress: '1',
  streetDos: '',
  interiorDos: '',
  exteriorDos: '',
  colonyDos: '',
  zipDos: '',
  townHallDos: '',
  federalEntityDos: '',
  brand: '',
  model: '',
  version: '',
  weeklyRent: '',
  finalPrice: 100000,
  wroteFinalPrice: '',
  insurance: { label: '', value: '' },
  initialPayment: '',
  deliverDate: '',
  isSameDate: { label: 'Sí', value: 'Si' },
  startDate: '',
  deliverer: '',
  downPayment: '', // enganche
  rentingType: { label: 'Selecciona', value: '' },
  noDownPayment: '',
  products: '',
  rentingProduct: undefined,
  assistanceProduct: undefined,
  downPaymentProduct: undefined,
  depositProduct: undefined,
  country: '',
  state: '',
  ssn: '',
  withAval: true,
};

const defaultContextValues: ContextProps = {
  form: initialValues,
  setForm: () => {},
};

export const DetailContext = createContext<ContextProps>(defaultContextValues);

export const useContract = () => {
  const context = useContext(DetailContext);
  // if (!context) throw new Error('There is not auth context');
  return context;
};

interface GenerateContractProps {
  initValues?: FormikValues;
  vehicleDetail: VehicleResponse;
  user: MyUser;
}

// Proveedor del contexto
export default function GenerateContract({ initValues, vehicleDetail, user }: GenerateContractProps) {
  const driverCity = vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.city;

  const combinedValues = {
    ...initialValues,
    ...initValues,
    ...vehicleDetail,
    contractNumber: vehicleDetail.carNumber,
    street: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.addressStreet || '',
    exterior: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.exterior || '',
    interior: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.interior || '',
    delegation: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.delegation || '',
    fullAddress: getFullAddressString(vehicleDetail.drivers[vehicleDetail.drivers.length - 1]),
    postalCode: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.postalCode || '',
    colony: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.colony || '',
    finalPrice: getPriceBasedOnVehicleModel(vehicleDetail.model),
    insurance: { value: 'qualitas', label: 'Qualitas' },
    paymentsDone: '',
    totalPays: '',
    policyNumber: vehicleDetail.policiesArray[vehicleDetail.policiesArray.length - 1]?.policyNumber,
    plates: vehicleDetail.carPlates?.plates,
    circulationCardNumber: vehicleDetail.circulationCard?.number,
    country: CountriesOptions[vehicleDetail.country as keyof typeof CountriesOptions] || {
      value: '',
      label: '',
    },
    state:
      vehicleDetail.country === Countries['United States']
        ? US_STATES_OBJ[
            vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address
              .state as keyof typeof US_STATES_OBJ
          ]
        : federalEntities[vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.state] || { label: '', value: '' },
    city:
      vehicleDetail.country === Countries['United States']
        ? US_CITIES_OPTIONS[
            vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address
              .city as keyof typeof US_CITIES_OPTIONS
          ]
        : cities[vehicleDetail.vehicleState] ||
        CONTRACT_REGIONS.find((region) => {
          if (region.value === driverCity || region.label === driverCity) {
            return {
              value: region.value,
              label: region.label,
            }
          }
          return {
            value: '',
            label: '',
          };
        }),
    ssn: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.ssn,
  } as FormikValues;


  if ('deliveredDate' in combinedValues) {
    delete combinedValues.deliveredDate;
  }

  const [form, setForm] = useState(combinedValues);

  const params = useParams();

  if (user.role === 'auditor') return null;

  return (
    <DetailContext.Provider value={{ form, setForm }}>
      {combinedValues.country.value !== Countries['United States'] ? (
        <ContractModal
          header="Generar contrato"
          openButtonText="Generar contrato"
          onCloseModal={() => {}}
          onSubmit={async (data) => {
            if (!data.phone.startsWith('+52')) {
              data.phone = '+52' + data.phone;
            }
            const products = {
              rentingProduct: data.rentingProduct,
              assistanceProduct: data.assistanceProduct,
              downPaymentProduct: data.downPaymentProduct,
              depositProduct: data.depositProduct,
            };

            const deliveredDate = data.deliverDate;

            const isElectric = vehicleDetail.isElectric;

            const city = data.city?.value as string;
            const firstName1 = data.firstName as string;
            const lastName1 = data.lastName as string;
            const extension = vehicleDetail.extensionCarNumber ? `-${vehicleDetail.extensionCarNumber}` : '';
            const headContract = getHeadContract(city, firstName1, lastName1, isElectric);

            const justDate = (data.startDate || data.deliverDate).split('T')[0];
            const firstResponse = await axios(`${URL_API}/contract/get/vehiclePayments/last/${params.id}`, {
              headers: {
                Authorization: `Bearer ${user.accessToken}`,
              },
            });

            const lastPaymentId = firstResponse.data.lastPayment?._id;

            // This is for semi new contracts
            if (lastPaymentId && data.paymentsDone) {
              await axios.patch(
                `${URL_API}/contract/updatePayment/${lastPaymentId}`,
                {
                  paymentNumber: Number(data.paymentsDone),
                },
                {
                  headers: {
                    Authorization: `Bearer ${user.accessToken}`,
                  },
                }
              );
            }

            const date = moment(justDate);

            const nextMondey = siguienteLunes(date);
            const allPayments = paymentsArray(nextMondey, data.totalPays);
            const restPayments = allPayments.slice(0, data.totalPays);

            const blob = await pdf(
              <DocumentComponent form={data} headContract={headContract} totalWeeks={data.totalPays} />
            ).toBlob();
            const buffer = await blob.arrayBuffer();

            const firstName = data.firstName.split('')[0].toUpperCase() + data.firstName.slice(1);
            const lastName = data.lastName.split('')[0].toUpperCase() + data.lastName.slice(1);

            const archivo = new File(
              [buffer],
              `${firstName}${lastName}-${data.contractNumber}${extension}.pdf`,
              {
                type: 'application/pdf',
              }
            );
            const dataToSend = {
              contract: archivo,
            };

            const deliveryDateContractTimezone =
              TIMEZONES_FOR_REGIONS_MX[city] || TIMEZONES_FOR_REGIONS_MX.cdmx;

            const payments = {
              ...products,
              weeklyRent: Number(data.weeklyRent),
              finalPrice: Number(data.finalPrice),
              allPayments,
              downPayment: Number(data.downPayment),
              totalPrice: Number(Number(data.weeklyRent) * 156 + Number(data.downPayment)),
              stockId: vehicleDetail._id,
              associatedId: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]._id,
              contract: dataToSend.contract,
              // documentId: response.data.currentAssociate.unSignedContractDoc,
              contractNumber: vehicleDetail.carNumber,
              deliveredDate,
              totalPayments: Number(data.totalPayments),
              restPayments,
              rentingType: data.rentingType.value,
              products: data.products,
              downPaymentProduct: data.downPaymentProduct,
              depositProduct: data.depositProduct,
              deliveryDate: deliveredDate,
              deliveryDateContractTimezone,
            };

            await axios.post(`${URL_API}/contract/associated/add`, payments, {
              headers: {
                'Content-Type': 'multipart/form-data',
                Authorization: `Bearer ${user.accessToken}`,
              },
            });
            return 'Contrato generado';
          }}
          onChangeStep={(values) => {
            /* if (values.startDate) {
            values.deliverDate = values.startDate;
          } */

            setForm({
              ...form,
              ...values,
            });
          }}
          initialValues={form}
        />
      ) : (
        <ContractModalUS
          header="Generate Contract"
          openButtonText="Generate Contract"
          onCloseModal={() => {}}
          onSubmit={async (data) => {
            if (!data.phone.startsWith('+1')) {
              data.phone = '+1' + data.phone;
            }
            const extension = vehicleDetail.extensionCarNumber ? `-${vehicleDetail.extensionCarNumber}` : '';
            const isElectric = vehicleDetail.isElectric;
            const headContract = getHeadContract(data.city?.value, data.firstName, data.lastName, isElectric);

            const deliveredDate = data.deliverDate;

            const firstResponse = await axios(`${URL_API}/contract/get/vehiclePayments/last/${params.id}`, {
              headers: {
                Authorization: `Bearer ${user.accessToken}`,
              },
            });

            // This is for semi new contracts
            const lastPaymentId = firstResponse.data.lastPayment?._id;
            if (lastPaymentId && data.paymentsDone) {
              await axios.patch(
                `${URL_API}/contract/updatePayment/${lastPaymentId}`,
                {
                  paymentNumber: Number(data.paymentsDone),
                },
                {
                  headers: {
                    Authorization: `Bearer ${user.accessToken}`,
                  },
                }
              );
            }
            const deliverDateWithoutTime = moment(data.deliverDate.split('T')[0]);
            const nextMondey = siguienteLunes(deliverDateWithoutTime);
            const allPayments = paymentsArray(nextMondey, data.totalPays);
            const restPayments = allPayments.slice(0, data.totalPays);

            const blob = await pdf(
              <DocumentComponentUS form={data} headContract={headContract} totalWeeks={data.totalPays} />
            ).toBlob();

            const buffer = await blob.arrayBuffer();
            const upperCaseFirstName = data.firstName.split('')[0].toUpperCase() + data.firstName.slice(1);
            const upperCaseLastName = data.lastName.split('')[0].toUpperCase() + data.lastName.slice(1);

            const archivo = new File(
              [buffer],
              `${upperCaseFirstName}${upperCaseLastName}-${data.contractNumber}${extension}.pdf`,
              {
                type: 'application/pdf',
              }
            );
            const dataToSend = {
              contract: archivo,
            };

            const weeklyPayment = Number(data.rentingProduct.subTotal || 0);
            const products = {
              rentingProduct: data.rentingProduct,
              assistanceProduct: data.assistanceProduct,
              downPaymentProduct: data.downPaymentProduct,
              depositProduct: data.depositProduct,
            };

            const totalPays = data.totalPays || data.totalPays > 0 ? data.totalPays : 156;
            // If totalPays doesn't exist or is less than 0, then it will be 156, otherwise it will be the value of totalPays

            const deliveryDateContractTimezone =
              TIMEZONES_FOR_REGIONS_MX[data.city?.value] || TIMEZONES_FOR_REGIONS_MX.cdmx;

            const payments = {
              ...products,
              weeklyRent: weeklyPayment,
              finalPrice: Number(data.finalPrice),
              allPayments,
              downPayment: Number(data.downPayment),
              totalPrice: Number(weeklyPayment * totalPays),
              stockId: vehicleDetail._id,
              associatedId: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]._id,
              contract: dataToSend.contract,
              contractNumber: vehicleDetail.carNumber,
              deliveredDate: deliveredDate,
              totalPayments: Number(data.totalPayments),
              restPayments,
              rentingType: data.rentingType.value,
              deliveryDate: deliveredDate,
              deliveryDateContractTimezone,
            };

            await axios.post(`${URL_API}/contract/associated/add`, payments, {
              headers: {
                'Content-Type': 'multipart/form-data',
                Authorization: `Bearer ${user.accessToken}`,
              },
            });
          }}
          onChangeStep={(values) => {
            setForm({
              ...form,
              ...values,
            });
          }}
          initialValues={form}
        />
      )}
    </DetailContext.Provider>
  );
}

const finalPricing: { [key: string]: number } = {
  tiggo: 108000,
  dolphin: 124740,
  mg3: 73770,
  mg5: 101970,
  '2 smart': 89700,
};

function getPriceBasedOnVehicleModel(model: string) {
  // need to check if model includes the word "tiggo" or "dolphin" to match the model totalPricing object

  const modelsAvailable = Object.keys(finalPricing);

  const modelFound = modelsAvailable.find((modelAvailable) =>
    model.toLocaleLowerCase().includes(modelAvailable)
  );

  if (modelFound) {
    return finalPricing[modelFound];
  }
  return 100000;
}
