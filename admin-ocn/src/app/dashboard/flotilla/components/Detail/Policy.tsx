'use client';
import { MyUser } from '@/actions/getCurrentUser';
import { VehicleResponse } from '@/actions/getVehicleData';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import DocumentDisplay from '@/components/DocumentDisplay';
import CustomInput from '@/components/Inputs/CustomInput';
import CustomDatePicker from '@/components/Inputs/CustomDatePicker';
import InputFile from '@/components/Inputs/InputFile';
import CustomModal from '@/components/Modals/CustomModal';
import { addPolicySchema } from '@/validatorSchemas/vehicleDetailSchema';
import axios from 'axios';
import { FormikValues, useFormikContext } from 'formik';
import { useSession } from 'next-auth/react';
import { useCallback, useMemo, useState } from 'react';
import Spinner from '@/components/Loading/Spinner';
import isNearOneWeek from '@/utils/isNearOneWeek';
import { Capabilities, editTextBtn, Sections, Subsections } from '@/constants';
import DeleteVehicleDocs from '../Buttons/DeleteVehicleDocs';
import RequiredBadge from '../others/RequiredBadge';
import ExpiredBadge from '../others/ExpiredBadge';
import LatestBadge from '../others/LatestBadge';
import { canPerform } from '@/casl/canPerform';
import { usePermissions } from '@/casl/PermissionsContext';
import { extractDataFromDocument, showOCRToast } from '@/utils/ocrApi';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useToast, Alert, AlertIcon, Text } from '@chakra-ui/react';
import { DocumentCategory } from '@/constants';
import { getDocumentInfoMessage } from '@/utils/documentInfoMessages';
import OCRConfirmationModal from '@/components/Modals/OCRConfirmationModal';

interface PolicyProps {
  policiesArray: VehicleResponse['policiesArray'];
  vehicleId: string;
  data: VehicleResponse;
}

interface BodyProps {
  insurer?: string;
  documentName?: string;
  validity?: string;
  policyNumber?: string | number;
  broker?: string;
  isEditing?: boolean;
  vehicleId?: string;
}

function Body({ documentName, isEditing = false, vehicleId }: BodyProps) {
  const [nameFile, setNameFile] = useState('');
  const { user } = useCurrentUser();
  const toast = useToast();
  const { setFieldValue } = useFormikContext();
  const [isProcessingOCR, setIsProcessingOCR] = useState(false);
  const [hasSuccessfulExtraction, setHasSuccessfulExtraction] = useState(false);

  // OCR Confirmation Modal state
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [pendingOCRResult, setPendingOCRResult] = useState<any>(null);
  const [pendingFile, setPendingFile] = useState<File | null>(null);

  // Get info message using centralized utility
  const infoMessage = getDocumentInfoMessage(DocumentCategory.INSURANCE_POLICY, false);

  const handlePolicyDocumentUpload = async (fileList: FileList | null) => {
    if (!fileList || !fileList[0] || !user?.accessToken) return;

    const file = fileList[0];
    setIsProcessingOCR(true);

    try {
      const result = await extractDataFromDocument(
        {
          document: file,
          documentType: 'INSURANCE_POLICY',
          vehicleId: vehicleId,
        },
        user.accessToken
      );

      if (result.success) {
        // Process extraction (valid document)
        processExtractedData(result.data);
        showOCRToast(toast, result);
      } else {
        // Show confirmation modal for errors instead of hard rejection
        setPendingOCRResult(result);
        setPendingFile(file);
        setIsConfirmationModalOpen(true);
        setIsProcessingOCR(false);
        return;
      }
    } catch (error) {
      console.error('OCR extraction failed:', error);

      // Show generic error for processing failures
      toast({
        title: 'Error al procesar documento',
        description: 'No se pudo procesar el documento. Por favor, intente con otro archivo.',
        status: 'error',
        duration: 5000,
      });

      // Clear the file from form and display on processing error
      setFieldValue('policyDocument', null);
      setNameFile('');
      setHasSuccessfulExtraction(false);
    } finally {
      setIsProcessingOCR(false);
    }
  };

  // Handle confirmation modal actions
  const handleConfirmWithErrors = () => {
    if (pendingOCRResult && pendingFile) {
      // Process the document despite errors
      processExtractedData(pendingOCRResult.data);
      showOCRToast(toast, pendingOCRResult, true); // Show warning toast for accepted invalid document

      // Set the file in the form
      setFieldValue('policyDocument', pendingFile);
      setNameFile(pendingFile.name);

      // Reset modal state
      setIsConfirmationModalOpen(false);
      setPendingOCRResult(null);
      setPendingFile(null);
    }
  };

  const handleCancelWithErrors = () => {
    // Clear everything and reset modal state
    setFieldValue('policyDocument', null);
    setNameFile('');
    setHasSuccessfulExtraction(false);
    setIsConfirmationModalOpen(false);
    setPendingOCRResult(null);
    setPendingFile(null);
  };

  // Helper function to process extracted data
  const processExtractedData = (data: any) => {
    if (data) {
      setHasSuccessfulExtraction(true);

      // Auto-fill the extracted fields
      if (data.policyNumber) {
        setFieldValue('policyNumber', data.policyNumber);
      }
      if (data.insurer) {
        setFieldValue('insurer', data.insurer);
      }
      if (data.validity) {
        setFieldValue('validity', data.validity);
      }
      if (data.broker) {
        setFieldValue('broker', data.broker);
      }
    }
  };

  return (
    <div className="flex flex-col w-full gap-3 overflow-hidden">
      {/* Info message for required fields */}
      <Alert
        status="info"
        borderRadius="md"
        variant="subtle"
        bg="#F4EEFF"
        color="#5800F7"
        sx={{
          '& .chakra-alert__icon': {
            color: '#5800F7',
          },
        }}
      >
        <AlertIcon />
        <Text fontWeight="400" color="#5800F7">
          {infoMessage}
        </Text>
      </Alert>
      <InputFile
        label="Subir archivo"
        name="policyDocument"
        buttonText={isProcessingOCR ? 'procesando...' : 'Subir archivo'}
        nameFile={documentName && nameFile === '' ? documentName : nameFile}
        handleSingleSetName={setNameFile}
        placeholder="Archivo .pdf no mayor a 2 MB"
        accept="pdf"
        onChange={handlePolicyDocumentUpload}
        isLoading={isProcessingOCR}
        loadingText="procesando..."
      />

      {/* Show existing fields in edit mode or OCR extracted fields after successful extraction */}
      {(isEditing || hasSuccessfulExtraction) && (
        <>
          <CustomInput label="No. de póliza" name="policyNumber" type="text" />
          <CustomInput label="Aseguradora" name="insurer" type="text" />
          <CustomDatePicker label="Vigencia" name="validity" limitPastDates={true} />
          <CustomInput label="Broker" name="broker" type="text" />
        </>
      )}

      {/* OCR Confirmation Modal */}
      <OCRConfirmationModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        onConfirm={handleConfirmWithErrors}
        onCancel={handleCancelWithErrors}
        ocrResult={pendingOCRResult}
        documentType="INSURANCE_POLICY"
      />
    </div>
  );
}

export default function Policy({ data }: PolicyProps) {
  const { data: session } = useSession();
  const isCarBlocked = data.status === 'bloqueo';
  const user = session?.user as unknown as MyUser;
  const [isLoading, setIsLoading] = useState(false);

  const initialValues = useMemo(() => {
    return { policyNumber: '', insurer: '', validity: '', broker: '', policyDocument: '' };
  }, []);

  const lastPolicy = data.policiesArray ? data.policiesArray[data.policiesArray.length - 1] : null;

  const isOneWeek = isNearOneWeek(lastPolicy?.validity);

  const url = useCurrentUrl();

  const onSubmit = useCallback(
    async (values: FormikValues) => {
      setIsLoading(true);
      const form = {
        ...values,
        historyData: {
          userId: user.id,
          step: 'DOCUMENTACIÓN',
          description: 'Agregó póliza', // Esto se actualizará para mostrar que cambio exacto se hizo
        },
      };
      const response = await axios
        .patch(`${url}/stock/update/policy/${data._id}`, form, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `bearer ${session ? user.accessToken : null}`,
          },
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => setIsLoading(false));
      return response;
    },
    [data._id, url, session, user?.accessToken, user?.id]
  );

  const ability = usePermissions();
  const canAddPolicy = canPerform(ability, Capabilities.AddPolicy, Sections.Fleet, Subsections.General);

  const OpenCreateModal = useCallback(() => {
    return isCarBlocked ? (
      <div>
        <p className="text-[#9CA3AF]">Este vehículo está bloqueado</p>
      </div>
    ) : (
      <CustomModal
        testId="policy"
        openButtonText="Agregar póliza"
        confirmButtonText="Agregar"
        isPrimaryButton={true}
        validatorSchema={addPolicySchema}
        body={<Body vehicleId={data._id} />}
        initialValues={initialValues}
        onCloseModal={() => {}}
        header="Agregar póliza"
        onSubmit={onSubmit}
        canEdit={canAddPolicy}
      />
    );
  }, [onSubmit, initialValues, isCarBlocked, data._id, canAddPolicy]);

  if (isLoading) return <Spinner />;

  return (
    <div
      className={`
        min-h-[251px]
        bg-[white]
        flex flex-col
        gap-3
        py-[25px]
        pl-[20px]
        pr-[15px]
        border-[1px]
        border-[#EAECEE]
        font-bold
        rounded
        ${data.policiesArray && data.policiesArray.length > 2 ? 'overflow-x' : ''}
        `}
    >
      {data.policiesArray && data.policiesArray.length > 0 ? (
        <>
          <div className="flex justify-between mb-[15px]">
            <p className="font-bold text-[24px]">Pólizas de seguro</p>
            <div className="flex gap-2">
              <DeleteVehicleDocs documentToDelete="policiesArray" vehicle={data} />
              {isOneWeek && canAddPolicy && OpenCreateModal()}
            </div>
          </div>
          <div className="flex gap-4">
            {data.policiesArray.map((policy, i) => (
              // eslint-disable-next-line @typescript-eslint/no-use-before-define
              <PolicyCard
                key={i}
                policy={policy}
                index={i + 1}
                currentUser={user}
                vehicleId={data._id}
                isLatest={i === data.policiesArray.length - 1}
                allPolicies={data.policiesArray}
              />
            ))}
          </div>
        </>
      ) : (
        <>
          <div className="flex justify-between items-center">
            <p className="font-bold text-[24px] mb-[15px]">Pólizas de seguro </p>
            <RequiredBadge />
          </div>
          <div className="w-full flex flex-col gap-3 justify-center items-center mt-[30px]">
            <p className="text-[#9CA3AF]">Este vehículo aún no tiene ninguna póliza de seguro</p>
            {canAddPolicy && OpenCreateModal()}
          </div>
        </>
      )}
    </div>
  );
}

interface PolicyCardProps {
  policy: VehicleResponse['policiesArray'][number];
  vehicleId: string;
  currentUser?: MyUser;
  index: number;
  isLatest: boolean;
  allPolicies?: VehicleResponse['policiesArray'];
}

const years: { [key: string]: string } = {
  '1': '1er',
  '2': '2do',
  '3': '3er',
  '4': '4to',
};

function PolicyCard({ policy, vehicleId, currentUser, index, isLatest, allPolicies }: PolicyCardProps) {
  const initiValues = {
    policyNumber: policy.policyNumber || '',
    insurer: policy.insurer || '',
    validity: policy.validity || '',
    broker: policy.broker || '',
    policyDocument: '',
    isEditing: true,
  };
  const url = useCurrentUrl();

  const isExpired = new Date(policy.validity) < new Date();
  const hasMultiplePolicies = isLatest && allPolicies && allPolicies.length > 1;

  const onSubmit = async (values: FormikValues) => {
    const form = {
      ...values,
      updatePolicyNumber: values.policyNumber ? Number(values.policyNumber) : null,
      updateValidity: values.validity,
      policyDocId: values.policyDocument ? policy.policyDocument.docId : null,
      updateBroker: values.broker,
      policyId: policy._id,
      historyData: {
        userId: currentUser && currentUser.id,
        step: 'DOCUMENTACIÓN',
        description: 'Actualización de póliza', // Esto se actualizará para mostrar que cambio exacto se hizo
      },
      isEditing: true,
    };
    const response = await axios.patch(`${url}/stock/update/policy/${vehicleId}`, form, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `bearer ${currentUser ? currentUser.accessToken : null}`,
      },
    });
    return response;
  };

  const ability = usePermissions();
  const canEditPolicy = canPerform(ability, Capabilities.EditPolicy, Sections.Fleet, Subsections.General);

  return (
    <div
      className="
      w-[302px]
      flex
      flex-col
      gap-2
      p-[20px]
      border-[1px]
      border-[#5CAFFC]
      rounded
      overflow-hidden
      relative
      "
    >
      <div className="absolute top-2 right-2 flex gap-2">
        {isExpired && <ExpiredBadge />}
        {hasMultiplePolicies && <LatestBadge />}
      </div>
      <div
        className={`flex justify-between text-[#5CAFFC] items-center ${
          isExpired || hasMultiplePolicies ? 'mt-[18px]' : ''
        }`}
      >
        <div className="flex items-start">
          <div
            className="
              w-[3px]
              h-[20px]
              rounded
              border-[2px] border-[#5CAFFC] bg-[#5CAFFC]
              mr-[6px]

            "
          />
          <p>{years[index]} año</p>
        </div>
        <p>
          {canEditPolicy && (
            <CustomModal
              testId="policy"
              openButtonText="Editar póliza"
              confirmButtonText={editTextBtn}
              isPrimaryButton={true}
              validatorSchema={addPolicySchema}
              body={
                <Body
                  insurer={policy.insurer}
                  documentName={policy.policyDocument.originalName}
                  validity={policy.validity}
                  policyNumber={policy.policyNumber}
                  broker={policy.broker}
                  isEditing={true}
                  vehicleId={vehicleId}
                />
              }
              initialValues={initiValues}
              onCloseModal={() => {}}
              header="Editar póliza"
              isUpdate
              updateIconColor="#5CAFFC"
              onSubmit={onSubmit}
            />
          )}
        </p>
      </div>
      <p className="my-[3px]">No. {policy.policyNumber}</p>
      <div className="flex justify-between ">
        <p className="font-normal">Aseguradora</p>
        <p className="text-[#5CAFFC]">{policy.insurer}</p>
      </div>
      <div className="flex justify-between ">
        <p className="font-normal">Vigencia</p>
        <p>{policy.validity}</p>
      </div>
      <div className="flex justify-between ">
        <p className="font-normal">Broker</p>
        <p>{policy.broker}</p>
      </div>
      <DocumentDisplay
        url={policy.policyDocument.url}
        docName={policy.policyDocument.originalName}
        backgroundColor="rgba(92, 175, 252, 0.2)"
        textColor="#5CAFFC"
      />
    </div>
  );
}
