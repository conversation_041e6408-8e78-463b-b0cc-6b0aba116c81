/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-use-before-define */
'use client';
import { MyUser } from '@/actions/getCurrentUser';
import { VehicleResponse } from '@/actions/getVehicleData';
import CustomDatePicker from '@/components/Inputs/CustomDatePicker';
import InputFile from '@/components/Inputs/InputFile';
import CustomInput from '@/components/Inputs/CustomInput';
import CustomModal from '@/components/Modals/CustomModal';
import { addTenancy } from '@/validatorSchemas/vehicleDetailSchema';
import axios from 'axios';
import { FormikValues, useFormikContext } from 'formik';
import { useSession } from 'next-auth/react';
import { useCallback, useMemo, useState } from 'react';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import DocumentDisplay from '@/components/DocumentDisplay';
import Spinner from '@/components/Loading/Spinner';
import isNearOneWeek from '@/utils/isNearOneWeek';
import { Capabilities, editTextBtn, Sections, Subsections } from '@/constants';
import DeleteVehicleDocs from '../Buttons/DeleteVehicleDocs';
import RequiredBadge from '../others/RequiredBadge';
import ExpiredBadge from '../others/ExpiredBadge';
import LatestBadge from '../others/LatestBadge';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { extractDataFromDocument, showOCRToast } from '@/utils/ocrApi';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useToast, Alert, AlertIcon, Text } from '@chakra-ui/react';
import { DocumentCategory } from '@/constants';
import { getDocumentInfoMessage } from '@/utils/documentInfoMessages';
import OCRConfirmationModal from '@/components/Modals/OCRConfirmationModal';

interface TenancyProps {
  tenancies: VehicleResponse['tenancy'];
  vehicle: VehicleResponse;
  isCarBlocked: boolean;
}

export default function Tenancy({ tenancies, vehicle, isCarBlocked }: TenancyProps) {
  const [isLoading, setIsLoading] = useState(false);
  const initialValues = useMemo(() => {
    return { payment: '', tenancyDocument: '', validity: '' };
  }, []);

  const { data: session } = useSession();

  const lastTenancy = tenancies ? tenancies[tenancies.length - 1] : null;

  const isOneWeek = isNearOneWeek(lastTenancy?.validity);

  const user = session?.user as unknown as MyUser;
  const url = useCurrentUrl();

  const onSubmit = useCallback(
    async (values: FormikValues) => {
      setIsLoading(true);
      const form = {
        ...values,
        historyData: {
          userId: user.id,
          step: 'DOCUMENTACIÓN',
          description: 'Agregó tenencia', // Esto se actualizará para mostrar qué cambio exacto se hizo
        },
      };
      const response = await axios
        .patch(`${url}/stock/update/tenancy/${vehicle._id}`, form, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `bearer ${session ? user.accessToken : null}`,
          },
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => setIsLoading(false));
      return response;
    },
    [user?.id, url, vehicle._id, session, user?.accessToken]
  );

  const ability = usePermissions();
  const canAddHolding = canPerform(ability, Capabilities.AddHolding, Sections.Fleet, Subsections.General);

  const OpenCreateModal = useCallback(() => {
    return isCarBlocked ? (
      <div>
        <p className="text-[#9CA3AF]">Este vehículo está bloqueado</p>
      </div>
    ) : (
      <CustomModal
        testId="tenancy"
        openButtonText="Agregar tenencia"
        confirmButtonText="Agregar"
        isPrimaryButton={true}
        validatorSchema={addTenancy}
        body={<Body vehicleId={vehicle._id} />}
        initialValues={initialValues}
        onCloseModal={() => {}}
        header="Agregar tenencia"
        onSubmit={onSubmit}
      />
    );
  }, [onSubmit, initialValues]);

  if (isLoading) return <Spinner />;
  return (
    <div
      className={`
        w-auto
        bg-[white]
        flex flex-col
        gap-3
        py-[25px]
        pl-[20px]
        pr-[15px]
        border-[1px]
        border-[#EAECEE]
        font-bold
        rounded
        ${tenancies && tenancies.length > 2 ? 'overflow-x' : ''}
        `}
    >
      {tenancies && tenancies.length > 0 ? (
        <>
          <div className="flex justify-between mb-[15px]">
            <p className="font-bold text-[24px]">Tenencias</p>
            <div className="flex gap-2">
              <DeleteVehicleDocs documentToDelete="tenancy" vehicle={vehicle} />
              {isOneWeek && canAddHolding && OpenCreateModal()}
            </div>
          </div>
          <div className="flex gap-4">
            {tenancies.map((t, i) => (
              // eslint-disable-next-line @typescript-eslint/no-use-before-define
              <TenancyCard
                key={i}
                tenancy={t}
                index={i + 1}
                vehicleId={vehicle._id}
                currentUser={user}
                isLatest={i === tenancies.length - 1}
                allTenancies={tenancies}
              />
            ))}
          </div>
        </>
      ) : (
        <>
          <div className="flex justify-between items-center">
            <p className="font-bold text-[24px]">Tenencias </p>
            <RequiredBadge />
          </div>
          <div className="w-full flex flex-col gap-3 justify-center items-center mt-[30px]">
            <p className="text-[#9CA3AF]">Este vehículo aún no tiene ninguna tenencia asociada</p>
            {canAddHolding && OpenCreateModal()}
          </div>
        </>
      )}
    </div>
  );
}

interface BodyProps {
  documentName?: string;
  validity?: string;
  payment?: string | number;
  isEditing?: boolean;
  vehicleId?: string;
}

function Body({ documentName, vehicleId }: BodyProps) {
  const [nameFile, setNameFile] = useState('');
  const { user } = useCurrentUser();
  const toast = useToast();
  const { setFieldValue } = useFormikContext();
  const [isProcessingOCR, setIsProcessingOCR] = useState(false);
  const [hasSuccessfulExtraction, setHasSuccessfulExtraction] = useState(false);

  // OCR Confirmation Modal state
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [pendingOCRResult, setPendingOCRResult] = useState<any>(null);
  const [pendingFile, setPendingFile] = useState<File | null>(null);

  // Get info message using centralized utility
  const infoMessage = getDocumentInfoMessage(DocumentCategory.TENENCIA, false);

  const handleSetName = (name: string, value: string) => {
    setNameFile(value);
  };

  const handleTenancyDocumentUpload = async (fileList: FileList | null) => {
    if (!fileList || !fileList[0] || !user?.accessToken) return;

    const file = fileList[0];
    setIsProcessingOCR(true);

    try {
      const result = await extractDataFromDocument(
        {
          document: file,
          documentType: 'TENENCIA',
          vehicleId: vehicleId,
        },
        user.accessToken
      );

      if (result.success) {
        // Process extraction (valid document)
        processExtractedData(result.data);
        showOCRToast(toast, result);
      } else {
        // Show confirmation modal for errors instead of hard rejection
        setPendingOCRResult(result);
        setPendingFile(file);
        setIsConfirmationModalOpen(true);
        setIsProcessingOCR(false);
        return;
      }
    } catch (error) {
      console.error('OCR extraction failed:', error);

      // Show generic error for processing failures
      toast({
        title: 'Error al procesar documento',
        description: 'No se pudo procesar el documento. Por favor, intente con otro archivo.',
        status: 'error',
        duration: 5000,
      });

      // Clear the file from form and display on processing error
      setFieldValue('tenancyDocument', null);
      setNameFile('');
      setHasSuccessfulExtraction(false);
    } finally {
      setIsProcessingOCR(false);
    }
  };

  // Handle confirmation modal actions
  const handleConfirmWithErrors = () => {
    if (pendingOCRResult && pendingFile) {
      // Process the document despite errors
      processExtractedData(pendingOCRResult.data);
      showOCRToast(toast, pendingOCRResult, true); // Show warning toast for accepted invalid document

      // Set the file in the form
      setFieldValue('tenancyDocument', pendingFile);
      setNameFile(pendingFile.name);

      // Reset modal state
      setIsConfirmationModalOpen(false);
      setPendingOCRResult(null);
      setPendingFile(null);
    }
  };

  const handleCancelWithErrors = () => {
    // Clear everything and reset modal state
    setFieldValue('tenancyDocument', null);
    setNameFile('');
    setHasSuccessfulExtraction(false);
    setIsConfirmationModalOpen(false);
    setPendingOCRResult(null);
    setPendingFile(null);
  };

  // Helper function to process extracted data
  const processExtractedData = (data: any) => {
    if (data) {
      setHasSuccessfulExtraction(true);

      // Auto-fill the extracted fields
      if (data.payment) {
        setFieldValue('payment', data.payment);
      }
      if (data.validity) {
        setFieldValue('validity', data.validity);
      }
    }
  };

  return (
    <div className="flex flex-col w-full gap-3 overflow-hidden">
      {/* Info message about required fields */}
      <Alert
        status="info"
        borderRadius="md"
        bg="#F4EEFF"
        color="#5800F7"
        sx={{
          '& .chakra-alert__icon': {
            color: '#5800F7',
          },
        }}
      >
        <AlertIcon />
        <Text fontWeight="400" color="#5800F7">
          {infoMessage}
        </Text>
      </Alert>

      <InputFile
        label="Tenencia PDF"
        name="tenancyDocument"
        nameFile={documentName && nameFile === '' ? documentName : nameFile}
        handleSetName={handleSetName}
        accept="pdf"
        placeholder="Archivo .pdf no mayor a 2 MB"
        buttonText={isProcessingOCR ? 'procesando...' : 'Subir archivo'}
        onChange={handleTenancyDocumentUpload}
        isLoading={isProcessingOCR}
        loadingText="procesando..."
      />

      {/* Show fields only in edit mode or after successful OCR extraction */}
      {(documentName || hasSuccessfulExtraction) && (
        <>
          <CustomInput label="Monto total de la tenencia" name="payment" type="text" />
          <CustomDatePicker label="Vigencia" name="validity" limitPastDates={true} />
        </>
      )}

      {/* OCR Confirmation Modal */}
      <OCRConfirmationModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        onConfirm={handleConfirmWithErrors}
        onCancel={handleCancelWithErrors}
        ocrResult={pendingOCRResult}
        documentType="TENENCIA"
      />
    </div>
  );
}

interface TenancyCardProps {
  tenancy: VehicleResponse['tenancy'][number];
  vehicleId: string;
  currentUser?: MyUser;
  index: number;
  isLatest: boolean;
  allTenancies?: VehicleResponse['tenancy'];
}

const years: { [key: string]: string } = {
  '1': '1er',
  '2': '2do',
  '3': '3er',
  '4': '4to',
};

function TenancyCard({ tenancy, vehicleId, currentUser, index, isLatest, allTenancies }: TenancyCardProps) {
  const initValues = {
    payment: tenancy.payment || '',
    validity: tenancy.validity || '',
    tenancyDocument: '',
    isEditing: true,
  };

  const url = useCurrentUrl();

  const isExpired = new Date(tenancy.validity) < new Date();
  const hasMultipleTenancies = isLatest && allTenancies && allTenancies.length > 1;

  const onSubmit = async (values: FormikValues) => {
    const form = {
      ...values,
      updatePayment: values.payment ? Number(values.payment) : null,
      updateValidity: values.validity,
      tenancyDocId: values.tenancyDocument ? tenancy.tenancyDocument.docId : null,
      tenancyId: tenancy._id,
      historyData: {
        userId: currentUser && currentUser.id,
        step: 'DOCUMENTACIÓN',
        description: 'Actualización de tenencia', // Esto se actualizará para mostrar que cambio exacto se hizo
      },
      isEditing: true,
    };
    const response = await axios.patch(`${url}/stock/update/tenancy/${vehicleId}`, form, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `bearer ${currentUser ? currentUser.accessToken : null}`,
      },
    });

    return response;
  };

  const ability = usePermissions();
  const canEditHolding = canPerform(ability, Capabilities.EditHolding, Sections.Fleet, Subsections.General);

  return (
    <div
      className="
      w-[302px]
      min-w-[302px]
      flex
      flex-col
      gap-2
      p-[20px]
      border-[1px]
      border-[#CC6FF8]
      rounded
      relative
      "
    >
      <div className="absolute top-2 right-2 flex gap-2">
        {isExpired && <ExpiredBadge />}
        {hasMultipleTenancies && <LatestBadge />}
      </div>
      <div
        className={`flex justify-between text-[#CC6FF8] items-center ${
          isExpired || hasMultipleTenancies ? 'mt-[18px]' : ''
        }`}
      >
        <div className="flex items-start">
          <div
            className="
              w-[3px]
              h-[20px]
              rounded
              border-[2px]
              border-[#CC6FF8]
              bg-[#CC6FF8]
              mr-[6px]

            "
          />
          <p>{years[index]} año</p>
        </div>
        <p>
          {canEditHolding && (
            <CustomModal
              testId="tenancy"
              openButtonText="Editar tenencia"
              confirmButtonText={editTextBtn}
              isPrimaryButton={true}
              body={
                <Body
                  documentName={tenancy.tenancyDocument.originalName}
                  validity={tenancy.validity}
                  payment={tenancy.payment}
                  isEditing={true}
                  vehicleId={vehicleId}
                />
              }
              initialValues={initValues}
              validatorSchema={addTenancy}
              onCloseModal={() => {}}
              header="Editar tenencia"
              isUpdate
              updateIconColor="#CC6FF8"
              onSubmit={onSubmit}
            />
          )}
        </p>
      </div>
      <p className="my-[3px]">$ {tenancy.payment} MXN</p>
      <div className="flex justify-between ">
        <p className="font-normal">Vigencia</p>
        <p>{tenancy.validity}</p>
      </div>
      <DocumentDisplay
        url={tenancy.tenancyDocument.url}
        docName={tenancy.tenancyDocument.originalName}
        backgroundColor="rgba(204, 111, 248, 0.2)"
        textColor="#CC6FF8"
      />
    </div>
  );
}
