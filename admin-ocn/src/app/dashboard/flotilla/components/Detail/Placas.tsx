/* eslint-disable @typescript-eslint/no-use-before-define */
'use client';
import CustomInput from '@/components/Inputs/CustomInput';
import FileInput from '@/components/Inputs/InputFile';
import CustomModal from '@/components/Modals/CustomModal';
import { addPlacasSchema, addPlacasSchemaNotAdmin } from '@/validatorSchemas/vehicleDetailSchema';
import { useState } from 'react';
import { VehicleResponse } from '@/actions/getVehicleData';
// import { MdPictureAsPdf } from 'react-icons/md';
import { FormikValues, useFormikContext } from 'formik';
import axios, { AxiosResponse } from 'axios';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import Spinner from '@/components/Loading/Spinner';
import ZoomImage from '../others/ZoomImage';
import { URL_API, editTextBtn, DocumentCategory, Capabilities, Sections, Subsections } from '@/constants';
import { useToast, Alert, AlertIcon, Text } from '@chakra-ui/react';
import DocumentDisplay from '@/components/DocumentDisplay';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import DeleteVehicleDocs from '../Buttons/DeleteVehicleDocs';
import RequiredBadge from '../others/RequiredBadge';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { extractDataFromDocument, showOCRToast } from '@/utils/ocrApi';
import { getBaseDocumentQualityMessage, getDocumentRequiredFieldsOnly } from '@/utils/documentInfoMessages';
import OCRConfirmationModal from '@/components/Modals/OCRConfirmationModal';

interface PlacasProps {
  data: VehicleResponse;
}

export default function Placas({ data }: PlacasProps) {
  // const [nameFile, setNameFile] = useState('');
  const [nameFiles, setNameFiles] = useState({
    frontImg: '',
    backImg: '',
    platesDocument: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const handleSetName = (name: string, value: string) => {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  };

  const ability = usePermissions();
  const canAddPlate = canPerform(ability, Capabilities.AddPlate, Sections.Fleet, Subsections.General);
  const canEditPlate = canPerform(ability, Capabilities.EditPlate, Sections.Fleet, Subsections.General);

  const initialValues = { plates: '', frontImg: '', backImg: '', platesDocument: '' };
  const onClose = () => {
    setNameFiles({
      frontImg: '',
      backImg: '',
      platesDocument: '',
    });
  };

  const backendValues = {
    plates: data.carPlates?.plates,
    frontImg: '',
    backImg: '',
    platesDocument: '',
  };

  const { user } = useCurrentUser();

  const config = function (formData?: boolean) {
    let headers: { [key: string]: any } = {
      headers: {
        Authorization: `bearer ${user.accessToken}`,
      },
    };
    if (formData) headers.headers['Content-Type'] = 'multipart/form-data';
    return headers;
  };

  const url = useCurrentUrl();
  const toast = useToast();

  const validatePreSubmit = async (values: FormikValues) => {
    try {
      await axios.post(
        `${URL_API}/stock/validate-fields`,
        {
          plates: values.plates,
          vehicleId: data._id,
        },
        config()
      );
      return true;
    } catch (error: any) {
      console.log(error);
      toast({
        title: 'Estas placas ya estan registradas en el vehiculo ' + error.response.data.carNumber,
        duration: 3000,
        status: 'error',
      });
      return false;
    }
  };
  const onSubmit = async (values: FormikValues, isEditing?: boolean) => {
    setIsLoading(true);
    const formData = {
      ...values,
      historyData: {
        userId: user._id,
        step: 'DOCUMENTACIÓN',
        description: isEditing ? 'Placas editadas' : 'Placas agregadas',
      },
      // eslint-disable-next-line no-unneeded-ternary
      isEditing: isEditing ? true : false,
    };

    const response = await axios
      .patch(`${url}/stock/update/carPlates/${data._id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `bearer ${user.accessToken}`,
        },
      })
      .then((res) => {
        toast({
          title: res.data.message,
          description: 'Actualizando pagina...',
          duration: 3000,
          status: 'success',
        });
        setTimeout(() => {
          window.location.reload();
        }, 3050);
      })
      .catch((err) => {
        toast({
          title: err.response.data.message,
          duration: 3000,
          status: 'error',
        });
      })
      .finally(() => setIsLoading(false));
    setIsLoading(false);
    return response;
  };
  if (isLoading) return <Spinner />;

  return (
    <div
      className="
        w-[316px]
        bg-[white]
        flex flex-col
        gap-3
        p-[25px]
        border-[1px]
        border-[#EAECEE]
        font-bold
        rounded"
    >
      {data.carPlates ? (
        <>
          <div className="flex justify-between">
            <p className="font-bold text-[24px] ">Placas</p>
            <div className="flex gap-2">
              <DeleteVehicleDocs documentToDelete="carPlates" vehicle={data} />
              {canEditPlate && (
                <CustomModal
                  testId="carPlates"
                  openButtonText="Editar Placas"
                  confirmButtonText={editTextBtn}
                  isPrimaryButton={true}
                  body={
                    <Body
                      carPlates={data.carPlates}
                      handleSetName={handleSetName}
                      nameFiles={nameFiles}
                      isEdit
                      vehicleId={data._id}
                    />
                  }
                  initialValues={backendValues}
                  onCloseModal={onClose}
                  header="Editar Placas"
                  isUpdate
                  updateIconColor="#5800F7"
                  customSubmit
                  onSubmit={async (values, _, onCloseModal) => {
                    const isValid = await validatePreSubmit(values);

                    if (isValid) {
                      const res = (await onSubmit(values, true)) as unknown as AxiosResponse<any, any>;

                      toast({
                        title: res.data.message,
                        description: 'Actualizando pagina...',
                        duration: 3000,
                        status: 'success',
                      });

                      onCloseModal();
                    }
                  }}
                />
              )}
            </div>
          </div>

          <div className="grid gap-1 overflow-hidden ">
            <p className="font-bold text-[18px] ">{data.carPlates.plates}</p>
            <div className="flex flex-col gap-2 overflow-hidden">
              <div className="flex gap-3">
                {data.carPlates.frontImg ? (
                  <ZoomImage
                    imageUrl={data.carPlates.frontImg.url}
                    name={data.carPlates.frontImg.originalName}
                  />
                ) : (
                  <div className="font-normal text-[14px]">Placa frontal faltante</div>
                )}
                {data.carPlates.backImg ? (
                  <ZoomImage
                    imageUrl={data.carPlates.backImg.url}
                    name={data.carPlates.backImg.originalName}
                  />
                ) : (
                  <div className="font-normal text-[14px]">Placa frontal faltante</div>
                )}
              </div>
              {
                <div className="flex gap-3">
                  {data.carPlates.platesDocument ? (
                    <DocumentDisplay
                      docName={data.carPlates.platesDocument.originalName}
                      url={data.carPlates.platesDocument.url}
                    />
                  ) : (
                    <div className="font-normal text-[14px]">Documento faltante</div>
                  )}
                </div>
              }
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="flex justify-between items-center">
            <p className="font-bold text-[24px] ">Placas </p>
            <RequiredBadge />
          </div>
          <p className="text-[#9CA3AF]">Este vehículo aún no tiene placas asignadas</p>
          {canAddPlate && (
            <CustomModal
              testId="carPlates"
              openButtonText="Agregar Placas"
              confirmButtonText="Agregar"
              isPrimaryButton={true}
              validatorSchema={user.role === 'superadmin' ? addPlacasSchema : addPlacasSchemaNotAdmin}
              body={<Body handleSetName={handleSetName} nameFiles={nameFiles} vehicleId={data._id} />}
              initialValues={initialValues}
              onCloseModal={onClose}
              header="Agregar Placas"
              customSubmit
              onSubmit={async (values, _, onCloseModal) => {
                const isValid = await validatePreSubmit(values);

                if (isValid) {
                  const res = (await onSubmit(values, false)) as unknown as AxiosResponse<any, any>;

                  toast({
                    title: res.data.message,
                    description: 'Actualizando pagina...',
                    duration: 3000,
                    status: 'success',
                  });

                  onCloseModal();
                }
              }}
            />
          )}
        </>
      )}
    </div>
  );
}

interface BodyParams {
  carPlates?: VehicleResponse['carPlates'] | null;
  nameFiles: {
    frontImg: string;
    backImg: string;
    platesDocument: string;
  };
  handleSetName: (name: string, value: string) => void;
  isEdit?: boolean;
  vehicleId?: string;
}

function Body({ carPlates, nameFiles, handleSetName, isEdit, vehicleId }: BodyParams) {
  const { user } = useCurrentUser();
  const toast = useToast();
  const [isProcessingOCR, setIsProcessingOCR] = useState({
    platesDocument: false,
    frontImg: false,
    backImg: false,
  });
  const { setFieldValue, values } = useFormikContext();
  const [hasSuccessfulExtraction, setHasSuccessfulExtraction] = useState(false);

  // OCR Confirmation Modal state
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [pendingOCRResult, setPendingOCRResult] = useState<any>(null);
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  const [pendingFieldName, setPendingFieldName] = useState<string>('');

  // Get base message and specific field messages using centralized utility
  const baseQualityMessage = getBaseDocumentQualityMessage(false);
  const platesDocumentFields = getDocumentRequiredFieldsOnly(DocumentCategory.PLATES_ALTA_PLACAS, false);
  const platesFrontFields = getDocumentRequiredFieldsOnly(DocumentCategory.PLATES_FRONT, false);
  const platesBackFields = getDocumentRequiredFieldsOnly(DocumentCategory.PLATES_BACK, false);

  const handlePlatesDocumentUpload = async (fileList: FileList | null) => {
    if (!fileList || !fileList[0] || !user?.accessToken) return;

    const file = fileList[0];
    setIsProcessingOCR((prev) => ({ ...prev, platesDocument: true }));

    try {
      const result = await extractDataFromDocument(
        {
          document: file,
          documentType: 'PLATES_ALTA_PLACAS',
          vehicleId: vehicleId,
        },
        user.accessToken
      );

      if (result.success) {
        // Process valid document - extract plates data
        if (result.data?.plates) {
          const extractedPlates = result.data.plates;

          // Auto-fill the plates field if OCR extracted it successfully
          setFieldValue('plates', extractedPlates);
          setHasSuccessfulExtraction(true);

          toast({
            title: 'Número de placas extraído',
            description: `Placas "${extractedPlates}" extraídas. Ahora puede subir las imágenes de las placas.`,
            status: 'info',
            duration: 3000,
          });
        }

        // Show appropriate toast message
        showOCRToast(toast, result);
      } else {
        // Show confirmation modal for errors instead of hard rejection
        setPendingOCRResult(result);
        setPendingFile(file);
        setPendingFieldName('platesDocument');
        setIsConfirmationModalOpen(true);
        setIsProcessingOCR((prev) => ({ ...prev, platesDocument: false }));
        return;
      }
    } catch (error) {
      console.error('OCR processing error:', error);

      // Show generic error for processing failures
      toast({
        title: 'Error al procesar documento',
        description: 'No se pudo procesar el documento. Por favor, intente con otro archivo.',
        status: 'error',
        duration: 3000,
      });

      // Clear the file from form and display on processing error
      setFieldValue('platesDocument', null);
      handleSetName('platesDocument', '');
      setHasSuccessfulExtraction(false);
    } finally {
      setIsProcessingOCR((prev) => ({ ...prev, platesDocument: false }));
    }
  };

  const handlePlateImageUpload = async (fileList: FileList | null, fieldName: 'frontImg' | 'backImg') => {
    if (!fileList || !fileList[0] || !user?.accessToken) return;

    // Check if plates number is filled before allowing image upload
    const currentPlatesValue = (values as any).plates;
    if (!currentPlatesValue || currentPlatesValue.trim() === '') {
      toast({
        title: 'Se requiere número de placas',
        description:
          'Por favor complete el número de placas antes de subir imágenes. Puede extraerlo del documento de placas primero.',
        status: 'warning',
        duration: 3000,
      });

      // Clear the file input
      setFieldValue(fieldName, null);
      handleSetName(fieldName, '');
      return;
    }

    const file = fileList[0];
    setIsProcessingOCR((prev) => ({ ...prev, [fieldName]: true }));

    try {
      const documentType = fieldName === 'frontImg' ? 'PLATES_FRONT' : 'PLATES_BACK';

      const result = await extractDataFromDocument(
        {
          document: file,
          documentType: documentType,
          vehicleId: vehicleId,
          plates: currentPlatesValue,
        },
        user.accessToken
      );

      if (result.success) {
        // For plate images, OCR is mainly for backend validation
        // We don't need to auto-fill any fields, just show success/failure
        toast({
          title: 'Imagen de placa validada',
          description: `Imagen de placa ${
            fieldName === 'frontImg' ? 'frontal' : 'trasera'
          } procesada exitosamente.`,
          status: 'success',
          duration: 3000,
        });

        // Show appropriate toast message
        showOCRToast(toast, result);
      } else {
        // Show confirmation modal for errors instead of hard rejection
        setPendingOCRResult(result);
        setPendingFile(file);
        setPendingFieldName(fieldName);
        setIsConfirmationModalOpen(true);
        setIsProcessingOCR((prev) => ({ ...prev, [fieldName]: false }));
        return;
      }
    } catch (error) {
      console.error('OCR processing error:', error);

      // Show generic error for processing failures
      toast({
        title: 'Error al procesar imagen',
        description: 'No se pudo procesar la imagen de la placa para validación',
        status: 'error',
        duration: 3000,
      });

      // Clear the file from form and display on processing error
      setFieldValue(fieldName, null);
      handleSetName(fieldName, '');
    } finally {
      setIsProcessingOCR((prev) => ({ ...prev, [fieldName]: false }));
    }
  };

  // Handle confirmation modal actions
  const handleConfirmWithErrors = () => {
    if (pendingOCRResult && pendingFile && pendingFieldName) {
      // Process the document despite errors
      if (pendingFieldName === 'platesDocument' && pendingOCRResult.data?.plates) {
        const extractedPlates = pendingOCRResult.data.plates;
        setFieldValue('plates', extractedPlates);
        setHasSuccessfulExtraction(true);

        toast({
          title: 'Número de placas extraído',
          description: `Placas "${extractedPlates}" extraídas. Ahora puede subir las imágenes de las placas.`,
          status: 'info',
          duration: 3000,
        });
      }

      showOCRToast(toast, pendingOCRResult, true); // Show warning toast for accepted invalid document

      // Set the file in the form
      setFieldValue(pendingFieldName, pendingFile);
      handleSetName(pendingFieldName, pendingFile.name);

      // Reset modal state
      setIsConfirmationModalOpen(false);
      setPendingOCRResult(null);
      setPendingFile(null);
      setPendingFieldName('');
    }
  };

  const handleCancelWithErrors = () => {
    // Clear everything and reset modal state
    if (pendingFieldName) {
      setFieldValue(pendingFieldName, null);
      handleSetName(pendingFieldName, '');

      // Reset extraction state for document upload
      if (pendingFieldName === 'platesDocument') {
        setHasSuccessfulExtraction(false);
      }
    }

    setIsConfirmationModalOpen(false);
    setPendingOCRResult(null);
    setPendingFile(null);
    setPendingFieldName('');
  };

  return (
    <div className="flex flex-col gap-3 w-full overflow-hidden">
      {/* Base info message about document quality and visibility - shown once at the top */}
      <Alert
        status="info"
        borderRadius="md"
        bg="#F4EEFF"
        color="#5800F7"
        sx={{
          '& .chakra-alert__icon': {
            color: '#5800F7',
          },
        }}
      >
        <AlertIcon />
        <Text fontWeight="400" color="#5800F7">
          {baseQualityMessage}
        </Text>
      </Alert>

      {/* Show plates input field only in edit mode or after successful OCR extraction */}
      {(isEdit || hasSuccessfulExtraction) && <CustomInput label="No. de placas" name="plates" type="text" />}

      {/* Document upload section with specific field requirements */}
      <div className="border-t pt-3">
        <FileInput
          nameFile={
            isEdit && nameFiles.platesDocument === ''
              ? (carPlates?.platesDocument?.originalName as string)
              : nameFiles.platesDocument
          }
          name="platesDocument"
          label="Alta de placas PDF"
          handleSetName={handleSetName}
          accept="all"
          buttonText={
            isProcessingOCR.platesDocument
              ? 'procesando...'
              : isEdit && carPlates?.platesDocument
              ? 'Reemplazar archivo'
              : 'Subir archivo'
          }
          placeholder={'Archivo .pdf no mayor a 2 MB'}
          onChange={handlePlatesDocumentUpload}
          isLoading={isProcessingOCR.platesDocument}
          loadingText="procesando..."
          infoMessage={platesDocumentFields ? `Datos requeridos: ${platesDocumentFields}` : undefined}
        />
      </div>

      {/* Front plate image section */}
      <div className="border-t pt-3">
        <FileInput
          nameFile={
            isEdit && nameFiles.frontImg === ''
              ? (carPlates?.frontImg?.originalName as string)
              : nameFiles.frontImg
          }
          name="frontImg"
          label="Foto placa delantera"
          handleSetName={handleSetName}
          accept="all-images"
          buttonText={
            isProcessingOCR.frontImg
              ? 'procesando...'
              : isEdit && carPlates?.frontImg
              ? 'Reemplazar foto'
              : 'Subir foto'
          }
          placeholder={'Imagen no mayor a 2 MB'}
          onChange={(fileList) => handlePlateImageUpload(fileList, 'frontImg')}
          isLoading={isProcessingOCR.frontImg}
          loadingText="procesando..."
          infoMessage={platesFrontFields ? `Datos requeridos: ${platesFrontFields}` : undefined}
        />
      </div>

      {/* Back plate image section */}
      <div className="border-t pt-3">
        <FileInput
          nameFile={
            isEdit && nameFiles.backImg === ''
              ? (carPlates?.backImg?.originalName as string)
              : nameFiles.backImg
          }
          name="backImg"
          label="Foto placa trasera"
          handleSetName={handleSetName}
          accept="all-images"
          buttonText={
            isProcessingOCR.backImg
              ? 'procesando...'
              : isEdit && carPlates?.backImg
              ? 'Reemplazar foto'
              : 'Subir foto'
          }
          placeholder={'Imagen no mayor a 2 MB'}
          onChange={(fileList) => handlePlateImageUpload(fileList, 'backImg')}
          isLoading={isProcessingOCR.backImg}
          loadingText="procesando..."
          infoMessage={platesBackFields ? `Datos requeridos: ${platesBackFields}` : undefined}
        />
      </div>

      {/* OCR Confirmation Modal */}
      <OCRConfirmationModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        onConfirm={handleConfirmWithErrors}
        onCancel={handleCancelWithErrors}
        ocrResult={pendingOCRResult}
        documentType={
          pendingFieldName === 'platesDocument'
            ? 'PLATES_ALTA_PLACAS'
            : pendingFieldName === 'frontImg'
            ? 'PLATES_FRONT'
            : 'PLATES_BACK'
        }
      />
    </div>
  );
}
