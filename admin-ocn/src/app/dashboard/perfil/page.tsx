import getCurrentUser from '@/actions/getCurrentUser';
import ModalChangePass from '@/components/Modals/ModalChangePass';
import NotificationProvider from './NotificationProvider';
import NotificationMessage from './notificationMessage';
import EditImage from './EditImage';
import { areaOptions, roleOptions } from '@/constants';

export const metadata = {
  title: 'Perfil',
  description: 'Pagina del perfil de usuario',
};

export default async function Profile() {
  const user = await getCurrentUser();
  if (!user) return null;
  return (
    <NotificationProvider>
      <div className="h-[1500px] flex flex-col">
        <div className="mb-4">
          <h1 className="text-[32px] font-bold text-[#262D33]">PERFIL</h1>
        </div>
        <NotificationMessage />
        <div
          className="
          w-[876px] 
          h-[395px] 
          bg-white 
          rounded 
          px-[30px] 
          pt-[30px] 
          pb-[44px] 
          grid 
          grid-cols-[140px,auto] 
          border-[#EAECEE]
          border-[1px]
          gap-[30px]"
        >
          <EditImage />
          <div className="grid gap-3">
            <div className="flex flex-col gap-3">
              <div className="text-[24px]">Información</div>
              <div className="flex flex-col">
                <div className="w-full flex py-3 px-5 justify-between bg-[#EAECEE] rounded">
                  <p>Nombre</p>
                  <p className="font-semibold">{user.name}</p>
                </div>
                <div className="w-full flex py-3 px-5 justify-between">
                  <p>Correo electrónico</p>
                  <p className="font-semibold">{user.email}</p>
                </div>
                <div className="w-full flex py-3 px-5 justify-between bg-[#EAECEE] rounded">
                  <p>Ciudad</p>
                  <p className="font-semibold">{user.city}</p>
                </div>
                <div className="w-full flex py-3 px-5 justify-between">
                  <p>Área</p>
                  <p className="font-semibold">
                    {areaOptions.find((option) => option.value === user.area)?.label}
                  </p>
                </div>
                <div className="w-full flex py-3 px-5 justify-between bg-[#EAECEE] rounded">
                  <p>Rol</p>
                  <p className="font-semibold">
                    {roleOptions.find((option) => option.value === user.role)?.label}
                  </p>
                </div>
              </div>
            </div>
            <ModalChangePass user={user} />
          </div>
        </div>
      </div>
    </NotificationProvider>
  );
}
