import React from 'react';
import { Text, Box, Icon, Flex } from '@chakra-ui/react';
import { FaCheckCircle, FaTimesCircle, FaInfoCircle } from 'react-icons/fa';
import { RequestDocumentStatus } from '../enums';
import { RequestDocumentStatusTranslations } from '../data';
import { truncateFileName } from '@/utils/text';

export const DocumentBadge = ({ fileName }: { fileName: string }) => {
  const truncated = truncateFileName(fileName, 25);
  return (
    <Box
      display="inline-block"
      px={2}
      py={1}
      borderWidth="1px"
      bg="purple.50"
      borderColor="purple.500"
      color="purple.500"
      borderRadius="md"
      fontSize="xs"
      fontWeight="normal"
      _hover={{ bg: 'purple.100' }}
    >
      {truncated}
    </Box>
  );
};

export function formatDocumentStatus(status: RequestDocumentStatus) {
  const colorScheme = {
    [RequestDocumentStatus.approved]: 'green',
    [RequestDocumentStatus.pending]: 'yellow',
    [RequestDocumentStatus.pending_review]: 'purple',
    [RequestDocumentStatus.rejected]: 'red',
  }[status];
  return <Text color={`${colorScheme}.500`}>{RequestDocumentStatusTranslations[status] || status}</Text>;
}

export function canUploadDocument(docStatus: RequestDocumentStatus) {
  return docStatus == RequestDocumentStatus.pending;
}

export function isDocumentApproved(docStatus: RequestDocumentStatus) {
  return docStatus == RequestDocumentStatus.approved;
}

export function canExecuteAnalysis(documents: any[]) {
  const allPendingReview = documents.every((doc) => doc.analysisResult.status === 'pending');

  if (allPendingReview) return true;

  const atLeastOnePendingReview = documents.some((doc) => doc.analysisResult.status === 'pending');

  return atLeastOnePendingReview;
}

export const DocumentAnalysisStatus = ({
  analysisResult,
  expandRowIcon,
}: {
  analysisResult: any;
  expandRowIcon?: React.ReactNode;
}) => {
  if (!analysisResult || analysisResult.status === 'pending') return null;

  const getChipStyle = (type: 'error' | 'warning' | 'success') => {
    switch (type) {
      case 'error':
        return {
          bg: 'red.100',
          color: 'red.500',
          icon: FaTimesCircle,
        };
      case 'warning':
        return {
          bg: 'orange.100',
          color: 'orange.500',
          icon: FaInfoCircle,
        };
      case 'success':
        return {
          bg: 'green.100',
          color: 'green.500',
          icon: FaCheckCircle,
        };
      default:
        return {};
    }
  };

  let chipStyle;
  let count;

  if ((analysisResult.validationErrors?.length ?? 0) > 0) {
    chipStyle = getChipStyle('error');
    count = analysisResult.validationErrors.length;
  } else if ((analysisResult.validationWarnings?.length ?? 0) > 0) {
    chipStyle = getChipStyle('warning');
    count = analysisResult.validationWarnings.length;
  } else {
    chipStyle = getChipStyle('success');
    count = 0;
  }

  return (
    <Flex alignItems="center" gap={2}>
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        bg={chipStyle.bg}
        color={chipStyle.color}
        px={2}
        py={1}
        borderRadius="md"
        fontSize="sm"
        fontWeight="medium"
      >
        <Icon as={chipStyle.icon} mr={1} />
        {count > 0 && <Text>{count}</Text>}
        {expandRowIcon}
      </Box>
    </Flex>
  );
};

export const DocumentAnalysisDetails = ({ analysisResult }: { analysisResult: any }) => {
  if (!analysisResult) return null;

  return (
    <Box p={4} bg="gray.50" borderBottomWidth="1px" borderColor="gray.200">
      {analysisResult.validationErrors?.length > 0 && (
        <Box mb={analysisResult.validationWarnings?.length > 0 ? 4 : 0}>
          <Text fontWeight="bold" color="red.500" mb={2}>
            Errores:
          </Text>
          <Box pl={4}>
            {analysisResult.validationErrors.map((error: string, index: number) => (
              <Flex key={index} alignItems="flex-start" mb={2}>
                <Icon as={FaTimesCircle} color="red.500" mr={2} mt="2px" />
                <Text color="red.700">{error}</Text>
              </Flex>
            ))}
          </Box>
        </Box>
      )}

      {analysisResult.validationWarnings?.length > 0 && (
        <Box>
          <Text fontWeight="bold" color="orange.500" mb={2}>
            Advertencias:
          </Text>
          <Box pl={4}>
            {analysisResult.validationWarnings.map((warning: string, index: number) => (
              <Flex key={index} alignItems="flex-start" mb={2}>
                {/* <Icon as={FaExclamationTriangle} color="orange.500" mr={2} mt="2px" /> */}
                <Icon as={FaInfoCircle} color="orange.500" mr={2} mt="2px" />
                <Text color="orange.700">{warning}</Text>
              </Flex>
            ))}
          </Box>
        </Box>
      )}

      {analysisResult?.confidence !== undefined && (
        <Flex mt={4} justifyContent="flex-end" alignItems="center">
          <Icon as={FaInfoCircle} color="blue.500" mr={1} />
          <Text color="blue.600">Confianza: {Math.round(analysisResult.confidence * 100)}%</Text>
        </Flex>
      )}
    </Box>
  );
};
