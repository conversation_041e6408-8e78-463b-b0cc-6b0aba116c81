import EmptyState from '@/components/EmptyState';
import {
  Box,
  Card,
  CardBody,
  CardHeader,
  Circle,
  Flex,
  Heading,
  Text,
  VStack,
  Tooltip,
  Avatar,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
} from '@chakra-ui/react';
import { ActionType, EntityType } from '../enums';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { URL_API } from '@/constants';
import { formatDateTime } from '@/utils/dates';
import { useCountry } from './detail';
import ReactJsonView from '@microlink/react-json-view';

interface Event {
  id: string;
  userId: string;
  entityType: EntityType;
  actionType: ActionType;
  message: string;
  user: { id: string; name: string; image: { url: string } };
  createdAt: string;
  metadata?: Record<string, any>;
}

export default function EventsCard({ requestId }: { requestId: string }) {
  const { data: session } = useSession();
  const [events, setEvents] = useState<Event[]>([]);
  const user = session?.user as unknown as MyUser;
  const entityType = EntityType.admission_request;
  const entityId = requestId;

  const { isCountryUSA } = useCountry();

  useEffect(() => {
    // fetch events
    async function fetchEvents() {
      if (!user || events.length > 0) return;
      const res = await fetch(`${URL_API}/events/${entityType}/${entityId}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });

      const data = await res.json();
      setEvents(data.data);
    }

    fetchEvents();
  }, [entityId, entityType, events.length, requestId, user]);

  const activityHistoryText = isCountryUSA ? 'Activity History' : 'Historial de actividades';
  const noEventsText = isCountryUSA ? 'There are no events' : 'No hay eventos';

  const filterMetadata = (metadata: Record<string, any> | undefined) => {
    if (!metadata) return {};
    const filteredData = { ...metadata };

    delete filteredData.userEmail;
    return filteredData;
  };

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {activityHistoryText}
          </Heading>
        </Flex>
      </CardHeader>
      <CardBody minH={150}>
        {events.length > 0 ? (
          <Box maxHeight="400px" overflowY="auto">
            <VStack align="stretch" spacing={4}>
              <Accordion defaultIndex={[0]} allowMultiple>
                {events.map((event, index) => (
                  <Flex key={index} alignItems="center" paddingY={2} w="100%">
                    <Box mr={4}>
                      <Circle size="14px" color="white" bgColor="#5800F7"></Circle>
                    </Box>
                    <AccordionItem className="border border-gray-200 rounded-lg" w="100%">
                      <h2>
                        <AccordionButton>
                          <Box as="span" flex="1" textAlign="left" fontSize="sm">
                            <Text fontSize="sm">{event.message}</Text>
                          </Box>
                          <Flex alignItems="center">
                            <Text fontSize="xs" mr={4}>
                              {formatDateTime(event.createdAt)}
                            </Text>
                            <Tooltip label={event?.metadata?.userEmail || event.user?.name} placement="top">
                              <Avatar
                                src={
                                  event.user?.image?.url ||
                                  'https://w7.pngwing.com/pngs/81/570/png-transparent-profile-logo-computer-icons-user-user-blue-heroes-logo-thumbnail.png'
                                }
                                w="20px"
                                h="20px"
                              />
                            </Tooltip>
                          </Flex>
                        </AccordionButton>
                      </h2>
                      <AccordionPanel pb={4}>
                        <Box py={2}>
                          <Text fontSize="xs" color="gray.500">
                            Event
                          </Text>
                          <Text fontSize="sm" px={1}>
                            {event.actionType}
                          </Text>
                        </Box>
                        <Box py={2}>
                          <Text fontSize="xs" color="gray.500">
                            By
                          </Text>
                          <Text fontSize="sm" px={1}>
                            {event?.user?.id} - {event?.metadata?.userEmail || event?.user?.name}
                          </Text>
                        </Box>

                        {event?.metadata && (
                          <Text fontSize="xs" color="gray.500">
                            Metadata:
                            <ReactJsonView
                              name={false}
                              displayDataTypes={false}
                              collapsed={true}
                              enableClipboard={false}
                              displayObjectSize={false}
                              theme="rjv-default"
                              src={filterMetadata(event.metadata)}
                            />
                          </Text>
                        )}
                      </AccordionPanel>
                    </AccordionItem>
                  </Flex>
                ))}
              </Accordion>
            </VStack>
          </Box>
        ) : (
          <EmptyState title={noEventsText} />
        )}
      </CardBody>
    </Card>
  );
}
