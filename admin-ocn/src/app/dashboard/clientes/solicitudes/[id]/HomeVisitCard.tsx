import EmptyState from '@/components/EmptyState';
import { formatDateTime } from '@/utils/dates';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Flex,
  <PERSON>ing,
  Stack,
  TableContainer,
  Tbody,
  Table,
  Text,
  Th,
  Thead,
  Tr,
  Td,
  useToast,
  Modal,
  ModalOverlay,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalContent,
  VStack,
  useDisclosure,
} from '@chakra-ui/react';
import { AdmissionRequestStatus } from '../enums';
import { useCountry } from './detail';
import { usePathname, useRouter } from 'next/navigation';
import { firstIncompleteStepIndex } from './home-visit/_components/steps';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { DRIVERS_APP_URL, URL_API } from '@/constants';
import { ApiPath } from '@/constants/api.endpoints';
import { MyUser } from '@/actions/getCurrentUser';
import { DateTime } from 'luxon';
import { AppointmentStatus, AppointmentStatusTranslationMX } from '@/app/dashboard/calendar/types';
import { IoArrowForward } from 'react-icons/io5';
import { translations } from './home-visit/_components/translations';
import { Loader, RotateCcw } from 'lucide-react';
import { resetHomeVisitSchedulingLinkSendDate } from '@/actions/postAdmissionRequest';

enum ResidentOwnershipStatus {
  owned = 'owned',
  rented = 'rented',
}

enum HomeVisitStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
}

const statusColorMap = {
  [HomeVisitStatus.approved]: 'green.500',
  [HomeVisitStatus.rejected]: 'red.500',
  [HomeVisitStatus.pending]: 'yellow.500',
};

const homeVisitAppointmentStatusColorMap = {
  [AppointmentStatus.pending]: 'yellow.500',
  [AppointmentStatus.scheduled]: 'purple.500',
  [AppointmentStatus.completed]: 'green.500',
  [AppointmentStatus.noshow]: 'red.500',
  [AppointmentStatus.canceled]: 'red.500',
  [AppointmentStatus.rescheduled]: '#B95000',
  [AppointmentStatus.reassigned]: 'blue.500',
};

const statusLabelMap = {
  [HomeVisitStatus.approved]: 'Aprobada',
  [HomeVisitStatus.rejected]: 'Rechazada',
  [HomeVisitStatus.pending]: 'Pendiente',
};

interface HomeVisit {
  residentOwnershipStatus: ResidentOwnershipStatus;

  hasGarage: boolean;

  comments: string;

  images: string[];

  responsible: string;

  visitDate: string;

  status: HomeVisitStatus;

  behaviourOfCustomerDuringCall: string;

  characteristicsOfGarage: string;

  doesProofOfAddressMatchLocation: string;

  homeVisitStepsStatus: {
    personal: string;
    contact: string;
    address: string;
    family: string;
    property: string;
    automobile: string;
    debt: string;
    references: string;
    outcome: string;
  };

  houseInformation: {
    ownProperty: string;
    nameOfOwner: string;
    ownerRelative: string;
    ownerRelativeRelation: string;
    ownerPhone: string;
    typeOfHousing: string;
    noOfBedrooms: number;
    livingRoom: string;
    dinningRoom: string;
    kitchen: string;
    television: string;
    audioSystem: string;
    stove: string;
    refrigerator: string;
    washingMachine: string;
  };
  media: null;
  proofOfPropertyOwnership: [];
  statusReason: string;
  suggestedStatus: string;
  visitTime: string;
  visitorEmailAddress: string;
}

enum HomeVisitSchedulingActionSource {
  admin_portal = 'admin-portal',
  customer = 'customer',
}

export default function HomeVisitDataCard({
  homeVisit,
  status,
  requestId,
  request,
}: {
  homeVisit: HomeVisit | null;
  status: AdmissionRequestStatus;
  requestId: string;
  request: any;
}) {
  const pathname = usePathname();
  const router = useRouter();

  const handleClickDetails = () => {
    const stepIndex = firstIncompleteStepIndex(homeVisit?.homeVisitStepsStatus!);
    if (stepIndex !== null && stepIndex >= 0) {
      router.replace(pathname + `/${ApiPath.HOME_VISIT}?stepIndex=${stepIndex}`);
      return;
    }
    router.replace(pathname + `/${ApiPath.HOME_VISIT}`);
  };

  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  const [appointment, setAppointment] = useState<any>();
  const toast = useToast();

  useEffect(() => {
    async function getAppointment({ accessToken }: { id: string; accessToken: string }) {
      try {
        const res = await fetch(`${URL_API}${ApiPath.CALENDAR_EVENTS}/application/${requestId}`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });
        const response = await res.json();
        if (response.success) {
          setAppointment(response.data);
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Error occured while fetching appointment',
          status: 'error',
          duration: 2000,
        });
      }
    }

    if (!appointment && user?.accessToken) {
      getAppointment({
        id: requestId,
        accessToken: user?.accessToken,
      });
    }
  }, [appointment, requestId, toast, user?.accessToken]);

  const appointmentStatus = appointment?.status;

  const handleBookCustomerAppointment = () => {
    if (
      [
        AdmissionRequestStatus.created,
        AdmissionRequestStatus.earnings_analysis,
        AdmissionRequestStatus.documents_analysis,
      ].includes(status)
    ) {
      toast({
        title: 'Error',
        description: 'El cliente aún no ha sido aprobado para la visita domiciliaria.',
        status: 'error',
        duration: 3000,
      });
      return;
    }

    const homeVisitSchedulingUrl = appointmentStatus
      ? `${DRIVERS_APP_URL}/${requestId}/schedule-home-visit?step=reschedule&source=${HomeVisitSchedulingActionSource.admin_portal}&entity=${user?.id}`
      : `${DRIVERS_APP_URL}/${requestId}/schedule-home-visit?source=${HomeVisitSchedulingActionSource.admin_portal}&entity=${user?.id}`;
    window.open(homeVisitSchedulingUrl, '_blank');
  };

  const homeVisitScheduleLinkSendDate = request?.homeVisitScheduleLinkSendDate
    ? DateTime.fromISO(request?.homeVisitScheduleLinkSendDate)
        .setZone('local')
        .toFormat("d 'de' MMMM 'de' yyyy, hh:mm a")
    : '';

  const visitScheduledDate = appointment?.date
    ? DateTime.fromISO(appointment?.date, { zone: 'utc' }).toFormat("d 'de' MMMM 'de' yyyy")
    : '';

  const startTimeLocal = appointment?.startTime
    ? DateTime.fromISO(appointment.startTime, { zone: 'utc' }).setZone('local').toFormat('hh:mm a')
    : '';

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {translations.es.InformationAboutAppointmentForHomeVisit}
          </Heading>
        </Flex>
      </CardHeader>
      <CardBody>
        <TableContainer fontSize="sm">
          <Table variant="striped">
            <Tbody>
              <Tr>
                <Td p={2}>{translations.es.DateOfReservationLink}</Td>
                <Td p={2}>
                  {homeVisitScheduleLinkSendDate ? (
                    <HomeVisitSchedulingLinkSendDate
                      homeVisitScheduleLinkSendDate={homeVisitScheduleLinkSendDate}
                      requestId={requestId}
                    />
                  ) : null}
                </Td>
              </Tr>
              <Tr>
                <Td p={2}>{translations.es.HomeVisitAppointmentStatus}</Td>
                <Td p={2}>
                  {appointmentStatus ? (
                    <AppointmentStatusPreview appointment={appointment} />
                  ) : (
                    <Text color={homeVisitAppointmentStatusColorMap[AppointmentStatus.pending]}>
                      {homeVisitScheduleLinkSendDate && AppointmentStatusTranslationMX.pending}
                    </Text>
                  )}
                </Td>
              </Tr>
              <Tr>
                <Td p={2}>{translations.es.DateOfHomeVisit}</Td>
                <Td p={2}>
                  {visitScheduledDate}
                  {','} {startTimeLocal}
                </Td>
              </Tr>
              <Tr>
                <Td p={2}>{translations.es.HomeVisitorEmailID}</Td>
                <Td p={2}>{appointment?.user?.email}</Td>
              </Tr>
            </Tbody>
          </Table>
        </TableContainer>
        <div className="pt-4">
          <button
            onClick={handleBookCustomerAppointment}
            className="font-semibold  bg-white text-[#5800F7] px-2.5 py-2 text-xs rounded border-[#5800F7] border hover:bg-gray-100"
          >
            {translations.es.ScheduleAnAppointmentWithTheClient}
          </button>
        </div>
      </CardBody>

      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center" paddingBottom={4}>
          <Heading size="md" color="gray.600">
            {'Información de la visita a domicilio'}
          </Heading>
          {homeVisit && (
            <Text color={statusColorMap[homeVisit.status] || 'gray.500'}>
              {statusLabelMap[homeVisit.status] || 'No home visit recorded'}
            </Text>
          )}
        </Flex>
      </CardHeader>

      <CardBody>
        <Stack spacing={2}>{!homeVisit && <NotHomeVisitMarkup status={status} />}</Stack>
        {homeVisit && (
          <TableContainer fontSize="sm">
            <Table variant="striped">
              <Thead>
                <Tr>
                  <Th>Fecha</Th>
                  <Th>Visitante casero</Th>
                  <Th>Estatus</Th>
                  <Th textAlign="right">Acciones</Th>
                </Tr>
              </Thead>
              <Tbody>
                <Tr>
                  <Td>{homeVisit.visitDate ? formatDateTime(homeVisit.visitDate) : ''}</Td>
                  <Td>{homeVisit?.visitorEmailAddress}</Td>
                  <Td>
                    <Text color={statusColorMap[homeVisit.status] || 'gray.500'}>
                      {statusLabelMap[homeVisit.status]}
                    </Text>
                  </Td>
                  <Td textAlign="right">
                    <button
                      onClick={handleClickDetails}
                      className="font-semibold  bg-white text-[#5800F7] px-2.5 py-1 text-xs rounded border-[#5800F7] border hover:bg-gray-100"
                    >
                      Ver detalle
                    </button>
                  </Td>
                </Tr>
              </Tbody>
            </Table>
          </TableContainer>
        )}
      </CardBody>
    </Card>
  );
}

const NotHomeVisitMarkup = ({ status }: { status: AdmissionRequestStatus }) => {
  const isDisabled = status !== AdmissionRequestStatus.home_visit;

  const router = useRouter();
  const pathName = usePathname();

  const { isCountryUSA } = useCountry();
  const registerVisitText = isCountryUSA ? 'Register visit' : 'Registrar visita';
  const homeVisitText = isCountryUSA ? 'No home visit recorded' : 'No se ha registrado visita a domicilio';

  const handleButtonClick = () => {
    router.push(pathName + `/${ApiPath.HOME_VISIT}`);
  };

  const actionButton = isDisabled ? null : (
    <Button
      sx={{
        h: '34px',
      }}
      className="bg-white cursor-pointer hover:bg-gray-50 border border-[#5800F7] text-[#5800F7] font-medium"
      onClick={handleButtonClick}
    >
      {registerVisitText}
    </Button>
  );
  return <EmptyState minH={32} title={homeVisitText} action={actionButton} />;
};

const AppointmentStatusPreview = (props: { appointment: any }) => {
  const { appointment } = props;
  const { onClose, onOpen, isOpen } = useDisclosure();

  return (
    <>
      <Text
        color={
          homeVisitAppointmentStatusColorMap[
            appointment.status as keyof typeof homeVisitAppointmentStatusColorMap
          ] || 'gray.500'
        }
      >
        {AppointmentStatusTranslationMX[appointment?.status as keyof typeof AppointmentStatusTranslationMX]}
        <span className="pl-5 text-primaryPurple cursor-pointer" onClick={onOpen}>
          {translations.es.AppointmentHistory}
          <IoArrowForward className="inline-flex transform rotate-[-60deg]" size={22} />
        </span>
      </Text>
      <AppointmentStatusPreviewDialog isOpen={isOpen} onClose={onClose} appointment={appointment} />
    </>
  );
};

interface IAppointmentStatusHistoryDialog {
  isOpen: boolean;
  onClose: () => void;
  appointment: any;
}

function AppointmentStatusPreviewDialog({ isOpen, onClose, appointment }: IAppointmentStatusHistoryDialog) {
  const statusHistory = (appointment?.statusHistory as Array<Record<string, any>>) || [];

  return (
    <>
      {
        <Modal isOpen={isOpen} onClose={onClose}>
          <ModalOverlay />
          <ModalContent className=" max-w-[1000px]">
            <ModalHeader>{translations.es.AppointmentHistory}</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack spacing={4} align="stretch" className="pb-4">
                <div className="overflow-hidden rounded-lg border border-primaryLightPastelBlueGray">
                  <Table variant="simple" className="w-full">
                    <Thead>
                      <Tr className="bg-primaryOffWhite text-primaryBlueGray">
                        <Th>{translations.es.Status}</Th>
                        <Th>{translations.es.Date}</Th>
                        <Th>{translations.es.ActionBy}</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {statusHistory.map((history: any) => (
                        <Tr key={history.date} className="border-b">
                          <Td
                            color={
                              homeVisitAppointmentStatusColorMap[
                                history.status as keyof typeof homeVisitAppointmentStatusColorMap
                              ] || 'gray.500'
                            }
                          >
                            {
                              AppointmentStatusTranslationMX[
                                history?.status as keyof typeof AppointmentStatusTranslationMX
                              ]
                            }
                          </Td>
                          <Td>
                            {DateTime.fromISO(history?.date)
                              .setZone('local')
                              .toFormat("d 'de' MMMM 'de' yyyy, hh:mm a")}
                          </Td>
                          <Td>
                            {history?.source === HomeVisitSchedulingActionSource.admin_portal
                              ? history?.actionBy?.email ?? ''
                              : history?.source === HomeVisitSchedulingActionSource.customer
                              ? translations.es.Customer
                              : ''}
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </div>
              </VStack>
            </ModalBody>
          </ModalContent>
        </Modal>
      }
    </>
  );
}

interface IHomeVisitAppointmentSchedulingLinkSendDateProps {
  homeVisitScheduleLinkSendDate: string;
  requestId: string;
}

const HomeVisitSchedulingLinkSendDate = (props: IHomeVisitAppointmentSchedulingLinkSendDateProps) => {
  const { homeVisitScheduleLinkSendDate, requestId } = props;

  const router = useRouter();
  const toast = useToast();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const isSuperAdmin = user?.role === 'superadmin';

  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    try {
      setLoading(true);
      const response = await resetHomeVisitSchedulingLinkSendDate({ requestId: requestId });
      if (response && response.success) {
        toast({
          title: 'Success',
          description:
            'La fecha de envío del enlace de programación de visitas domiciliarias se ha restablecido correctamente.',
          status: 'success',
          duration: 2000,
        });
        router.refresh();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Se produjo un error al restablecer la fecha de envío del enlace de programación.',
        status: 'error',
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <span>
      {homeVisitScheduleLinkSendDate}{' '}
      {isSuperAdmin &&
        (loading ? (
          <Loader className="px-1 inline-block cursor-pointer" size={22} />
        ) : (
          <RotateCcw className="px-1 inline-block cursor-pointer" size={22} onClick={handleClick} />
        ))}
    </span>
  );
};
