'use client';
import { useEffect, useState } from 'react';
import {
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Button,
  Grid,
  FormLabel,
  Input,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
} from '@chakra-ui/react';
import { Formik, Form, FieldArray } from 'formik';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import * as Yup from 'yup';
import { URL_API } from '@/constants';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import FormErrorMessage from './_components/FormErrorMessage';
import { ApiPath } from '@/constants/api.endpoints';

export const AddEarnings = ({
  requestId,
  admissionRequest,
}: // accounts,
{
  requestId: string;
  accounts: Array<{ accountId: string; platform: string; status: string }>;
  admissionRequest: any;
}) => {
  const router = useRouter();
  const toast = useToast();
  const pathname = usePathname();
  const { user } = useCurrentUser();
  const [platform, setPlatform] = useState('');
  const [totalTrips, setTotalTrips] = useState<number>(0);
  const [acceptanceRate, setAcceptanceRate] = useState<number>(0);
  const [cancellationRate, setCancellationRate] = useState<number>(0);
  const [rating, setRating] = useState<number>(0);
  const [timeSinceFirstTrip, setTimeSinceFirstTrip] = useState<number>(0);

  // const existingPlatforms = accounts.map((account) => account.platform.toLowerCase());
  const isCountryUSA = admissionRequest?.personalData?.country?.toLowerCase() === 'us';

  //translations
  const validNumber = isCountryUSA ? 'Must be a valid number' : 'Debe ser un número válido';
  const fieldRequired = isCountryUSA ? 'This field is required' : 'Este campo es obligatorio';
  const minPercentageValue = isCountryUSA ? 'Value must be at least 0' : 'El valor debe ser al menos 0';
  const maxPercentageValue = isCountryUSA ? 'Value cannot exceed 100' : 'El valor no puede exceder 100';
  const postiveNumberRequired = isCountryUSA ? 'Value must be positive' : 'El valor debe ser positivo';
  const platformSaved = isCountryUSA ? 'Platform Saved' : 'Plataforma guardada';
  const platformDataSavedSuccessfully = isCountryUSA
    ? 'The platform data has been saved successfully.'
    : 'Los datos de la plataforma se han guardado correctamente.';
  const errorSavingPlatformData = isCountryUSA
    ? 'Error saving platform data.'
    : 'Error al guardar los datos de la plataforma.';
  const errorFetchingData = isCountryUSA
    ? 'Error occured while fetching platform data.'
    : 'Se produjo un error al obtener datos de la plataforma.';
  const platformData = isCountryUSA ? 'Platform Data' : 'Datos de la plataforma';
  const platfromTitle = isCountryUSA ? 'Platform' : 'Plataforma';
  const selectPlatform = isCountryUSA ? 'Select a platform' : 'Selecciona una plataforma';
  const totalTripsTitle = isCountryUSA ? 'Total trips' : 'Viajes totales';
  const acceptanceRateTitle = isCountryUSA ? 'Acceptance rate' : 'Tasa de aceptación';
  const cancellationRateTitle = isCountryUSA ? 'Cancellation rate' : 'Tasa de cancelación';
  const ratingTitle = isCountryUSA ? 'Rating' : 'Clasificación';
  const timeSinceFirstTripTitle = isCountryUSA
    ? 'Time Since First Trip (years)'
    : 'Tiempo desde el primer viaje (años)';
  const earningsTitle = isCountryUSA ? 'Earnings' : 'Ganancias';
  const saveTitle = isCountryUSA ? 'Save' : 'Ahorrar';

  function onClose() {
    router.back();
  }

  const formatDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
  };

  const today = new Date();
  const getLastMonday = (date: Date) => {
    const dayOfWeek = date.getDay();
    const diff = dayOfWeek >= 1 ? dayOfWeek - 1 : 6;
    const lastMonday = new Date(date);
    lastMonday.setDate(date.getDate() - diff - 7);
    return lastMonday;
  };

  const lastMonday = getLastMonday(today);
  const initialDates = Array.from({ length: 12 }, (_, i) => {
    const date = new Date(lastMonday);
    date.setDate(lastMonday.getDate() - i * 7);
    return formatDate(date);
  });

  const [earnings, setEarnings] = useState(initialDates.map((date) => ({ week: date, earning: 0 })));

  const initProfileMetics = () => {
    setTotalTrips(0);
    setAcceptanceRate(0);
    setCancellationRate(0);
    setRating(0);
    setTimeSinceFirstTrip(0);
  };
  const initEarnings = () => {
    setEarnings(initialDates.map((date) => ({ week: date, earning: 0 })));
  };

  const searchParams = useSearchParams();
  const isOpen = searchParams.get('dialog') === 'earnings-analysis';

  const validationSchema = Yup.object().shape({
    totalTrips: Yup.number().min(0, postiveNumberRequired).typeError(validNumber).required(fieldRequired),
    acceptanceRate: Yup.number()
      .typeError(validNumber)
      .min(0, minPercentageValue)
      .max(100, maxPercentageValue)
      .required(fieldRequired),
    cancellationRate: Yup.number()
      .typeError(validNumber)
      .min(0, minPercentageValue)
      .max(100, maxPercentageValue)
      .required(fieldRequired),
    rating: Yup.number()
      .typeError(validNumber)
      .min(0, minPercentageValue)
      .max(100, maxPercentageValue)
      .required(fieldRequired),
    timeSinceFirstTrip: Yup.number()
      .min(0, postiveNumberRequired)
      .typeError(validNumber)
      .required(fieldRequired),
    platform: Yup.string().required(fieldRequired),
    earnings: Yup.array().of(
      Yup.object().shape({
        week: Yup.string().required(fieldRequired),
        earning: Yup.number().min(0, postiveNumberRequired).required(fieldRequired),
      })
    ),
  });

  const formatEarningsDates = (values: any) => {
    const formattedEarnings = values.earnings.map((earning: any) => ({
      ...earning,
      week: earning.week.split('/').reverse().join('-'),
    }));
    return { ...values, earnings: formattedEarnings };
  };

  async function handleSubmit(values: any, actions: { setSubmitting: (arg0: boolean) => void }) {
    try {
      if (!user) return null;
      const formattedValues = formatEarningsDates({
        totalTrips,
        acceptanceRate,
        cancellationRate,
        rating,
        timeSinceFirstTrip,
        platform,
        earnings,
      });
      const response = await fetch(
        `${URL_API}${ApiPath.ADMISSION_REQUEST}${requestId}/save-driver-profile-data`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user.accessToken}`,
          },
          body: JSON.stringify(formattedValues),
        }
      );
      if (response.ok) {
        const getEarnings = await fetch(`${URL_API}/ocr/get-earnings-analysis/${requestId}`);
        await getEarnings.json();
        toast({
          title: platformSaved,
          description: platformDataSavedSuccessfully,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        setTimeout(() => {
          window.location.reload();
        }, 2500);
      } else {
        throw new Error(errorSavingPlatformData);
      }
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: errorSavingPlatformData,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      initProfileMetics();
      initEarnings();
      actions.setSubmitting(false);
      router.refresh();
      router.push(`${pathname}`);
      onClose();
    }
  }

  useEffect(() => {
    if (platform && user && requestId) {
      try {
        const getDriverProfileData = async () => {
          const res = await fetch(`${URL_API}/ocr/driver-profile-data/${requestId}/${platform}`, {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            },
          });
          const response = await res.json();
          if (response.profileMetrics) {
            setAcceptanceRate(response?.profileMetrics?.acceptanceRate);
            setTotalTrips(response?.profileMetrics?.lifetimeTrips);
            setCancellationRate(response?.profileMetrics?.cancellationRate);
            setRating(response?.profileMetrics?.rating);
            setTimeSinceFirstTrip(response?.profileMetrics?.timeSinceFirstTrip);
          } else {
            initProfileMetics();
          }
          if (response.earnings && response?.earnings?.length > 0) {
            const valueClone = structuredClone(earnings);
            response.earnings.map((earning: any) => {
              const earningDate = new Date(earning.earningDate);
              const formattedEarningDate = formatDate(earningDate);
              for (let index = 0; index < valueClone.length; index++) {
                if (valueClone[index].week === formattedEarningDate) {
                  valueClone[index].earning = earning.amount;
                  break;
                }
              }
            });
            setEarnings(valueClone);
          } else {
            initEarnings();
          }
        };
        getDriverProfileData();
      } catch (error) {
        console.log(error);
        toast({
          title: 'Error',
          description: errorFetchingData,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    }
  }, [platform, user, requestId]);

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{platformData}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Formik
            initialValues={{
              totalTrips,
              acceptanceRate,
              cancellationRate,
              rating,
              timeSinceFirstTrip,
              platform,
              earnings,
            }}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ values, handleChange, handleBlur, isValid, isSubmitting, errors, touched }) => (
              <Form>
                <Grid gap={4}>
                  <FormLabel>{platfromTitle}</FormLabel>
                  <Select
                    name="platform"
                    value={platform}
                    onChange={(e) => {
                      const platfromVal = e.target.value.toLowerCase();
                      setPlatform(platfromVal);
                      values.platform = platfromVal;
                      handleChange(e);
                    }}
                    onBlur={handleBlur}
                  >
                    <option value="">{selectPlatform}</option>
                    {isCountryUSA ? (
                      <>
                        <option value="uber">Uber</option>
                        <option value="lyft">Lyft</option>
                        <option value="other">Other</option>
                      </>
                    ) : (
                      <>
                        <option value="didi">Didi</option>
                        <option value="uber">Uber</option>
                        <option value="indriver">InDriver</option>
                      </>
                    )}
                  </Select>
                  <FormErrorMessage
                    message={errors.platform && touched.platform ? errors.platform : ''}
                  ></FormErrorMessage>
                  <FormLabel>{totalTripsTitle}</FormLabel>
                  <Input
                    type="number"
                    name={`totalTrips`}
                    value={totalTrips}
                    onChange={(e) => {
                      const totalTripsVal = e.target.value;
                      values.totalTrips = parseInt(totalTripsVal);
                      setTotalTrips(parseInt(totalTripsVal));
                      handleChange(e);
                    }}
                    onBlur={handleBlur}
                  />
                  <FormErrorMessage
                    message={errors.totalTrips && touched.totalTrips ? errors.totalTrips : ''}
                  ></FormErrorMessage>
                  <FormLabel>{acceptanceRateTitle}</FormLabel>
                  <Input
                    type="number"
                    name={`acceptanceRate`}
                    value={acceptanceRate}
                    onChange={(e) => {
                      const acceptanceRateVal = e.target.value;
                      values.acceptanceRate = parseInt(acceptanceRateVal);
                      setAcceptanceRate(parseFloat(acceptanceRateVal));
                      handleChange(e);
                    }}
                    onBlur={handleBlur}
                  />
                  <FormErrorMessage
                    message={errors.acceptanceRate && touched.acceptanceRate ? errors.acceptanceRate : ''}
                  ></FormErrorMessage>
                  <FormLabel>{cancellationRateTitle}</FormLabel>
                  <Input
                    type="number"
                    name={`cancellationRate`}
                    value={cancellationRate}
                    onChange={(e) => {
                      const cancellationRateVal = e.target.value;
                      values.cancellationRate = parseInt(cancellationRateVal);
                      setCancellationRate(parseFloat(cancellationRateVal));
                      handleChange(e);
                    }}
                    onBlur={handleBlur}
                  />
                  <FormErrorMessage
                    message={
                      errors.cancellationRate && touched.cancellationRate ? errors.cancellationRate : ''
                    }
                  ></FormErrorMessage>
                  <FormLabel>{ratingTitle}</FormLabel>
                  <Input
                    type="number"
                    name={`rating`}
                    value={rating}
                    onChange={(e) => {
                      const ratingVal = e.target.value;
                      values.rating = parseInt(ratingVal);
                      setRating(parseFloat(ratingVal));
                      handleChange(e);
                    }}
                    onBlur={handleBlur}
                  />
                  <FormErrorMessage
                    message={errors.rating && touched.rating ? errors.rating : ''}
                  ></FormErrorMessage>
                  <FormLabel>{timeSinceFirstTripTitle}</FormLabel>
                  <Input
                    type="number"
                    name={`timeSinceFirstTrip`}
                    value={timeSinceFirstTrip}
                    onChange={(e) => {
                      const timeSinceFirstTripVal = e.target.value;
                      values.timeSinceFirstTrip = parseInt(timeSinceFirstTripVal);
                      setTimeSinceFirstTrip(parseInt(timeSinceFirstTripVal));
                      handleChange(e);
                    }}
                    onBlur={handleBlur}
                  />
                  <FormErrorMessage
                    message={
                      errors.timeSinceFirstTrip && touched.timeSinceFirstTrip ? errors.timeSinceFirstTrip : ''
                    }
                  />
                  <FormLabel>{earningsTitle}</FormLabel>
                  <FieldArray name="earnings">
                    {() => (
                      <>
                        {earnings.map((earning, index) => (
                          <Grid key={index} gap={4} templateColumns="repeat(2, 1fr)">
                            <Input
                              type="text"
                              name={`earnings[${index}].week`}
                              value={earning.week}
                              onChange={handleChange}
                              onBlur={handleBlur}
                            />
                            <NumberInput
                              name={`earnings[${index}].earning`}
                              min={0}
                              value={earning.earning}
                              onChange={(valueString) => {
                                const valueClone = structuredClone(earnings);
                                const earningVal = parseFloat(valueString) || 0;
                                values.earnings[index].earning = earningVal;
                                valueClone[index].earning = earningVal;
                                setEarnings(valueClone);
                                handleChange({
                                  target: {
                                    name: `earnings[${index}].earning`,
                                    value: parseFloat(valueString),
                                  },
                                });
                              }}
                              onBlur={handleBlur}
                              precision={2}
                            >
                              <NumberInputField />
                              <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                              </NumberInputStepper>
                            </NumberInput>
                          </Grid>
                        ))}
                      </>
                    )}
                  </FieldArray>
                </Grid>
                <ModalFooter>
                  <Button type="submit" isLoading={isSubmitting} disabled={!isValid || isSubmitting}>
                    {saveTitle}
                  </Button>
                </ModalFooter>
              </Form>
            )}
          </Formik>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
