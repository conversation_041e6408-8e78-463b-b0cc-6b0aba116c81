'use client';

import React, { useEffect, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { useQueryState } from 'nuqs';
import { parseAsString, parseAsStringEnum } from 'nuqs';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { DataTable } from '@/components/data-table/data-table';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { DataTableToolbar } from '@/components/data-table/data-table-toolbar';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { useDataTable } from '@/hooks/use-data-table';
import { ReassignLeadsReason } from '@/constants'; // or wherever you have this enum

export interface ReassignAuditLog {
  _id: string;
  fromAgentName: string;
  fromAgentId: string;
  toAgentName: string;
  toAgentId: string;
  solicitudeId: string;
  reassignedByName: string;
  reassignedBy: string;
  reason: string;
  description: string;
  createdAt: string;
}

export function ReassignAuditLogsTable({ refetchData }: any) {
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const [fromAgentName] = useQueryState('fromAgentName', parseAsString.withDefault(''));
  const [toAgentName] = useQueryState('toAgentName', parseAsString.withDefault(''));
  const [solicitudeId] = useQueryState('solicitudeId', parseAsString.withDefault(''));
  const [reassignedByName] = useQueryState('reassignedByName', parseAsString.withDefault(''));
  const [reason] = useQueryState('reason', parseAsStringEnum(Object.values(ReassignLeadsReason)));
  const [startDate] = useQueryState('startDate', parseAsString.withDefault(''));
  const [endDate] = useQueryState('endDate', parseAsString.withDefault(''));

  const [sortField] = useQueryState('sortField', parseAsString.withDefault('createdAt'));
  const [sortOrder] = useQueryState('sortOrder', parseAsStringEnum(['asc', 'desc']).withDefault('desc'));

  // Local state
  const [data, setData] = useState<ReassignAuditLog[]>([]);
  const [pageCount, setPageCount] = useState(1);

  const columns = React.useMemo<ColumnDef<ReassignAuditLog>[]>(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Seleccionar todo"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Seleccionar fila"
          />
        ),
        size: 32,
        enableSorting: false,
        enableHiding: false,
      },
      {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: ({ column }) => <DataTableColumnHeader column={column} title="Fecha y Hora" />,
        cell: ({ cell }) => {
          const date = new Date(cell.getValue<string>());
          return date.toLocaleString();
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'fromAgentName',
        accessorKey: 'fromAgentName',
        header: ({ column }) => <DataTableColumnHeader column={column} title="Agente Anterior" />,
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Agente Anterior',
          placeholder: 'Buscar agente anterior...',
          variant: 'text',
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'toAgentName',
        accessorKey: 'toAgentName',
        header: ({ column }) => <DataTableColumnHeader column={column} title="Nuevo Agente" />,
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Nuevo Agente',
          placeholder: 'Buscar nuevo agente ...',
          variant: 'text',
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'solicitudeId',
        accessorKey: 'solicitudeId',
        header: ({ column }) => <DataTableColumnHeader column={column} title="Solicitude ID" />,
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Solicitude ID',
          placeholder: 'Buscar Solicitude ID ...',
          variant: 'text',
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'reassignedByName',
        accessorKey: 'reassignedByName',
        header: ({ column }) => <DataTableColumnHeader column={column} title="Reasignado por" />,
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Reasignado por',
          placeholder: 'Buscar usuario...',
          variant: 'text',
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'reason',
        accessorKey: 'reason',
        header: ({ column }) => <DataTableColumnHeader column={column} title="Razón" />,
        cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return (
            <Badge variant="outline" className="capitalize">
              {value}
            </Badge>
          );
        },
        meta: {
          label: 'Razón',
          variant: 'select',
          options: Object.values(ReassignLeadsReason).map((r) => ({
            label: r,
            value: r,
          })),
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'description',
        accessorKey: 'description',
        header: ({ column }) => <DataTableColumnHeader column={column} title="Descripción" />,
        cell: ({ cell }) => (
          <div>
            <p className="text-wrap">{cell.getValue<string>()}</p>
          </div>
        ),
        enableColumnFilter: true,
        enableSorting: false,
      },
    ],
    []
  );

  const {
    table,
    page: currentPage,
    perPage,
  } = useDataTable({
    data,
    columns,
    pageCount,
    initialState: {
      sorting: [{ id: sortField, desc: sortOrder === 'desc' }],
    },
    getRowId: (row: any) => row._id,
    shallow: false,
    clearOnDefault: true,
  });

  const fetchData = async () => {
    const params = new URLSearchParams();

    if (fromAgentName) params.append('fromAgentName', fromAgentName);
    if (toAgentName) params.append('toAgentName', toAgentName);
    if (solicitudeId) params.append('solicitudeId', solicitudeId);
    if (reassignedByName) params.append('reassignedByName', reassignedByName);
    if (reason) params.append('reason', reason);
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    params.append('page', String(currentPage));
    params.append('limit', String(perPage));
    params.append('sortField', sortField);
    params.append('sortOrder', sortOrder);

    try {
      const response = await fetch(`${URL_API}/leadAssignment/reassignmentAuditLogs?${params}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      const result = await response.json();
      setData(result.data || []);
      setPageCount(result.pagination.totalPages || 1);
    } catch (error) {
      console.error('Error fetching reassign audit logs:', error);
    }
  };

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [
    user,
    fromAgentName,
    toAgentName,
    solicitudeId,
    reassignedByName,
    reason,
    startDate,
    endDate,
    currentPage,
    perPage,
    refetchData,
  ]);

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border overflow-auto">
        <DataTable table={table}>
          <DataTableToolbar table={table} />
        </DataTable>
      </div>
    </div>
  );
}
