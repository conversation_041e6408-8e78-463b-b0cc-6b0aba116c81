'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';
import { MultiSelect } from '../../../../../components/ui/multi-select';
import { DateTimePicker } from '../../../../../components/ui/date-time-picker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BlockLeadAssignationReason } from '@/constants';
import { toast } from 'sonner';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';

export function BlockAgentCreateModal({ refetchData, setRefetchData, agentOptions }: any) {
  const [open, setOpen] = useState(false);
  const [agentIds, setAgentIds] = useState<string[]>([]);
  const [blockedFrom, setBlockedFrom] = useState<Date | null>(null);
  const [blockedUntil, setBlockedUntil] = useState<Date | null>(null);
  const [reason, setReason] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [overlappingBlocks, setOverlappingBlocks] = useState<any | null>(null); // <-- New state
  const { data: session } = useSession();
  const user = session?.user;

  const handleSubmit = async () => {
    if (!blockedFrom || !blockedUntil || !reason || agentIds.length === 0) {
      toast.error('All fields are required');
      return;
    }

    setLoading(true);
    setOverlappingBlocks(null); // Reset previous overlaps

    try {
      const response = await fetch(`${URL_API}/leadAssignment/block-leads`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user?.accessToken}`,
        },
        body: JSON.stringify({
          userId: user?.id,
          agentIds,
          blockedFrom,
          blockedUntil,
          reason,
          blockedBy: user?.id,
        }),
      });

      if (!response.ok) {
        if (response.status === 409) {
          const data = await response.json();
          setOverlappingBlocks(data.overlappingBlocks); // Save overlap info
        } else {
          toast.error('Error al bloquear agentes.');
        }
      } else {
        toast.success('La asignación de leads ha sido bloqueada exitosamente');
        setRefetchData(!refetchData);
        setOpen(false);
        resetForm();
      }
    } catch (error) {
      console.error(error);
      toast.error('Error al bloquear agentes.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setAgentIds([]);
    setBlockedFrom(null);
    setBlockedUntil(null);
    setReason('');
    setOverlappingBlocks(null);
  };

  const handleClose = () => {
    setOpen(false);
    resetForm();
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        setOpen(newOpen);
      }}
    >
      <DialogTrigger asChild>
        <Button>Bloquear Agente(s)</Button>
      </DialogTrigger>
      <DialogContent className="max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bloquear asignación de contactos</DialogTitle>
          <DialogDescription>Seleccionar agentes y establecer fechas de bloqueo.</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Agent Multi-select */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="agents" className="text-right font-medium">
              Agentes
            </label>
            <MultiSelect
              options={agentOptions}
              onValueChange={setAgentIds}
              defaultValue={agentIds}
              placeholder="Seleccionar agentes"
              variant="inverted"
              maxCount={5}
              className="col-span-3"
            />
          </div>

          {/* Start Date-Time Picker */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="start" className="text-right font-medium">
              Bloqueado desde
            </label>
            <DateTimePicker
              selectedDate={blockedFrom}
              onDateChange={setBlockedFrom}
              disabledHoursBefore={new Date(Date.now())}
              className="col-span-3"
            />
          </div>

          {/* End Date-Time Picker */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="end" className="text-right font-medium">
              Bloqueado hasta
            </label>
            <DateTimePicker
              selectedDate={blockedUntil}
              onDateChange={setBlockedUntil}
              disabledHoursBefore={blockedFrom}
              className="col-span-3"
            />
          </div>

          {/* Reason Dropdown */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="reason" className="text-right font-medium">
              Razón
            </label>
            <Select onValueChange={setReason} value={reason}>
              <SelectTrigger id="reason" className="col-span-3">
                <SelectValue placeholder="Selecciona una razón" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(BlockLeadAssignationReason).map((r) => (
                  <SelectItem key={r} value={r}>
                    {r}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Show overlapping blocks if exists */}
        {overlappingBlocks && (
          <div className="bg-red-100 p-4 rounded-md border border-red-300 text-sm">
            <ul className="space-y-2">
              {overlappingBlocks.map((agentBlock: any) => (
                <li key={agentBlock.agentId}>
                  <ul className="ml-4 mt-1 space-y-1">
                    {agentBlock.overlappingBlocks.map((block: any) => (
                      <li key={block.blockId} className="text-red-700">
                        <span>{`${agentBlock.agentName} ya está bloqueado desde el ${new Date(
                          block.blockedFrom
                        ).toLocaleString()} hasta el ${new Date(
                          block.blockedUntil
                        ).toLocaleString()}. con el número de serie ${block.serialNumber}`}</span>
                      </li>
                    ))}
                  </ul>
                </li>
              ))}
            </ul>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Guardando...' : 'Confirmar'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
