'use client';

import React, { useEffect, useState } from 'react';
import { ColumnDef, Column } from '@tanstack/react-table';
import { useQueryState } from 'nuqs';
import { parseAsString, parseAsInteger, parseAsStringEnum } from 'nuqs';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { DataTable } from '@/components/data-table/data-table';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { DataTableToolbar } from '@/components/data-table/data-table-toolbar';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { useDataTable } from '@/hooks/use-data-table';

// Define audit log interface
interface BlockLeadAssignationAudit {
  _id: string;
  timestamp: string;
  action: 'create' | 'update' | 'delete';
  agentId: string;
  userName: string;
  agentName: string;
  serialNumber: number;
  description: string;
}

export function BlockLeadAssignationAuditTable({ refetchData, agentOptions }: any) {
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  // URL state filters
  const [agentId] = useQueryState('agentId', parseAsString.withDefault(''));
  const [agentName] = useQueryState('agentName', parseAsString.withDefault(''));
  const [userName] = useQueryState('userName', parseAsString.withDefault(''));
  const [action] = useQueryState('action', parseAsStringEnum(['create', 'update', 'delete']));
  const [serialNumber] = useQueryState('serialNumber', parseAsInteger.withDefault(0));
  const [startDate] = useQueryState('startDate', parseAsString.withDefault(''));
  const [endDate] = useQueryState('endDate', parseAsString.withDefault(''));

  // Sorting & Pagination
  const [sortField] = useQueryState('sortField', parseAsString.withDefault('timestamp'));
  const [sortOrder] = useQueryState('sortOrder', parseAsStringEnum(['asc', 'desc']).withDefault('desc'));
  //   const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1));
  //   const [limit] = useQueryState('limit', parseAsInteger.withDefault(10));

  // Local state
  const [data, setData] = useState<BlockLeadAssignationAudit[]>([]);
  const [pageCount, setPageCount] = useState(1);

  // Define columns inline
  const columns = React.useMemo<ColumnDef<BlockLeadAssignationAudit>[]>(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Seleccionar todo"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Seleccionar fila"
          />
        ),
        size: 32,
        enableSorting: false,
        enableHiding: false,
      },
      {
        id: 'timestamp',
        accessorKey: 'timestamp',
        header: ({ column }: { column: Column<BlockLeadAssignationAudit, unknown> }) => (
          <DataTableColumnHeader column={column} title="Fecha y Hora" />
        ),
        cell: ({ cell }) => {
          const date = new Date(cell.getValue<string>());
          return date.toLocaleString();
        },
        // meta: {
        //   label: 'Fecha y Hora',
        //   placeholder: 'Buscar fecha...',
        //   variant: 'text',
        //   icon: undefined,
        // },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'action',
        accessorKey: 'action',
        header: ({ column }: { column: Column<BlockLeadAssignationAudit, unknown> }) => (
          <DataTableColumnHeader column={column} title="Acción" />
        ),
        cell: ({ cell }) => {
          const actionVal = cell.getValue<'create' | 'update' | 'delete'>();
          const actionMap = {
            create: 'Crear',
            update: 'Actualizar',
            delete: 'Eliminar',
          };
          return (
            <Badge variant="outline" className="capitalize">
              {actionMap[actionVal]}
            </Badge>
          );
        },
        meta: {
          label: 'Acción',
          variant: 'select',
          options: [
            { label: 'Crear', value: 'create' },
            { label: 'Actualizar', value: 'update' },
            { label: 'Eliminar', value: 'delete' },
          ],
          icon: undefined,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'userName',
        accessorKey: 'userName',
        header: ({ column }: { column: Column<BlockLeadAssignationAudit, unknown> }) => (
          <DataTableColumnHeader column={column} title="Usuario" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Usuario',
          placeholder: 'Buscar usuario...',
          variant: 'text',
          icon: undefined,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'agentId',
        accessorKey: 'agent',
        header: ({ column }: { column: Column<BlockLeadAssignationAudit, unknown> }) => (
          <DataTableColumnHeader column={column} title="Agent ID" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Agente',
          variant: 'select',
          options: agentOptions,
          icon: undefined,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'agentName',
        accessorKey: 'agentName',
        header: ({ column }: { column: Column<BlockLeadAssignationAudit, unknown> }) => (
          <DataTableColumnHeader column={column} title="Agente" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'serialNumber',
        accessorKey: 'serialNumber',
        header: ({ column }: { column: Column<BlockLeadAssignationAudit, unknown> }) => (
          <DataTableColumnHeader column={column} title="Número de Serie" />
        ),
        cell: ({ cell }) => cell.getValue<number>(),
        meta: {
          label: 'Número de Serie',
          placeholder: 'Buscar número de serie...',
          variant: 'number',
          icon: undefined,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'description',
        accessorKey: 'description',
        header: ({ column }: { column: Column<BlockLeadAssignationAudit, unknown> }) => (
          <DataTableColumnHeader column={column} title="Descripción" />
        ),
        cell: ({ cell }) => (
          <div>
            <p className="text-wrap">{cell.getValue<string>()}</p>
          </div>
        ),
        enableColumnFilter: true,
        enableSorting: false,
      },
    ],
    [agentOptions]
  );

  // Fetch data when filters or pagination changes
  const {
    table,
    page: currentPage,
    perPage,
  } = useDataTable({
    data,
    columns,
    pageCount,
    initialState: {
      sorting: [{ id: sortField, desc: sortOrder === 'desc' }],
    },
    getRowId: (row: any) => row._id,
    shallow: false,
    clearOnDefault: true,
  });

  const fetchData = async () => {
    const params = new URLSearchParams();

    if (agentId) params.append('agentId', agentId);
    if (agentName) params.append('agentName', agentName);
    if (userName) params.append('userName', userName);
    if (action) params.append('action', action);
    if (serialNumber > 0) params.append('serialNumber', String(serialNumber));
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    params.append('page', String(currentPage));
    params.append('limit', String(perPage));
    params.append('sortField', sortField);
    params.append('sortOrder', sortOrder);

    try {
      const response = await fetch(`${URL_API}/leadAssignment/block-lead/audit-logs?${params}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      const result = await response.json();
      setData(result.data || []);
      setPageCount(result.pagination.totalPages || 1);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
    }
  };

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [
    user,
    agentId,
    userName,
    agentName,
    action,
    serialNumber,
    startDate,
    endDate,
    currentPage,
    perPage,
    refetchData,
  ]);

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border overflow-auto">
        <DataTable table={table}>
          <DataTableToolbar table={table} />
        </DataTable>
      </div>
    </div>
  );
}
