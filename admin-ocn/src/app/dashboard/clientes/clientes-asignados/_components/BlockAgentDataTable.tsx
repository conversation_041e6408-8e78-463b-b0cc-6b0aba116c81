'use client';

import { DataTable } from '@/components/data-table/data-table';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { DataTableToolbar } from '@/components/data-table/data-table-toolbar';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useDataTable } from '@/hooks/use-data-table';
import { ColumnDef, Column } from '@tanstack/react-table';
import { Text, Ellipsis, Loader2 } from 'lucide-react'; // Icons
import React, { useEffect, useState } from 'react';
import { Agent, AgentOption, URL_API } from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { parseAsString, useQueryState } from 'nuqs';
import { BlockAgentCreateModal } from './BlockAgentCreateModal';
import { BlockAgentEditModal } from './BlockAgentEditModal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { BlockAgentDeleteDialog } from './BlockAgentDeleteDialog';
import { BlockLeadAssignationAuditTable } from './BlockAgentAuditLogsTable';
import { toast } from 'sonner';

// Define BlockLeadAssignation interface
interface BlockLeadAssignation {
  _id: string;
  agent: {
    name: string;
    email: string;
  };
  blockedFrom: string;
  blockedUntil: string;
  reason: string;
  blockedBy: {
    name: string;
    email: string;
  };
  createdAt: string;
}

export function BlockAgentAssignmentDataTable() {
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  // URL state management
  const [agentNameOrEmail] = useQueryState('searchAgent', parseAsString.withDefault(''));
  const [blockedByNameOrEmail] = useQueryState('searchBlockedBy', parseAsString.withDefault(''));
  const [reason] = useQueryState('reason', parseAsString.withDefault(''));
  const [blockedFromGte] = useQueryState('blockedFrom[gte]', parseAsString.withDefault(''));
  const [blockedFromLte] = useQueryState('blockedFrom[lte]', parseAsString.withDefault(''));
  const [serialNumber] = useQueryState('serialNumber', parseAsString.withDefault(''));

  // Local state
  const [data, setData] = useState<BlockLeadAssignation[]>([]);
  const [pageCount, setPageCount] = useState(1);
  const [refetchData, setRefetchData] = useState(false);
  const [loading, setLoading] = useState(false);
  const [agentOptions, setAgentOptions] = useState<Array<AgentOption> | null>(null);

  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editRecord, setEditRecord] = useState<BlockLeadAssignation | null>(null);

  useEffect(() => {
    const getAgents = async () => {
      setLoading(true);
      const response = await fetch(`${URL_API}/leadAssignment/agents`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user?.accessToken}`,
        },
      });

      if (!response.ok) {
        toast.error('No hay agentes verificados disponibles.');
      } else {
        const res = await response.json();
        console.log('res', res);
        const agentsData = res.data;
        const agents: Array<AgentOption> = [];
        agentsData.map((agentData: Agent) => {
          agents.push({ value: agentData._id, label: agentData.name });
        });
        setAgentOptions(agents);
      }
      setLoading(false);
    };
    getAgents();
  }, []);

  // Define columns
  const columns = React.useMemo<ColumnDef<BlockLeadAssignation>[]>(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        size: 32,
        enableSorting: false,
        enableHiding: false,
      },
      {
        id: 'serialNumber',
        accessorKey: 'serialNumber',
        header: ({ column }: { column: Column<BlockLeadAssignation, unknown> }) => (
          <DataTableColumnHeader column={column} title="Número de serie" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Número de serie',
          placeholder: 'Buscar Número de Serie',
          variant: 'number',
          icon: Text,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'searchAgent',
        accessorKey: 'agent.name',
        header: ({ column }: { column: Column<BlockLeadAssignation, unknown> }) => (
          <DataTableColumnHeader column={column} title="Agente" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Agente',
          placeholder: 'Buscar por nombre o email...',
          variant: 'text',
          icon: Text,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'blockedFrom',
        accessorKey: 'blockedFrom',
        header: ({ column }: { column: Column<BlockLeadAssignation, unknown> }) => (
          <DataTableColumnHeader column={column} title="Bloqueado Desde" />
        ),
        cell: ({ cell }) => new Date(cell.getValue<string>()).toLocaleString(),
        enableSorting: false,
      },
      {
        id: 'blockedUntil',
        accessorKey: 'blockedUntil',
        header: ({ column }: { column: Column<BlockLeadAssignation, unknown> }) => (
          <DataTableColumnHeader column={column} title="Bloqueado Hasta" />
        ),
        cell: ({ cell }) => {
          const dateVal = new Date(cell.getValue<string>());
          const currentDate = new Date();
          if (dateVal > currentDate) {
            return (
              <Badge variant="outline" className="capitalize bg-green-300">
                {new Date(cell.getValue<string>()).toLocaleString()}
              </Badge>
            );
          } else {
            return <>{new Date(cell.getValue<string>()).toLocaleString()}</>;
          }
        },
        enableSorting: false,
      },
      {
        id: 'reason',
        accessorKey: 'reason',
        header: ({ column }: { column: Column<BlockLeadAssignation, unknown> }) => (
          <DataTableColumnHeader column={column} title="Motivo" />
        ),
        cell: ({ cell }) => {
          const reasonVal = cell.getValue<string>();
          return (
            <Badge variant="outline" className="capitalize">
              {reasonVal}
            </Badge>
          );
        },
        meta: {
          label: 'Motivo',
          variant: 'select',
          options: [
            { label: 'Vacaciones', value: 'Vacaciones' },
            { label: 'Baja por enfermedad', value: 'Baja por enfermedad' },
            { label: 'Ausente', value: 'Ausente' },
            { label: 'Permiso de emergencia', value: 'Permiso de emergencia' },
            { label: 'Asignación Especial', value: 'Asignación Especial' },
            { label: 'Rendimiento', value: 'Rendimiento' },
            { label: 'Terminación', value: 'Terminación' },
          ],
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'searchBlockedBy',
        accessorKey: 'blockedBy.name',
        header: ({ column }: { column: Column<BlockLeadAssignation, unknown> }) => (
          <DataTableColumnHeader column={column} title="Bloqueado Por" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Bloqueado Por',
          placeholder: 'Buscar bloqueado por nombre o correo electrónico',
          variant: 'text',
          icon: Text,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: ({ column }: { column: Column<BlockLeadAssignation, unknown> }) => (
          <DataTableColumnHeader column={column} title="Creado En" />
        ),
        cell: ({ cell }) => new Date(cell.getValue<string>()).toLocaleString(),
        enableSorting: false,
      },
      {
        id: 'actions',
        cell: function Cell({ row }) {
          console.log('row', row);
          const handleDeleteClick = (id: string) => {
            setSelectedId(id);
            setDeleteDialogOpen(true);
          };
          return (
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button
                  aria-label="Open menu"
                  variant="ghost"
                  className="flex size-8 p-0 data-[state=open]:bg-muted"
                >
                  <Ellipsis className="size-4" aria-hidden="true" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuItem
                  onSelect={(e) => {
                    e.preventDefault();
                    setEditRecord(row.original);
                    setEditDialogOpen(true);
                  }}
                >
                  Editar
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onSelect={(e) => {
                    e.preventDefault();
                    handleDeleteClick(row.original._id);
                  }}
                  className="text-destructive focus:text-destructive"
                >
                  Eliminar
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
        size: 40,
      },
    ],
    []
  );

  const { table, page, perPage } = useDataTable({
    data,
    columns,
    pageCount,
    initialState: {
      sorting: [{ id: 'createdAt', desc: true }],
      columnPinning: { right: ['actions'] },
    },
    getRowId: (row) => row._id?.toString() || '',
    shallow: false,
    clearOnDefault: true,
  });

  useEffect(() => {
    const fetchData = async (pageIndex: number, pageSize: number) => {
      try {
        const params = new URLSearchParams();

        if (agentNameOrEmail) params.append('searchAgent', agentNameOrEmail);
        if (blockedByNameOrEmail) params.append('searchBlockedBy', blockedByNameOrEmail);
        if (reason) params.append('reason', reason);
        if (blockedFromGte) params.append('blockedFrom[gte]', blockedFromGte);
        if (blockedFromLte) params.append('blockedFrom[lte]', blockedFromLte);
        if (serialNumber) params.append('serialNumber', serialNumber);

        params.append('page', pageIndex.toString());
        params.append('limit', pageSize.toString());

        const response = await fetch(`${URL_API}/leadAssignment/block-leads-list?${params}`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });

        const result = await response.json();

        setData(result.data);
        setPageCount(result.pagination.totalPages);
      } catch (error) {
        console.error('Error fetching block lead assignments:', error);
      }
    };

    if (user) {
      fetchData(page, perPage);
    }
  }, [
    user,
    agentNameOrEmail,
    blockedByNameOrEmail,
    reason,
    blockedFromGte,
    blockedFromLte,
    serialNumber,
    page,
    perPage,
    refetchData,
  ]);

  return (
    <div className="space-y-4 relative">
      {loading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
          <div className="flex flex-col items-center justify-center text-white">
            <Loader2 className="size-12 animate-spin" />
            <span className="mt-2 text-sm">Cargando...</span>
          </div>
        </div>
      )}
      <BlockAgentCreateModal
        refetchData={refetchData}
        setRefetchData={setRefetchData}
        agentOptions={agentOptions}
      ></BlockAgentCreateModal>
      <BlockAgentDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        blockId={selectedId}
        onSuccess={() => setRefetchData((prev) => !prev)} // Refresh table
      />
      <BlockAgentEditModal
        refetchData={refetchData}
        setRefetchData={setRefetchData}
        editRecord={editRecord}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
      />
      {/* Table */}
      <div className="rounded-md border">
        <DataTable table={table}>
          <DataTableToolbar table={table} />
        </DataTable>
      </div>
      <div className="py-8">
        <div className="crounded-md border">
          <strong>Audit Logs</strong>
          {!loading && (
            <BlockLeadAssignationAuditTable refetchData={refetchData} agentOptions={agentOptions} />
          )}
        </div>
      </div>
    </div>
  );
}
