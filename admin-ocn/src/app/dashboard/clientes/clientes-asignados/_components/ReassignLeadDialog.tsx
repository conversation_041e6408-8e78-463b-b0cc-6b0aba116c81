'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';
import { MultiSelect } from '../../../../../components/ui/multi-select';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Agent, AgentOption, ReassignLeadsReason } from '@/constants';
import { toast } from 'sonner';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';

export function ReassignLeadDialog({ leadsList, btnDisable, refetchData, setRefetchData }: any) {
  const [open, setOpen] = useState(false);
  const [agentIds, setAgentIds] = useState<string[]>([]);
  const [reason, setReason] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [blockedAgents, setBlockedAgents] = useState<Array<any> | null>(null);
  const [agentOptions, setAgentOptions] = useState<Array<AgentOption>>([]);
  const [showMultipleAgentsMsg, setShowMultipleAgentsMsg] = useState(false);

  const { data: session } = useSession();
  const user = session?.user;

  useEffect(() => {
    if (agentIds && agentIds.length > 1) {
      setShowMultipleAgentsMsg(true);
    } else {
      setShowMultipleAgentsMsg(false);
    }
  }, [agentIds]);

  useEffect(() => {
    const getAgents = async () => {
      setLoading(true);
      const response = await fetch(`${URL_API}/leadAssignment/available-agents`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user?.accessToken}`,
        },
      });

      if (!response.ok) {
        toast.error('No hay agentes verificados disponibles.');
      } else {
        const res = await response.json();
        console.log('res', res);
        const agentsData = res.data;
        const agents: Array<AgentOption> = [];
        agentsData.map((agentData: Agent) => {
          agents.push({ value: agentData._id, label: agentData.name });
        });
        setAgentOptions(agents);
      }
      setLoading(false);
    };
    getAgents();
  }, []);

  const handleSubmit = async () => {
    if (!reason || agentIds.length === 0) {
      toast.error('All fields are required');
      return;
    }

    setLoading(true);
    setBlockedAgents([]); // Reset previous blocked agents

    try {
      const response = await fetch(`${URL_API}/leadAssignment/reassign-lead`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user?.accessToken}`,
        },
        body: JSON.stringify({
          userId: user?.id,
          agentIds,
          requestIds: Object.keys(leadsList),
          reason,
        }),
      });

      if (!response.ok) {
        if (response.status === 409) {
          const data = await response.json();
          setBlockedAgents(data);
        } else {
          toast.error('Error al bloquear agentes.');
        }
      } else {
        toast.success('La asignación principal se ha reasignado con éxito');
        setOpen(false);
        resetForm();
      }
    } catch (error) {
      console.error(error);
      toast.error(JSON.stringify(error));
    } finally {
      setLoading(false);
      setRefetchData(!refetchData);
    }
  };

  const resetForm = () => {
    setAgentIds([]);
    setReason('');
    setBlockedAgents([]);
  };

  const handleClose = () => {
    setOpen(false);
    resetForm();
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        setOpen(newOpen);
      }}
    >
      <DialogTrigger asChild>
        <Button disabled={btnDisable}>Reasignar</Button>
      </DialogTrigger>
      <DialogContent className="max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Reasignar clientes potenciales de ventas</DialogTitle>
          <DialogDescription>
            Seleccionar agente(s) para reasignar clientes potenciales de ventas.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Agent Multi-select */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="agents" className="text-right font-medium">
              Agentes
            </label>
            <MultiSelect
              options={agentOptions}
              onValueChange={setAgentIds}
              defaultValue={agentIds}
              placeholder="Seleccionar agentes"
              variant="inverted"
              maxCount={5}
              className="col-span-3"
            />
          </div>

          {/* Reason Dropdown */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="reason" className="text-right font-medium">
              Razón
            </label>
            <Select onValueChange={setReason} value={reason}>
              <SelectTrigger id="reason" className="col-span-3">
                <SelectValue placeholder="Selecciona una razón" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(ReassignLeadsReason).map((r) => (
                  <SelectItem key={r} value={r}>
                    {r}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {showMultipleAgentsMsg && (
          <div className="bg-yellow-100 p-4 rounded-md border border-yellow-300 text-sm">
            <p>
              Por favor, tenga en cuenta que los prospectos de ventas se reasignarán a los agentes
              seleccionados de forma rotatoria.
            </p>
          </div>
        )}

        {/* Show blocked agents if exists */}
        {blockedAgents && blockedAgents.length > 0 && (
          <div className="bg-red-100 p-4 rounded-md border border-red-300 text-sm">
            <ul className="space-y-2">
              {blockedAgents.map((agentBlock: any) => (
                <li key={agentBlock.agentId}>
                  <ul className="ml-4 mt-1 space-y-1">
                    {agentBlock.overlappingBlocks.map((block: any) => (
                      <li key={block.blockId} className="text-red-700">
                        <span>{`${agentBlock.agentName} ya está bloqueado desde el ${new Date(
                          block.blockedFrom
                        ).toLocaleString()} hasta el ${new Date(
                          block.blockedUntil
                        ).toLocaleString()}. con el número de serie ${block.serialNumber}`}</span>
                      </li>
                    ))}
                  </ul>
                </li>
              ))}
            </ul>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Guardando...' : 'Confirmar'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
