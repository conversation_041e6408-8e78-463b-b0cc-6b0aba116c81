'use client';
import { Table, TableContainer, Tbody, Td, Th, Thead, Tr } from '@chakra-ui/react';
import { usePermissionSets } from './PermisosProvider';
import { roleOptions, areaOptions, Capabilities, Sections, Subsections } from '@/constants';
import Link from 'next/link';
import Swal from 'sweetalert2';
import axios from 'axios';
import { useSession } from 'next-auth/react';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import { MyUser } from '@/actions/getCurrentUser';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';

export default function PermisoTable() {
  const { permissionSets, setPermissionSets } = usePermissionSets();
  const ability = usePermissions();
  const { data: session } = useSession();
  const url = useCurrentUrl();
  const user = session?.user as unknown as MyUser;

  if (!user) return null;

  const canEdit = canPerform(ability, Capabilities.Edit, Sections.UserManagement, Subsections.Permissions);
  const canDelete = canPerform(
    ability,
    Capabilities.Delete,
    Sections.UserManagement,
    Subsections.Permissions
  );

  const deletePermissionSetAPI = async (_id: string) => {
    const response = await axios.delete(`${url}/permissionSet/${_id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    return response;
  };

  const deletePermissionSet = async (_id: string) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'This will permanently delete the permission set.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it',
      cancelButtonText: 'Cancel',
    });

    if (result.isConfirmed) {
      try {
        await deletePermissionSetAPI(_id);
        Swal.fire('Deleted!', 'The permission set has been deleted.', 'success').then(() => {
          setPermissionSets((prev) => prev.filter((p) => p._id !== _id));
        });
      } catch (error: any) {
        if (error.response && error.response.status === 400) {
          Swal.fire(
            'No se puede eliminar',
            error.response.data.message ||
              'No se puede eliminar este rol de acceso porque hay usuarios asignados a él actualmente.',
            'warning'
          );
        } else {
          Swal.fire('Error', 'Failed to delete the permission set.', 'error');
        }
      }
    }
  };

  return (
    <>
      <p className="text-sm text-gray-600 mb-2">
        Mostrando <strong>{permissionSets.length}</strong> permiso{permissionSets.length === 1 ? '' : 's'}
      </p>
      <TableContainer bgColor="#FFFFFF" px={6} pt={3} pb={6} borderRadius="5px">
        <Table variant="striped" colorScheme="gray">
          <Thead>
            <Tr>
              <Th px="4px" py="6px">
                Nombre
              </Th>
              <Th px="4px" py="6px">
                Área
              </Th>
              <Th px="4px" py="6px">
                Rol
              </Th>
              <Th px="4px" py="6px">
                Acciones
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {permissionSets &&
              permissionSets.map((permissionSet) => (
                <Tr key={permissionSet._id} h="30px" color="#262D33">
                  <Td px="4px" py="6px">
                    {permissionSet.name}
                  </Td>
                  <Td px="4px" py="6px">
                    {areaOptions.find((option) => option.value === permissionSet.area)?.label}
                  </Td>
                  <Td px="4px" py="6px">
                    {roleOptions.find((option) => option.value === permissionSet.role)?.label}
                  </Td>
                  <Td px="4px" py="6px">
                    <div className="flex gap-2 items-center self-end">
                      {canEdit && (
                        <Link href={`/dashboard/permisos/${permissionSet._id}`}>
                          <button className="border-[2px] border-[#5800F7] text-[#5800F7] rounded py-[7px] px-[5px] font-bold ">
                            Editar
                          </button>
                        </Link>
                      )}
                      {!(user.area === permissionSet.area && user.role === permissionSet.role) &&
                        canDelete && (
                          <button
                            className="border-[2px] border-[#E14942] text-[#E14942] rounded py-[7px] px-[5px] font-bold"
                            onClick={() => deletePermissionSet(permissionSet._id)}
                          >
                            Eliminar
                          </button>
                        )}
                    </div>
                  </Td>
                </Tr>
              ))}
          </Tbody>
        </Table>
      </TableContainer>
    </>
  );
}
