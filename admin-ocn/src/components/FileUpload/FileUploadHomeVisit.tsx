import { forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import { MdOutlineUploadFile } from 'react-icons/md';
import { useDropzone } from 'react-dropzone';
import { useSession } from 'next-auth/react';
import { URL_API } from '@/constants';
import { FileUploadLogo } from '@/svgsComponents/FileUploadLogo';
import { ShowLogo } from '@/svgsComponents/ShowLogo';
import { TrashLogo } from '@/svgsComponents/TrashLogo';
import { MyUser } from '@/actions/getCurrentUser';
import { MediaType } from '@/app/dashboard/clientes/solicitudes/enums';
import { useToast } from '@chakra-ui/react';
import { translations } from '@/app/dashboard/clientes/solicitudes/[id]/home-visit/_components/translations';
const allowedFileFormats =
  'image/png,image/jpeg,image/jpg,application/pdf,image/gif,image/tiff,image/bmp,image/webp';

interface IHomeVisit {
  residentOwnershipStatus: string;
  hasGarage: string;
  comments: string;
  images: Array<string>;
  status: string;
  responsible: string;
  visitDate: string;
  media: Array<IUploadedFile>;
  visitTime: string;
  houseInformation: {
    ownProperty: string;
    nameOfOwner: string;
    ownerRelative: string;
    ownerRelativeRelation: string;
    ownerPhone: string;
    typeOfHousing: string;
    noOfBedrooms: number;
    livingRoom: string;
    dinningRoom: string;
    kitchen: string;
    television: string;
    audioSystem: string;
    stove: string;
    refrigerator: string;
    washingMachine: string;
  };
  proofOfPropertyOwnership: Array<string>[];
  visitorEmailAddress: string;
  doesProofOfAddressMatchLocation: string;
  characteristicsOfGarage: string;
  behaviourOfCustomerDuringCall: string;
  homeVisitStepsStatus: {
    personal: string;
    contact: string;
    address: string;
    family: string;
    property: string;
    automobile: string;
    debt: string;
    references: string;
    outcome: string;
  };
}

interface IUploadedFile {
  id: string;
  fileName: string;
  path: string;
  url: string;
  createdAt: string;
  updatedAt: string;
  file?: File;
}

export const uploadFile = async (files: Array<File>) => {
  const formData = new FormData();
  files.forEach((file: File) => {
    formData.append('file', file);
  });
  formData.append('mediaType', MediaType.home_visit_evidence);

  try {
    const response = await fetch(`${URL_API}/media/upload`, {
      method: 'POST',
      body: formData,
    });
    const data = await response.json();
    return data.data as Array<IUploadedFile>;
  } catch (error) {
    throw new Error('Error occured while uploading file.');
  }
};

interface IFilesSection {
  form: any;
  requestId: string;
  uploadText: string;
  fieldToLookForImages: string;
  formFieldName: string;
  updateImages: () => Promise<void>;
  showImageCounter?: boolean;
  totalNoOfImages?: number;
}

export const FilesSection = (props: IFilesSection) => {
  const {
    form,
    requestId,
    uploadText,
    fieldToLookForImages,
    formFieldName,
    updateImages,
    showImageCounter,
    totalNoOfImages,
  } = props;

  const [uploadedFiles, setUploadedFiles] = useState<Array<IUploadedFile>>([]);
  const [isUploading, setIsUploading] = useState(false);
  const toast = useToast();

  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  const progressRef = useRef<HTMLProgressElement>(null);

  const onDrop = useCallback(
    async (newFiles: Array<File>) => {
      setIsUploading(true);
      const allowedFileTypes = new Set(allowedFileFormats.split(','));
      const invalidFiles = newFiles.filter((file) => !allowedFileTypes.has(file.type));
      if (invalidFiles.length > 0) {
        toast({
          title: 'Error',
          description: translations.es.InvalidFileFormat,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        setIsUploading(false);
        return;
      }
      try {
        const uploadedFilesMetadata = await uploadFile(newFiles);
        form.setValue(
          formFieldName,
          [...uploadedFiles, ...uploadedFilesMetadata].map((fileMetadata) => fileMetadata.id)
        );
        await updateImages();
        if (progressRef.current) {
          progressRef.current.value = 100;
        }
        setUploadedFiles([...uploadedFiles, ...uploadedFilesMetadata]);
      } catch (error) {
        toast({
          title: 'Error',
          description: translations.es.FileUploadErrorMsg,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      } finally {
        if (progressRef.current) {
          progressRef.current.value = 0;
        }
        setIsUploading(false);
      }
    },
    [form, formFieldName, uploadedFiles, updateImages, toast]
  );

  const { getRootProps, getInputProps } = useDropzone({ onDrop });

  useEffect(() => {
    if (!user) {
      return;
    }
    async function fetchHomeVisit() {
      const res = await fetch(`${URL_API}/admission/requests/${requestId}/home-visit`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      const data = await res.json();
      const homeVisit = data.data as IHomeVisit;
      const fields = homeVisit[fieldToLookForImages as keyof IHomeVisit] as Array<string>;
      const mediaFiles = fields
        ? fields.reduce((acc: Array<IUploadedFile>, item: string) => {
            const mediaMetadata = homeVisit.media.find((media) => media.id === item);
            acc.push(mediaMetadata!);
            return acc;
          }, [])
        : [];
      setUploadedFiles([...mediaFiles]);
    }
    fetchHomeVisit();
  }, [user, requestId, setUploadedFiles, fieldToLookForImages]);

  const areAllFilesNotUploaded = uploadedFiles.length < (totalNoOfImages ?? 0);

  return (
    <div>
      {showImageCounter ? (
        <div className="flex gap-2 py-2">
          {areAllFilesNotUploaded ? (
            <div className={`rounded-md px-2 bg-error-text`}>
              <span className={`text-sm`}>{translations.es.MinImgesErrorMessage}</span>
            </div>
          ) : null}
        </div>
      ) : null}

      <div
        {...getRootProps()}
        className="cursor-pointer border border-dotted rounded-sm border-primaryPaleBlueGray flex flex-col"
      >
        <div className="flex flex-col items-center justify-center py-20">
          <MdOutlineUploadFile size={40} className=" text-primaryVibrantPurpleGradient" />
          {<p className="text-primaryPrussianBlue">{uploadText}</p>}
          <input
            {...getInputProps()}
            accept={allowedFileFormats}
            className="hidden"
            id="file-upload"
            type="file"
          />
        </div>
        {isUploading ? (
          <div className="py-8 px-2">
            <ProgressBar ref={progressRef} />
          </div>
        ) : null}
      </div>

      <div className="flex flex-wrap py-2 gap-2">
        {uploadedFiles.map((fileMetadata, index) => {
          return (
            <FileUploaded
              key={fileMetadata.fileName + index}
              fileName={fileMetadata.fileName.slice(0, 10)}
              fileSize={
                fileMetadata?.file?.size ? (fileMetadata.file.size / (1024 * 1024)).toFixed(2) + 'MB' : ''
              }
              onShow={() => {
                window.open(fileMetadata.url, '_blank');
              }}
              onDelete={async () => {
                try {
                  const updatedFiles = uploadedFiles.filter(
                    (uploadedFile) => uploadedFile.id !== fileMetadata.id
                  );
                  form.setValue(
                    formFieldName,
                    updatedFiles.map((file) => file.id)
                  );
                  const homeImages = form.getValues('homeImages');
                  if (formFieldName === 'propertyPictures' && homeImages) {
                    Object.keys(homeImages).forEach((key) => {
                      homeImages[key] = homeImages[key].filter((image: string) => image !== fileMetadata.id);
                    });
                    form.setValue('homeImages', homeImages);
                  }
                  await updateImages();
                  setUploadedFiles(updatedFiles);
                } catch (error) {
                  toast({
                    title: 'Error',
                    description: translations.es.FileDeleteErrorMsg,
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                  });
                  form.setValue(
                    formFieldName,
                    uploadedFiles.map((file) => file.id)
                  );
                }
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

interface IFileUploaded {
  fileName: string;
  fileSize: string;
  onShow: () => void;
  onDelete: () => void;
}

const FileUploaded = (props: IFileUploaded) => {
  const { fileName, fileSize, onShow, onDelete } = props;

  return (
    <div className="w-2/6 flex p-2 justify-between items-center border border-primarySoftBlue rounded-sm">
      <div className="flex items-center">
        <FileUploadLogo />
        <div className="px-2">
          <p className=" text-primaryNavyBlue">{fileName}</p>
          <p className="text-primarySlateBlueGray">{fileSize}</p>
        </div>
      </div>
      <div className="flex gap-2">
        <ShowLogo onClick={onShow} />
        <TrashLogo onClick={onDelete} />
      </div>
    </div>
  );
};

const Progress = (props: any, progressRef: any) => {
  return (
    <div className="relative w-full">
      <progress
        ref={progressRef}
        value={40}
        max="100"
        className="w-full h-2 appearance-none rounded-md"
      ></progress>
      <style jsx>{`
        progress::-webkit-progress-bar {
          background-color: #e5e7eb; /* Light gray for the background bar */
          border-radius: 12px;
        }
        progress::-webkit-progress-value {
          background: linear-gradient(to right, #9d4edd, #6610f2); /* Purple gradient */
        }
        progress::-moz-progress-bar {
          background: linear-gradient(to right, #9d4edd, #6610f2); /* Purple gradient */
        }
      `}</style>
    </div>
  );
};

const ProgressBar = forwardRef(Progress);
