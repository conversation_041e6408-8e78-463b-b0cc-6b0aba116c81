/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-use-before-define */
import {
  Button,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import React, { useEffect, useMemo, useState } from 'react';
import { Form as FormikForm, Formik, FormikValues, useFormikContext } from 'formik';
import * as Yup from 'yup';
import CustomInput from '../Inputs/CustomInput';
import SelectInput from '../Inputs/SelectInput';
import {
  addressDataSchema,
  associateDataSchema,
  cityContractSchema,
  facturationAddressSchema,
  facturationSchema,
  vehicleDataSchema,
  appointmentSchema,
} from '@/validatorSchemas/contractSchema';
import InputPhone from '../Inputs/InputPhone';
import RadioOptions from '../Inputs/RadioOptions';
import InputDate from '../Inputs/InputDate';
import { useContract } from '@/app/dashboard/flotilla/components/Detail/GenerateContract';
import { usePathname } from 'next/navigation';
import Spinner from '../Loading/Spinner';
import { ApprovalTypes, PAYMENTS_API_URL, PAYMENT_API_SECRET, URL_API, citiesDependingState, citiesSelect, ferderalEntitiesSelect, statesSelect } from '@/constants';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import axios from 'axios';
import { Product } from '@/app/dashboard/pagos/types';
import { useVehicleDetailData } from '@/app/dashboard/flotilla/components/Providers/VehicleDetailDataProvider';

import { ShowCalculatedWeeks } from './ShowCalculatedWeeks';
import { Checkbox } from '../ui/checkbox';
import { Combobox, ComboboxOption } from '../ui/combobox';

enum STEPS {
  CITY = 1,
  DATA = 2,
  ADDRESS = 3,
  BILLINGDATA = 4,
  VEHICLEDATA = 5,
  APPOINTMENT = 6,
}


enum ExtendApproval {
  APPROVED = 'approved',
}

const approvalOptionsMap: Record<ApprovalTypes & ExtendApproval, { label: string; value: ApprovalTypes }> = {
  [ExtendApproval.APPROVED]: {
    label: 'Aprobado (sin enganche ni deposito)',
    value: ExtendApproval.APPROVED,
  },
  [ApprovalTypes.PREAPPROVED]: {
    label: 'Pre Aprobado (sin enganche ni deposito)',
    value: ApprovalTypes.PREAPPROVED,
  },
  [ApprovalTypes.PRE_OWNED]: {
    label: 'Seminuevo',
    value: ApprovalTypes.PRE_OWNED,
  },
  // [ApprovalTypes.MG3]: {
  //   label: 'MG3',
  //   value: ApprovalTypes.MG3,
  // },
  [ApprovalTypes.DOWN_PAYMENT]: {
    label: 'Aprobado con Enganche',
    value: ApprovalTypes.DOWN_PAYMENT,
  },
  [ApprovalTypes.DEPOSIT]: {
    label: 'Aprobado con Deposito',
    value: ApprovalTypes.DEPOSIT,
  },
}

// get an array of value, label for the select input, from ApprovalTypes that is an enum

const approvalOptions = Object.keys(approvalOptionsMap).map((key) => {
  return approvalOptionsMap[key as ApprovalTypes & ExtendApproval];
}) as { label: string; value: string }[];


const validationSchemas = {
  [STEPS.CITY]: cityContractSchema,
  [STEPS.DATA]: associateDataSchema,
  [STEPS.ADDRESS]: addressDataSchema,
  [STEPS.BILLINGDATA]: facturationSchema,
  [STEPS.VEHICLEDATA]: vehicleDataSchema,
  [STEPS.APPOINTMENT]: appointmentSchema,
};

interface CustomModalProps {
  header: string;
  openButtonText: string;
  confirmButtonText?: string;
  validatorSchema?: Yup.ObjectSchema<any>;
  initialValues: FormikValues;
  onSubmit: (values: FormikValues) => Promise<any>;
  onCloseModal?: () => any;
  bodyContent?: React.ReactElement;
  onChangeStep?: (values: FormikValues) => void; // Esto sirve para actualizar el estado de los valores con el paso actual
}
const requiredFields: Record<string, { required: boolean; message: string }> = {
  policyNumber: {
    required: true,
    message: 'Número de poliza',
  },
  plates: {
    required: true,
    message: 'Placas',
  },
  circulationCardNumber: {
    required: true,
    message: 'Número de tarjeta de circulación',
  },
};


function validateCreateContract(data: any, toast: any) {

  const missingFields = Object.keys(requiredFields).filter((key) => !data[key]);

  if (missingFields.length) {
    const msg = `Faltan los siguientes campos:\n${missingFields
      .map((key, i) => `${i + 1}. ${requiredFields[key].message}`)
      .join(' ')}`;
    toast({
      title: 'No se pudo generar el contrato',
      description: msg || '',
      status: 'info',
      duration: 3000,
      isClosable: true,
      position: 'top',
    });
    return process.env.NEXT_PUBLIC_IS_LOCAL === 'true'; // if the env var exists return true so the contract can be generated without validation
  }

  if (!data.avalData.name || !data.avalData.address || !data.avalData.email) {

    const missingAvalFields = Object.keys(data.avalData).filter((key) => !data.avalData[key]);

    const msg = `Faltan los siguientes campos del aval:\n${missingAvalFields
      .map((key, i) => `${i + 1}. ${key}`)
      .join(' ')}`;

    toast({
      title: 'No se pudo generar el contrato',
      description: msg || '',
      status: 'info',
      duration: 3000,
      isClosable: true,
      position: 'top',
    });

  }

  if (data.contacts.length < 2) {
    toast({
      title: 'No se pudo generar el contrato',
      description: 'Faltan referencias de contacto, puedes agregarlas en la edición del conductor',
      status: 'info',
      duration: 3000,
      isClosable: true,
      position: 'top',
    });

    return process.env.NEXT_PUBLIC_IS_LOCAL === 'true'; // if the env bar exists return true so the contract can be generated without validation

  }

  return true;

}

export default function ContractModal({
  header,
  openButtonText,
  initialValues,
  onSubmit,
  onChangeStep,
}: CustomModalProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [step, setStep] = useState(STEPS.VEHICLEDATA);
  const [selectedAddress, setSelectedAddress] = useState('1');
  const [admissionRequest, setAdmissionRequest] = useState<any>(null);
  const globalData = useVehicleDetailData();
  const { user } = useCurrentUser();
  useEffect(() => {
    async function getAdmissionRequest() {
      try {


        const { data } = await axios.post(`${URL_API}/admission/requests/find`, {
          email: globalData.associateData.email,
          curp: globalData.associateData.curp,
          rfc: globalData.associateData.rfc,
          associateId: globalData.associateData._id,
        }, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });
        console.log('data', data);
        setAdmissionRequest(data);
      } catch (error: any) {
        console.log('error response: ', error.response?.data);
      }
    }
    getAdmissionRequest();

  }, []);

  const onBack = () => {
    if (step === STEPS.VEHICLEDATA) return;
    setStep((value) => value - 1);
  };
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const { form } = useContract();
  const onNext = () => {
    setStep((value) => value + 1);
  };

  const location = usePathname();

  const carId = location.split('/')[4];

  const handleSubmit = async (data: any, setFieldError: any) => {
    let isValid = true;
    const dataValidator = {
      ...data,
    };
    if (validationSchemas.hasOwnProperty(step)) {
      try {
        await validationSchemas[step].validate(dataValidator, { abortEarly: false });
        if (step === STEPS.BILLINGDATA && selectedAddress === '2') {
          await facturationAddressSchema.validate(dataValidator, { abortEarly: false });
        }
      } catch (validationError: any) {
        isValid = false;
        validationError.inner.forEach((error: any) => {
          setFieldError(error.path, error.message);
        });
      }
    }

    if (step === STEPS.VEHICLEDATA) {
      // if the sum of the products is 0, then the contract can't be generated
      const weeklyRent = data.weeklyRent || form.weeklyRent || 0;
      if (weeklyRent === 0 || weeklyRent < 2500) { // This validation is for mexico sum of products on subscription
        toast({
          title: 'No se puede seguir con el siguiente paso',
          description: 'No se encontraron productos para el contrato',
          status: 'info',
          duration: 3000,
          isClosable: true,
          position: 'top',
        });
        return false;
      }
    }

    if (isValid) {
      if (step !== STEPS.APPOINTMENT) {
        return onNext();
      }
      try {
        if (onChangeStep) {
          onChangeStep(data);
        }
        setIsLoading(true);
        const canContinue = validateCreateContract(data, toast);
        if (!canContinue) {
          setIsLoading(false);
          return false
        }
        // check if invoiceFile is not empty, if is not empty, remove it from data
        console.log('data', data);
        if (data.invoiceFile) {
          // delete data.invoiceFile;
          data.invoiceFile = undefined;
        }
        await onSubmit(data);

        onClose();
        toast({
          title: 'Contrato generado exitosamente',
          status: 'success',
          duration: 3000,
          isClosable: true,
          position: 'top',
        });
        setTimeout(() => {
          window.location.reload();
        }, 3100);
        localStorage.removeItem('contractForm-' + carId);
      } catch (error: any) {
        console.log('ERROR', error);
        toast({
          title: 'Algo salio mal',
          description: error.message || '',
          status: 'error',
          duration: 3000,
          isClosable: true,
          position: 'top',
        });
      } finally {
        setIsLoading(false);
      }
    }
    return null;
  };

  const actionLabel = useMemo(() => {
    if (step === STEPS.APPOINTMENT) {
      return 'Generar contrato';
    }
    return 'Siguiente';
  }, [step]);

  let bodyContent = (
    <>
      <p className="font-bold">Localización del contrato</p>
      <SelectInput
        label="Ciudad"
        options={citiesDependingState[initialValues.state?.value || initialValues.state] || citiesSelect}
        name="city"
      />
    </>
  );

  if (step === STEPS.DATA) {
    bodyContent = (
      <>
        <p className="font-bold">Datos del cliente</p>
        <div className="w-full grid grid-cols-2 gap-x-4 gap-y-3 relative">
          <CustomInput label="Nombres" name="firstName" type="text" />
          <CustomInput label="Apellidos" name="lastName" type="text" />
          <InputPhone label="Celular" name="phone" placeholder="" />
          <InputPhone label="Teléfono fijo" name="tel" placeholder="" />
          <CustomInput label="Email" name="email" type="email" />
          <CustomInput label="No. de contrato" name="contractNumber" type="text" disabled />
          {initialValues.extensionCarNumber ? (
            <div className="absolute bottom-[8px] right-[180px]"> - {initialValues.extensionCarNumber}</div>
          ) : null}
        </div>
      </>
    );
  }

  if (step === STEPS.ADDRESS) {
    bodyContent = (
      <>
        <p className="font-bold">Dirección del cliente</p>
        <CustomInput label="Calle" name="street" type="text" />
        <div className="w-full grid grid-cols-2 gap-2">
          <CustomInput label="No. exterior" name="exterior" type="text" />
          <CustomInput label="No. interior (opcional)" name="interior" type="text" />
          <CustomInput label="Colonia" name="colony" type="text" />
          <CustomInput label="Codigo Postal" name="postalCode" type="text" />
          <CustomInput label="Alcaldia" name="delegation" type="text" />
          <SelectInput
            label="Entidad Federativa"
            name="state"
            options={ferderalEntitiesSelect}
          />
        </div>
      </>
    );
  }

  if (step === STEPS.BILLINGDATA) {
    bodyContent = (
      <>
        <p className="font-bold">Datos de Facturación</p>
        <CustomInput label="RFC" name="rfc" type="text" />
        <div className="flex flex-col gap-3">
          <p>¿La dirección de facturación es diferente a la dirección personal?</p>
          <RadioOptions
            name="sameAddress"
            options={[
              {
                label:
                  'No, usar la misma dirección (se usaran los mismos datos registrados en el paso anterior)',
                value: '1',
                name: 'some',
              },
              { label: 'Si, agregar una dirección nueva', value: '2', name: 'some2' },
            ]}
            selectedAddress={selectedAddress}
            // setSelectedAddress={setSelectedAddress}
            onChange={(value) => {
              setSelectedAddress(value);
            }}
          />
          {selectedAddress === '2' ? (
            <>
              <CustomInput label="Calle" name="streetDos" type="text" />
              <div className="w-full grid grid-cols-2 gap-2">
                <CustomInput label="No. exterior" name="exteriorDos" type="text" />
                <CustomInput label="No. interior (opcional)" name="interiorDos" type="text" />
                <CustomInput label="Colonia" name="colonyDos" type="text" />
                <CustomInput label="Codigo Postal" name="zipDos" type="text" />
                <CustomInput label="Alcaldia" name="townHallDos" type="text" />
                <SelectInput label="Entidad Federativa" name="stateDos" options={statesSelect} />
              </div>
            </>
          ) : null}
        </div>
      </>
    );
  }
  // const [paymentsDone, setPaymentsDone] = useState(0);
  console.log('form', form);
  if (step === STEPS.VEHICLEDATA) {
    // bodyContent = <VehicleDataStep form={form} setForm={setForm} />;
    bodyContent = (
      <TypeOfContract
        form={form}
        initialValues={{ ...initialValues, phone: initialValues.phone.replace('+52', '') }}
        admissionRequest={admissionRequest}
        setAdmissionRequest={setAdmissionRequest}
      />
    );
  }
  interface RadioOption {
    value: string;
    label: string;
  }
  const [isSameDate, setIsSameDate] = useState<RadioOption>({ value: 'Si', label: 'Sí' });

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsSameDate({ value: event.target.value, label: event.target.value === 'Si' ? 'Sí' : 'No' });
  };
  if (step === STEPS.APPOINTMENT) {
    bodyContent = (
      <>
        <p className="font-bold">Cita de entrega</p>
        <div className='flex flex-col gap-3'>

          <div>
            <label className="font-bold">Fecha de pago es igual a la fecha de entrega</label> <br />
            <label>
              <input
                type="radio"
                value="Si"
                checked={isSameDate.value === 'Si'}
                onChange={handleRadioChange}
              /> Sí
            </label>
            <label className='px-4'>
              <input
                type="radio"
                value="No"
                checked={isSameDate.value === 'No'}
                onChange={handleRadioChange}
              /> No
            </label>
          </div>


          <InputDate
            label="Fecha y hora de entrega"
            name="deliverDate"
            includeHours
          />

          {isSameDate.value === 'No' && (
            <InputDate
              label="Fecha de pago"
              name="startDate"
              includeHours
            />
          )}

        </div>
      </>
    );
  }


  if (user.role === 'auditor') return null;

  return (
    <>
      <div className="relative">
        <button
          className="bg-[#5800F7] text-white text-start px-3 h-[40px] rounded w-auto"
          onClick={onOpen}
          data-cy="contract"
        >
          {openButtonText}
        </button>
      </div>
      {isLoading && <Spinner zIndex={1401} />}

      <Modal closeOnOverlayClick={false} size="xl" isOpen={isOpen} onClose={onClose}>
        <ModalOverlay zIndex={20} />
        <ModalContent>
          <ModalHeader>{header}</ModalHeader>
          <ModalCloseButton
            onClick={() => {
              setStep(STEPS.VEHICLEDATA);
              onClose();
              setSelectedAddress('1');
            }}
          />
          <Formik
            initialValues={{ ...initialValues, phone: initialValues.phone.replace('+52', '') }}
            onSubmit={(values, { setFieldError }) => {
              if (onChangeStep) {
                onChangeStep(values);
              }
              handleSubmit(values, setFieldError);
            }}
          >
            {(props) => {
              const validate = props.dirty && props.isValid;
              return (
                <FormikForm>
                  <ModalBody pb={6}>
                    <div className="flex flex-col gap-[20px]">{bodyContent}</div>
                  </ModalBody>
                  <ModalFooter gap={3}>
                    <Button
                      sx={{
                        color: '#5800F7',
                        borderColor: '#5800F7 !important',
                        border: '2px',
                        h: '40px',
                      }}
                      onClick={() => {
                        onBack();
                      }}
                    >
                      Anterior
                    </Button>
                    <PreviewButton step={step} onChangeStep={onChangeStep} />
                    <Button
                      data-cy="next"
                      sx={{
                        // bg: '#5800F7 !important',
                        color: 'white',
                        h: '40px',
                      }}
                      className={`
                      ${!validate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
                      text-white rounded-md  h-[40px] cursor-pointer`}
                      type="submit"
                      disabled={!validate}
                    >
                      {actionLabel}
                    </Button>
                  </ModalFooter>
                </FormikForm>
              );
            }}
          </Formik>
        </ModalContent>
      </Modal>
    </>
  );
}

function PreviewButton({ step, onChangeStep }: { step: STEPS; onChangeStep?: (values: any) => void }) {
  const context = useFormikContext();
  const location = usePathname();
  const carId = location.split('/')[4];
  const toast = useToast();
  return (
    <>
      {step === STEPS.APPOINTMENT && (
        <Button
          sx={{
            color: '#5800F7',
            borderColor: '#5800F7 !important',
            border: '2px',
            h: '40px',
          }}
          onClick={() => {
            if (onChangeStep) {
              const { values } = context as any;
              if (!values.phone.startsWith('+52')) {
                values.phone = "+52" + values.phone;
              }
            
              if (!values.deliverDate || values.deliverDate.length < 6) {
                context.setFieldError('deliverDate', 'Fecha requerida');
                return;
              }

              const canContinue = validateCreateContract(context.values, toast);
              if (!canContinue) {
                return;
              }

              onChangeStep(context.values);
              localStorage.setItem('contractForm-' + carId, JSON.stringify(context.values));
              window.open(location + '/pdf', '_blank');
            }
          }}
        >
          Preview
        </Button>
      )}
    </>
  );
}

function isObjectId(value: string) {
  return value.match(/^[0-9a-fA-F]{24}$/);
}

interface TypeOfContractProps {
  form: any;
  initialValues: any;
  admissionRequest?: any;
  setAdmissionRequest?: any;
}

function TypeOfContract({ form, initialValues, admissionRequest, setAdmissionRequest }: TypeOfContractProps) {
  const { user } = useCurrentUser();
  const allData = useVehicleDetailData();
  const [admissionRequestId, setAdmissionRequestId] = useState(admissionRequest?._id || admissionRequest?.id || '');
  const [approvedStatus, setApprovedStatus] = useState('');
  const [usePreviousDriverPrice, setUsePreviousDriverPrice] = useState(false);
  const [useManualAssistanceProduct, setUseManualAssistanceProduct] = useState(false);
  const [availableAssistanceProducts, setAvailableAssistanceProducts] = useState<Product[]>([]);
  const [
    selectedManualAssistanceProduct,
    setSelectedManualAssistanceProduct,
  ] = useState<Product | null>(null);


  const { rentingProduct, assistanceProduct, downProduct, depositProduct } = useFetchWeeklyPaymentMexico({
    model: form.model,
    region: form.city.value,
    isNew: allData.vehicleData.newCar,
    approvedStatus: approvedStatus,
    downPaymentModified: allData.vehicleData.downPaymentModified,
    usePreviousDriverPrice,
    stockId: allData.vehicleData._id,
  });

  const formik = useFormikContext<any>();

  useEffect(() => {
    if (admissionRequest) {
      formik.setFieldValue('admissionRequest', admissionRequest._id || admissionRequest.id);
      setAdmissionRequestId(admissionRequest._id || admissionRequest.id);

      const found = approvalOptions.find((option) => option.value === admissionRequest.typeOfPreapproval);

      if (found) {
        setApprovedStatus(admissionRequest.typeOfPreapproval);
        formik.setFieldValue('rentingType', found);
      }
    }
  }, [admissionRequest]);

  useEffect(() => {

    async function getAdmissionRequest() {
      const { data } = await axios.get(`${URL_API}/admission/requests/${admissionRequestId}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      setAdmissionRequest(data.data);
      const foundOption = approvalOptions.find((option) => option.value === data.data.typeOfPreapproval);
      if (foundOption) {
        setApprovedStatus(data.data.typeOfPreapproval);
        formik.setFieldValue('rentingType', foundOption);
      }
    }
    if (admissionRequestId && isObjectId(admissionRequestId) && !admissionRequest) {
      getAdmissionRequest();
    }

  }, [admissionRequestId]);

  // Función para fetchear productos de asistencia disponibles
  const fetchAvailableAssistanceProducts = async (customName?: string) => {
    if (!form.model || !form.city?.value) return;

    try {
      const url = new URL('/products', PAYMENTS_API_URL);
      // Usar el nombre personalizado si se proporciona, sino usar el patrón por defecto
      const productName = customName || `anexo A ${form.model}`;
      url.searchParams.append('name', productName);
      if (form.city?.value) url.searchParams.append('region', form.city.value.toUpperCase());

      const res = await axios.get(url.toString(), {
        headers: {
          Authorization: `Bearer ${PAYMENT_API_SECRET}`,
        },
      });

      const products = res.data.data || [];

      // Si es una búsqueda personalizada, agregar los productos encontrados a la lista existente
      if (customName) {
        // Evitar duplicados
        const existingIds = availableAssistanceProducts.map(p => p.id);
        const newProducts = products.filter((p: Product) => !existingIds.includes(p.id));
        setAvailableAssistanceProducts(prev => [...prev, ...newProducts]);
      } else {
        setAvailableAssistanceProducts(products);
      }
    } catch (err) {
      console.log('ERROR fetching assistance products', err);
      if (!customName) {
        setAvailableAssistanceProducts([]);
      }
    }
  };

  // Efecto para fetchear productos cuando se activa la opción manual
  useEffect(() => {
    if (useManualAssistanceProduct && form.model && form.city?.value) {
      fetchAvailableAssistanceProducts();
    }
  }, [useManualAssistanceProduct, form.model, form.city?.value]);

  // Calcular el pago semanal considerando el producto de asistencia manual si está seleccionado
  const finalAssistanceProductForCalculation = useManualAssistanceProduct && selectedManualAssistanceProduct
    ? selectedManualAssistanceProduct
    : assistanceProduct;

  const weeklyPayment = rentingProduct && finalAssistanceProductForCalculation
    && (+rentingProduct.price) + (+finalAssistanceProductForCalculation.price) || 0;


  useEffect(() => {

    formik.setFieldValue('weeklyRent', weeklyPayment);

    if (rentingProduct) {
      formik.setFieldValue('rentingProduct', rentingProduct);
    }

    // Usar producto de asistencia manual si está seleccionado, sino usar el automático
    const finalAssistanceProduct = useManualAssistanceProduct && selectedManualAssistanceProduct
      ? selectedManualAssistanceProduct
      : assistanceProduct;

    if (finalAssistanceProduct) {
      formik.setFieldValue('assistanceProduct', finalAssistanceProduct);
    }

    if (downProduct) {
      formik.setFieldValue('downPayment', downProduct.price);
      formik.setFieldValue('downPaymentProduct', downProduct);
    } else {
      formik.setFieldValue('downPayment', '');
      formik.setFieldValue('downPaymentProduct', '');
    }

    if (depositProduct) {
      formik.setFieldValue('depositProduct', depositProduct);
    } else {
      formik.setFieldValue('depositProduct', '');
    }
  }, [
    weeklyPayment,
    downProduct,
    depositProduct,
    useManualAssistanceProduct,
    selectedManualAssistanceProduct,
  ])

  return (
    <>
      <p className="font-bold">Datos del vehículo</p>
      <div className="w-full grid grid-cols-1 gap-x-4 gap-y-3">
        <SelectInput
          label="Ciudad"
          options={citiesDependingState[initialValues.state?.value || initialValues.state] || citiesSelect}
          name="city"
        />
        <SelectInput
          label="Entidad Federativa"
          name="state"
          options={ferderalEntitiesSelect}
        />

        <CustomInput
          label="Solicitud de admisión"
          name="admissionRequest"
          type="text"
          disabled={user.role !== 'superadmin' || !!admissionRequest}
          onChange={(value) => {
            setAdmissionRequestId(value);
          }}
        />

        <SelectInput
          label="Tipo de contrato"
          name="rentingType"
          disabled={user.role !== 'superadmin' || !admissionRequest}
          // approvalOptions MG3 should validate if the car model is MG3 to show the option
          // if not, it should not show the option
          options={approvalOptions.filter((option) => {

            if (option.value === ApprovalTypes.PRE_OWNED && initialValues.newCar) {
              return false;
            }
            return true;

          })}
          onChange={(option) => {
            setApprovedStatus(option.value);
          }}
        />
        <p>
          Pago semanal: <span className="font-bold">{weeklyPayment}</span>
        </p>

        {!form.newCar && (
          <>
            <label htmlFor="usePreviousDriverPrice" className="flex items-center">
              {/* Use previous driver price */}
              <span className="text-sm mr-2">
                Usar precio de conductor/contrato anterior
              </span>
              <Checkbox
                name="usePreviousDriverPrice"
                checked={usePreviousDriverPrice}
                onCheckedChange={(checked) => {
                  console.log('checked', checked);
                  if (checked) {
                    setUsePreviousDriverPrice(true);
                    setUseManualAssistanceProduct(false);
                  } else {
                    setUsePreviousDriverPrice(false);
                  }
                }}
              />
            </label>

            {/* Create an option that is able to fetch products to select specific product for assistance */}

            {/* Opción para selección manual de producto de asistencia - Solo visible para superadmin y admin */}
            {(user.role === 'superadmin') && (
              <div className="flex flex-col gap-3">
                <label htmlFor="useManualAssistanceProduct" className="flex items-center">
                  <span className="text-sm mr-2">
                    Seleccionar producto de asistencia manualmente
                  </span>
                  <Checkbox
                    name="useManualAssistanceProduct"
                    checked={useManualAssistanceProduct}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setUseManualAssistanceProduct(true);
                        setUsePreviousDriverPrice(false);
                      } else {
                        setUseManualAssistanceProduct(false);
                        setSelectedManualAssistanceProduct(null);
                      }
                    }}
                  />
                </label>

                {/* Combobox para seleccionar producto de asistencia */}
                {useManualAssistanceProduct && (
                  <div className="flex flex-col gap-2">
                    <label className="text-sm font-medium">
                      Producto de asistencia:
                    </label>
                    <CustomAssistanceProductCombobox
                      availableProducts={availableAssistanceProducts}
                      selectedProduct={selectedManualAssistanceProduct}
                      onProductSelect={setSelectedManualAssistanceProduct}
                      onCustomSearch={fetchAvailableAssistanceProducts}
                    />
                    {selectedManualAssistanceProduct && (
                      <div className="text-xs text-gray-600">
                        Seleccionado: {selectedManualAssistanceProduct.name} - $
                        {selectedManualAssistanceProduct.price}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

          </>
        )}

        {downProduct && <p>Enganche: <span className="font-bold">{downProduct.price || 0}</span></p>}

        {depositProduct && <p>Deposito: <span className="font-bold">{depositProduct.price || 0}</span></p>}
      </div>
      <div className="flex flex-col gap-3 ">
        {!initialValues.newCar ? (
          <>
            {/* <CalculateWeeks form={form} initialValues={initialValues} weeklyPayment={weeklyPayment} /> */}
            <ShowCalculatedWeeks stockId={form._id} />
            <CustomInput label="Total de semanas a pagar (calculado sugerido)" name="totalPays" type="number" />
          </>
        ) : null}

      </div>
    </>
  )

}

interface HookProps {
  model: string;
  region?: string;
  approvedStatus?: string;
  isNew: boolean;
  downPaymentModified?: boolean;
  usePreviousDriverPrice?: boolean;
  stockId?: string;
}
/* isNew */
export function useFetchWeeklyPaymentMexico({
  model, region, approvedStatus, downPaymentModified, ...rest
}: HookProps) {

  if (region?.toUpperCase() === 'EDOMX') region = 'CDMX';

  const [rentingProduct, setRentingProduct] = useState<Product | null>(null);
  const [assistanceProduct, setAssistanceProduct] = useState<Product | null>(null);
  const [downProduct, setDownProduct] = useState<Product | null>(null);
  const [depositProduct, setDepositProduct] = useState<Product | null>(null);
  const { user } = useCurrentUser();


  const fetchPreviousDriverPrice = async () => {
    // if (!rest.stockId || !rest.usePreviousDriverPrice) return;
    try {
      const { data } = await axios.get(`${URL_API}/stock/get-previous-driver-price/${rest.stockId}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });

      const previousRentingProduct = data.data.rentingProduct;
      const previousAssistanceProduct = data.data.assistanceProduct;


      const result = {
        previousRentingProduct: {
          ...previousRentingProduct,
          price: +previousRentingProduct.price,
        },
        previousAssistanceProduct: {
          ...previousAssistanceProduct,
          price: +previousAssistanceProduct.price,
        },
      }

      console.log('result', result);
      setRentingProduct(previousRentingProduct);
      setAssistanceProduct(previousAssistanceProduct);

      return result;

    } catch (err) {
      console.log('ERROR', err);
      return null;
    }
  };

  const fetchDownPaymentProduct = async () => {
    const url = new URL('/products', PAYMENTS_API_URL);
    // url.searchParams.append('name', `Enganche ${model}`);
    const isDownPaymentModified = localStorage.getItem('downPaymentModified') === 'true';
    const productNameDownPayment = downPaymentModified || isDownPaymentModified ? `Enganche ${model} modificado` : `Enganche ${model}`; // If the down payment is modified, we should show the modified down payment product
    url.searchParams.append('name', productNameDownPayment);
    url.searchParams.append('selectOne', 'true');
    // if (region) url.searchParams.append('region', region.toUpperCase());
    try {
      const res = await axios.get(url.toString(), {
        headers: {
          Authorization: `Bearer ${PAYMENT_API_SECRET}`,
        },
      });
      const products = res.data.data;
      setDownProduct(products[0]);
    } catch (err) {
      console.log('ERROR', err);
    }
  };

  const fetchDepositProduct = async () => {
    const url = new URL('/products', PAYMENTS_API_URL);
    url.searchParams.append('name', `Deposito ${model}`);
    url.searchParams.append('selectOne', 'true');
    try {
      const res = await axios.get(url.toString(), {
        headers: {
          Authorization: `Bearer ${PAYMENT_API_SECRET}`,
        },
      });
      const products = res.data.data;
      setDepositProduct(products[0]);
    } catch (err) {
      console.log('ERROR', err);
    }
  };

  const fetchAssistanceProduct = async (nameProduct: string) => {
    const url = new URL('/products', PAYMENTS_API_URL);

    url.searchParams.append('name', `anexo A ${model}${nameProduct}`);
    url.searchParams.append('selectOne', 'true');
    if (region) url.searchParams.append('region', region.toUpperCase());
    try {
      const res = await axios.get(url.toString(), {
        headers: {
          Authorization: `Bearer ${PAYMENT_API_SECRET}`,
        },
      });
      const products = res.data.data;
      setAssistanceProduct(products[0]);
    } catch (err) {
      console.log('ERROR', err);
    }
  }



  useEffect(() => {

    const fetchRentingProduct = async () => {
      const url = new URL('/products', PAYMENTS_API_URL);
      url.searchParams.append('selectOne', 'true');
      url.searchParams.append('name', `Renting ${model}`);
      if (region) url.searchParams.append('region', region.toUpperCase());
      try {
        const res = await axios.get(url.toString(), {
          headers: {
            Authorization: `Bearer ${PAYMENT_API_SECRET}`,
          },
        });
        const products = res.data.data;
        setRentingProduct(products[0]);
      } catch (err) {
        console.log('ERROR', err);
      }
    }

    fetchRentingProduct();



  }, [model, region, approvedStatus]);


  useEffect(() => {

    if (!approvedStatus) return;

    if (rest.usePreviousDriverPrice && rest.stockId) {
      fetchPreviousDriverPrice();
      return;
    }

    if (approvedStatus === ApprovalTypes.PREAPPROVED ||
      approvedStatus === ExtendApproval.APPROVED ||
      approvedStatus === ApprovalTypes.DEPOSIT ||
      approvedStatus === ApprovalTypes.PRE_OWNED
      // approvedStatus === ApprovalTypes.MG3
    ) {
      fetchAssistanceProduct('');
    }

    if (approvedStatus === ApprovalTypes.DOWN_PAYMENT) {
      fetchDownPaymentProduct();
      fetchAssistanceProduct(' enganche');
    } else {
      setDownProduct(null);
    }

    if (approvedStatus === ApprovalTypes.DEPOSIT) {
      fetchDepositProduct();
    } else {
      setDepositProduct(null);
    }

  }, [region, model, approvedStatus, rest.usePreviousDriverPrice]);

  return {
    rentingProduct,
    assistanceProduct,
    downProduct,
    depositProduct,
  };

}

// Componente personalizado para el Combobox de productos de asistencia
interface CustomAssistanceProductComboboxProps {
  availableProducts: Product[];
  selectedProduct: Product | null;
  onProductSelect: (product: Product | null) => void;
  onCustomSearch: (customName: string) => Promise<void>;
}

function CustomAssistanceProductCombobox({
  availableProducts,
  selectedProduct,
  onProductSelect,
  onCustomSearch,
}: CustomAssistanceProductComboboxProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [lastSearchTerm, setLastSearchTerm] = useState('');
  const [currentSearchText, setCurrentSearchText] = useState('');

  // Crear opciones del combobox con lógica dinámica basada en el texto de búsqueda
  const createComboboxOptions = (searchText: string): ComboboxOption[] => {
    const productOptions: ComboboxOption[] = availableProducts.map((product) => ({
      value: product.id,
      label: product.name,
      subtitle: `$${product.price} - ${product.region}`,
    }));

    // Si hay texto de búsqueda, agregar opción de búsqueda exacta al final
    if (searchText.trim()) {
      const customSearchOption: ComboboxOption = {
        value: `custom-search-${searchText}`,
        label: '🔍 Buscar producto por nombre exacto',
        subtitle: `Buscar: "${searchText}"`,
      };
      return [...productOptions, customSearchOption];
    }

    return productOptions;
  };

  const handleValueChange = (value: string): boolean | void => {
    // Si es una búsqueda personalizada
    if (value.startsWith('custom-search-')) {
      const searchTerm = value.replace('custom-search-', '');
      if (!searchTerm.trim() || searchTerm === lastSearchTerm) return false;

      setIsLoading(true);
      setLastSearchTerm(searchTerm);

      // Ejecutar búsqueda de forma asíncrona
      onCustomSearch(searchTerm.trim())
        .then(() => {
          setCurrentSearchText(''); // Limpiar el texto de búsqueda después de la búsqueda
        })
        .catch((error) => {
          console.error('Error en búsqueda personalizada:', error);
        })
        .finally(() => {
          setIsLoading(false);
        });

      return false; // No cerrar el combobox
    }

    // Selección normal de producto
    const foundProduct = availableProducts.find((p) => p.id === value);
    onProductSelect(foundProduct || null);
    return true; // Cerrar el combobox normalmente
  };

  return (
    <div className="relative">
      <Combobox
        options={createComboboxOptions(currentSearchText)}
        value={selectedProduct?.id || ''}
        onValueChange={handleValueChange}
        placeholder="Seleccionar producto de asistencia..."
        searchPlaceholder="Buscar producto o escribir nombre exacto..."
        emptyText={
          currentSearchText.trim()
            ? 'No se encontraron coincidencias. Use la opción "🔍 Buscar producto por nombre exacto" para buscar con el texto completo.'
            : 'No se encontraron productos de asistencia.'
        }
        disabled={isLoading}
        onSearchChange={setCurrentSearchText}
      />
      {isLoading && (
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
        </div>
      )}
    </div>
  );
}

// Make an api call to get the calculated weeks
// function CalculateWeeks({ form, initialValues, weeklyPayment }: any) {
//   const isNew = initialValues.newCar;
//   const { user } = useCurrentUser();
//   const formik = useFormikContext<any>();
//   const { id: stockId } = useParams<{ id: string }>();
//   const [hasInvoice, setHasInvoice] = useState<boolean | undefined>(undefined);
//   const [invoiceAmount, setInvoiceAmount] = useState<number>();
//   const [invoiceFile, setInvoiceFile] = useState<File>();
//   const [nameFile, setNameFile] = useState('');
//   const [weeksDetail, setWeeksDetail] = useState<{
//     totalWeeks: number;
//     weeksWithoutOverhauling: number | undefined;
//     lastPaymentDate: string;
//     paymentsCompleted: number;
//     previousTotalPayments: number;
//     invoiceAmount: number | undefined;
//   }>();
//   const [isOldVersion, setIsOldVersion] = useState(false);

//   useEffect(() => {
//     async function getWeeks() {
//       if (isNew) return;
//       try {
//         const sendData = {
//           stockId,
//           weeklyPayment,
//           hasInvoice,
//           invoiceAmount,
//           invoiceFile,
//         };

//         const res = await axios.post(`${URL_API}/contract/semi-new/calculate-weeks`, sendData, {
//           headers: {
//             Authorization: `Bearer ${user.accessToken}`,
//             'Content-Type': 'multipart/form-data',
//           },
//         });

//         const data = res.data.data as {
//           weeks: number;
//           lastPaymentDate: string;
//           paymentsCompleted: number;
//           detail: {
//             previousTotalPayments: number;
//             weeksWithoutOverhauling: number | undefined;
//             overhauling: {
//               _id: string;
//               isOldVersion: boolean;
//               hasInvoice: boolean;
//               weeks: number | undefined;
//               invoiceAmount: number | undefined;
//             }
//           }
//         }
//         console.log('isOldVersion', data.detail.overhauling.isOldVersion);
//         setIsOldVersion(data.detail.overhauling.isOldVersion);
//         // console.log('total weeks and without overhauling', data.detail.weeksWithoutOverhauling, data.weeks);
//         setWeeksDetail({
//           totalWeeks: data.weeks,
//           weeksWithoutOverhauling: data.detail.weeksWithoutOverhauling,
//           lastPaymentDate: data.lastPaymentDate,
//           paymentsCompleted: data.paymentsCompleted,
//           previousTotalPayments: data.detail.previousTotalPayments,
//           invoiceAmount: data.detail.overhauling.invoiceAmount,
//         });

//         if (data.detail.overhauling.isOldVersion && hasInvoice === undefined) {
//           setHasInvoice(data.detail.overhauling.hasInvoice);
//           setInvoiceAmount(data.detail.overhauling.invoiceAmount);
//         }

//         formik.setFieldValue('totalPays', data.weeks);
//       } catch (error: any) {
//         console.log('error response data: ', error.response.data);
//       }
//     }
//     getWeeks();
//   }, [form, weeklyPayment, hasInvoice, invoiceAmount]);
//   const onChange = useDebounce((value: string) => {
//     // setInvoiceAmount(Number(value));
//     // cannot be negative
//     const newValue = Number(value);
//     if (newValue > 0) {
//       setInvoiceAmount(newValue);
//     }
//   }, 500);
//   if (isNew) return null;




//   return (
//     <div className="flex flex-col gap-3">

//       {
//         isOldVersion && (
//           <>
//             <h2 className="text-sm text-red-500">
//               Esta revisión no se corroboró si tiene factura o no, favor de confirmar
//             </h2>
//           </>
//         )
//       }

//       <SelectInput
//         label="¿Tiene factura?"
//         name="hasInvoice"
//         options={[
//           { value: 'Si', label: 'Si' },
//           { value: 'No', label: 'No' },
//         ]}
//         defaultOption={{ value: hasInvoice ? 'Si' : 'No', label: hasInvoice ? 'Si' : 'No' }}
//         onChange={(option) => {
//           setHasInvoice(option.value === 'Si');
//         }}
//       />
//       {hasInvoice && (
//         <>
//           <CustomInput
//             name="invoiceAmount"
//             label="Monto de la factura"
//             type="number"
//             onChange={onChange}
//           />
//           <InputFile
//             name="invoiceFile"
//             label="Factura"
//             nameFile={nameFile}
//             handleSingleSetName={setNameFile}
//             accept="pdf"
//             buttonText="Subir archivo"
//             placeholder="No mayor a 3mb"
//             onChange={(e: any) => {
//               const file = e.target.files?.[0];
//               if (file) setInvoiceFile(file);
//             }}
//           />
//         </>
//       )}


//       {weeksDetail && (
//         <div className="flex flex-col gap-2 text-sm">
//           <p>Semanas del contrato anterior: <span className="font-semibold">{weeksDetail.previousTotalPayments}</span></p>
//           <p>Pagos completados del contrato anterior: <span className="font-semibold">{weeksDetail.paymentsCompleted}</span></p>
//           <p>Fecha de último pago: <span className="font-semibold">{DateTime.fromISO(weeksDetail.lastPaymentDate).toFormat("d 'de' MMMM 'de' yyyy", { locale: 'es' })}</span></p>
//           {
//             // checar si las semanas son diferentes para mostrar, si no, no mostrar
//             weeksDetail.weeksWithoutOverhauling &&
//             weeksDetail.weeksWithoutOverhauling !== weeksDetail.totalWeeks && (
//               <>
//                 <p>Monto de revisión: <span className="font-semibold">${weeksDetail.invoiceAmount?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || 0}</span></p>

//                 <p>
//                   Semanas sin considerar monto de revisión:{' '}
//                   <span className="font-semibold">{weeksDetail.weeksWithoutOverhauling}</span>
//                 </p>
//                 <div>

//                   <p>
//                     Suma adicional de semanas por monto de revisión:{' '}
//                     <span className="font-semibold">
//                       {weeksDetail.totalWeeks - weeksDetail.weeksWithoutOverhauling}
//                     </span>

//                   </p>
//                   <p className="text-xs mt-1">
//                     Calculo realizado: (Monto de revisión / Pago semanal) redondeando al entero más cercano
//                   </p>
//                 </div>

//               </>
//             )
//           }
//           <p>Total de semanas calculadas: <span className="font-semibold">{weeksDetail.totalWeeks}</span></p>
//         </div>
//       )}

//       {/* {hasInvoice !== undefined && (
//         <div className="flex flex-col gap-3 border p-3 rounded">
//           <p className="font-medium">Información de revisión previa</p>
//           <SelectInput
//             name="hasInvoice"
//             label="¿Tiene factura?"
//             defaultOption={{ value: hasInvoice ? 'Si' : 'No', label: hasInvoice ? 'Si' : 'No' }}
//             // value={{ value: hasInvoice ? 'Si' : 'No', label: hasInvoice ? 'Si' : 'No' }}
//             options={[
//               { value: 'Si', label: 'Si' },
//               { value: 'No', label: 'No' },
//             ]}
//             onChange={(option) => setHasInvoice(option.value === 'Si')}
//           />

//           {hasInvoice && (
//             <>
//               <InputFile
//                 name="invoiceFile"
//                 label="Factura"
//                 nameFile={nameFile}
//                 handleSetName={setNameFile}
//                 accept="pdf"
//                 buttonText="Subir archivo"
//                 placeholder="No mayor a 3mb"
//                 onChange={(e: any) => {
//                   const file = e.target.files?.[0];
//                   if (file) setInvoiceFile(file);
//                 }}
//               />

//               <CustomInput
//                 name='invoiceAmount'
//                 label="Monto de la factura"
//                 type="number"
//                 // value={invoiceAmount}
//                 // onChange={(e) => setInvoiceAmount(Number(e.target.value))}
//                 onChange={(value) => setInvoiceAmount(Number(value))}
//               />
//             </>
//           )}
//         </div>
//       )} */}
//     </div>
//   );
// }
