import {
  <PERSON><PERSON>,
  <PERSON>dal,
  ModalBody,
  ModalClose<PERSON>utton,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';
import { Form as FormikForm, Formik, FormikValues, useFormikContext } from 'formik';
import * as Yup from 'yup';
import CustomInput from '../Inputs/CustomInput';
import SelectInput from '../Inputs/SelectInput';
import {
  addressDataSchemaUS,
  associateDataSchema,
  facturationAddressSchema,
  facturationSchema,
  vehicleDataSchema,
  CountryContractSchemaUS,
  appointmentSchemaUS,
} from '@/validatorSchemas/contractSchema';
import InputPhone from '../Inputs/InputPhone';
import RadioOptions from '../Inputs/RadioOptions';
import InputDate from '../Inputs/InputDate';

import { useContract } from '@/app/dashboard/flotilla/components/Detail/GenerateContract';
import { usePathname, useRouter } from 'next/navigation';
import Spinner from '../Loading/Spinner';
import {
  CountriesOptions,
  PAYMENTS_API_URL,
  PAYMENT_API_SECRET,
  USCITIESNAMES,
  US_CITIES_NAMES_SHORT_CODES,
  US_CITIES_OPTIONS,
  US_COUNTRY_CODE,
  US_STATES_OBJ,
  US_STATES_OPTIONS,
} from '@/constants';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import axios from 'axios';
import { Product } from '@/app/dashboard/pagos/types';

enum STEPS {
  COUNTRY = 1,
  DATA = 2,
  ADDRESS = 3,
  BILLINGDATA = 4,
  VEHICLEDATA = 5,
  APPOINTMENT = 6,
}

const validationSchemas = {
  [STEPS.COUNTRY]: CountryContractSchemaUS,
  [STEPS.DATA]: associateDataSchema,
  [STEPS.ADDRESS]: addressDataSchemaUS,
  [STEPS.BILLINGDATA]: facturationSchema,
  [STEPS.VEHICLEDATA]: vehicleDataSchema,
  [STEPS.APPOINTMENT]: appointmentSchemaUS,
};

interface CustomModalProps {
  header: string;
  openButtonText: string;
  confirmButtonText?: string;
  validatorSchema?: Yup.ObjectSchema<any>;
  initialValues: FormikValues;
  onSubmit: (values: FormikValues) => Promise<any>;
  onCloseModal?: () => any;
  bodyContent?: React.ReactElement;
  onChangeStep?: (values: FormikValues) => void; // Esto sirve para actualizar el estado de los valores con el paso actual
}
const requiredFields: Record<string, { required: boolean; message: string }> = {
  policyNumber: {
    required: true,
    message: 'Policy number',
  },
  plates: {
    required: true,
    message: 'Plates',
  },
  circulationCardNumber: {
    required: true,
    message: 'Circulation card number',
  },
};

function validateCreateContract(data: any, toast: any) {
  const missingFields = Object.keys(requiredFields).filter((key) => !data[key]);

  if (missingFields.length) {
    const msg = `The following fields are missing:\n${missingFields
      .map((key, i) => `${i + 1}. ${requiredFields[key].message}`)
      .join(' ')}`;
    toast({
      title: 'The contract could not be generated',
      description: msg || '',
      status: 'info',
      duration: 3000,
      isClosable: true,
      position: 'top',
    });
    return false;
  }

  return true;
}

export default function ContractModalUS({
  header,
  openButtonText,
  initialValues,
  onSubmit,
  onChangeStep,
}: CustomModalProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [step, setStep] = useState(STEPS.COUNTRY);
  const [selectedAddress, setSelectedAddress] = useState('1');
  const router = useRouter();

  const onBack = () => {
    if (step === STEPS.COUNTRY) return;
    setStep((value) => value - 1);
  };
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const { form, setForm } = useContract();

  const onNext = () => {
    setStep((value) => value + 1);
  };

  const location = usePathname();
  const carId = location.split('/')[4];

  const handleSubmit = async (data: any, setFieldError: any) => {
    let isValid = true;
    const dataValidator = {
      ...data,
    };
    if (validationSchemas.hasOwnProperty(step)) {
      try {
        await validationSchemas[step].validate(dataValidator, {
          abortEarly: false,
        });
        if (step === STEPS.BILLINGDATA && selectedAddress === '2') {
          await facturationAddressSchema.validate(dataValidator, {
            abortEarly: false,
          });
        }
      } catch (validationError: any) {
        console.log('validationError: ', validationError);

        isValid = false;
        validationError.inner.forEach((error: any) => {
          console.log('error: ', error);
          setFieldError(error.path, error.message);
        });
      }
    }

    if (isValid) {
      if (step !== STEPS.APPOINTMENT) {
        return onNext();
      }
      try {
        if (onChangeStep) {
          onChangeStep(data);
        }
        setIsLoading(true);
        const canContinue = validateCreateContract(data, toast);
        if (!canContinue) {
          setIsLoading(false);
          return false;
        }
        await onSubmit(data);

        onClose();
        toast({
          title: 'Contrato generado exitosamente',
          status: 'success',
          duration: 3000,
          isClosable: true,
          position: 'top',
        });
        localStorage.removeItem('contractForm-' + carId);
        router.refresh();
      } catch (error: any) {
        toast({
          title: 'Algo salio mal',
          description: error.message || '',
          status: 'error',
          duration: 3000,
          isClosable: true,
          position: 'top',
        });
      } finally {
        setIsLoading(false);
      }
    }
    return null;
  };

  const actionLabel = step === STEPS.APPOINTMENT ? 'Generate contract' : 'Following';

  let bodyContent = (
    <>
      <p className="font-bold">{'Contract location'}</p>
      <SelectInput
        label="Country"
        options={[CountriesOptions[initialValues.country.value as keyof typeof CountriesOptions]]}
        name="country"
      />
    </>
  );

  if (step === STEPS.DATA) {
    bodyContent = (
      <>
        <p className="font-bold">{'Client data'}</p>
        <div className="w-full grid grid-cols-2 gap-x-4 gap-y-3 relative">
          <CustomInput label="First Name" name="firstName" type="text" />
          <CustomInput label="Last Name" name="lastName" type="text" />
          <InputPhone label="Phone no" name="phone" placeholder="" countryCode={US_COUNTRY_CODE} />
          <InputPhone label="Telephone no" name="tel" placeholder="" countryCode={US_COUNTRY_CODE} />
          <CustomInput label="Email" name="email" type="email" />
          <CustomInput label="Contract No." name="contractNumber" type="text" disabled />
          {initialValues.extensionCarNumber ? (
            <div className="absolute bottom-[8px] right-[180px]"> - {initialValues.extensionCarNumber}</div>
          ) : null}
        </div>
      </>
    );
  }

  if (step === STEPS.ADDRESS) {
    bodyContent = (
      <>
        <p className="font-bold">{'Client address'}</p>
        <CustomInput label="Street" name="street" type="text" />
        <div className="w-full grid grid-cols-2 gap-2">
          <CustomInput label="Address 1. (optional)" name="interior" type="text" />
          <SelectInput
            label="State"
            options={[US_STATES_OBJ[initialValues?.state?.value as keyof typeof US_STATES_OBJ]]}
            name="state"
          />

          <SelectInput label="city" options={[US_CITIES_OPTIONS[USCITIESNAMES.Dallas]]} name="city" />
          <CustomInput label="Zip code" name="postalCode" type="text" />
        </div>
      </>
    );
  }

  if (step === STEPS.BILLINGDATA) {
    bodyContent = (
      <>
        <p className="font-bold">{'Billing Data'}</p>
        {/* <CustomInput label="RFC" name="rfc" type="text" /> */}

        <CustomInput label="SSN" name="ssn" type="text" />
        <div className="flex flex-col gap-3">
          <p>Is the billing address different from the personal address?</p>
          <RadioOptions
            name="sameAddress"
            options={[
              {
                label:
                  'No, use the same address (the same data registered in the previous step will be used)',
                value: '1',
                name: 'some',
              },
              {
                label: 'Yes, add a new address',
                value: '2',
                name: 'some2',
              },
            ]}
            selectedAddress={selectedAddress}
            onChange={(value) => {
              setSelectedAddress(value);
            }}
          />
          {selectedAddress === '2' ? (
            <>
              <CustomInput label="Street" name="streetDos" type="text" />
              <div className="w-full grid grid-cols-2 gap-2">
                <CustomInput label="Exterior No" name="exteriorDos" type="text" />
                <CustomInput label="Interior No (opcional)" name="interiorDos" type="text" />
                <CustomInput label="Zip code" name="zipDos" type="text" />
                <CustomInput label="Delegation or municipality" name="townHallDos" type="text" />

                <SelectInput label="State" options={US_STATES_OPTIONS} name="stateDos" />
              </div>
            </>
          ) : null}
        </div>
      </>
    );
  }

  if (step === STEPS.VEHICLEDATA) {
    bodyContent = (
      <TypeOfContract
        form={form}
        setForm={setForm}
        initialValues={{ ...initialValues, phone: initialValues.phone.replace('+1', '') }}
      />
    );
  }

  const [isSameDate, setIsSameDate] = useState({ value: 'Yes', label: 'Yes' });

  if (step === STEPS.APPOINTMENT) {
    bodyContent = (
      <>
        <p className="font-bold">{'Delivery appointment'}</p>
        <div className="flex flex-col gap-3">
          <SelectInput
            label="Payment date is equal to the delivery date"
            name="isSameDate"
            options={[
              { value: 'Yes', label: 'Yes' },
              { value: 'No', label: 'No' },
            ]}
            onChange={(option) => {
              setIsSameDate(option);
            }}
            placeholder="Select"
          />

          <InputDate label="Delivery date and time" name="deliverDate" includeHours />

          {isSameDate.value === 'No' && <InputDate label="Payment date" name="startDate" includeHours />}
        </div>
      </>
    );
  }

  const { user } = useCurrentUser();

  if (user.role === 'auditor') return null;

  return (
    <>
      <div className="relative">
        <button
          className="bg-[#5800F7] text-white text-start px-3 h-[40px] rounded w-auto"
          onClick={onOpen}
          data-cy="contract"
        >
          {openButtonText}
        </button>
      </div>
      {isLoading && <Spinner zIndex={1401} />}

      <Modal closeOnOverlayClick={false} size="xl" isOpen={isOpen} onClose={onClose}>
        <ModalOverlay zIndex={20} />
        <ModalContent>
          <ModalHeader>{header}</ModalHeader>
          <ModalCloseButton
            onClick={() => {
              setStep(STEPS.COUNTRY);
              onClose();
              setSelectedAddress('1');
            }}
          />
          <Formik
            initialValues={{ ...initialValues, phone: initialValues.phone.replace('+1', '') }}
            onSubmit={(values, { setFieldError }) => {
              if (onChangeStep) {
                onChangeStep(values);
              }
              handleSubmit(values, setFieldError);
            }}
          >
            {(props) => {
              const validate = props.dirty && props.isValid;
              return (
                <FormikForm>
                  <ModalBody pb={6}>
                    <div className="flex flex-col gap-[20px]">{bodyContent}</div>
                  </ModalBody>
                  <ModalFooter gap={3}>
                    <Button
                      sx={{
                        color: '#5800F7',
                        borderColor: '#5800F7 !important',
                        border: '2px',
                        h: '40px',
                      }}
                      onClick={() => {
                        onBack();
                      }}
                    >
                      {'Back'}
                    </Button>
                    <PreviewButton step={step} onChangeStep={onChangeStep} />
                    <Button
                      data-cy="next"
                      sx={{
                        color: 'white',
                        h: '40px',
                      }}
                      className={`
                        ${!validate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
                        text-white rounded-md  h-[40px] cursor-pointer`}
                      type="submit"
                      disabled={!validate}
                    >
                      {actionLabel}
                    </Button>
                  </ModalFooter>
                </FormikForm>
              );
            }}
          </Formik>
        </ModalContent>
      </Modal>
    </>
  );
}

function PreviewButton({ step, onChangeStep }: { step: STEPS; onChangeStep?: (values: any) => void }) {
  const context = useFormikContext();
  const location = usePathname();
  const carId = location.split('/')[4];
  const toast = useToast();
  const { form } = useContract();

  return (
    <>
      {step === STEPS.APPOINTMENT && (
        <Button
          sx={{
            color: '#5800F7',
            borderColor: '#5800F7 !important',
            border: '2px',
            h: '40px',
          }}
          onClick={() => {
            if (onChangeStep) {
              const { values } = context as any;
              if (!values.phone.startsWith('+1')) {
                values.phone = '+1' + values.phone;
              }
              if (!values.deliverDate || values.deliverDate.length < 6) {
                context.setFieldError('deliverDate', 'Deliver date is required');
                return;
              }
              const canContinue = validateCreateContract(context.values, toast);
              if (!canContinue) {
                return;
              }

              onChangeStep(context.values);
              localStorage.setItem(
                'contractForm-' + carId,
                JSON.stringify({
                  ...form,
                  ...(context.values as object),
                })
              );
              window.open(location + '/pdf', '_blank');
            }
          }}
        >
          Preview
        </Button>
      )}
    </>
  );
}

function TypeOfContract({ form, initialValues }: any) {
  const formik = useFormikContext<any>();

  const { error, rentingProduct, downProduct } = useFetchData({
    rentingType: formik.values.rentingType,
    model: form.model,
    region: form.city.value,
  });

  const handleSelection = async (option: { value: string; label: string }) => {
    formik.setFieldValue('rentingType', option);
  };

  const isNormalRent = formik.values.rentingType.value === 'normal_rent';
  const isDownPayment = formik.values.rentingType.value === 'down_payment';
  const weeklyPayment = (rentingProduct && rentingProduct.subTotal) || 0;

  useEffect(() => {
    if (rentingProduct) {
      formik.setFieldValue('rentingProduct', rentingProduct);
    }
  }, [rentingProduct, formik]);

  return (
    <>
      <p className="font-bold">{'Vehicle data'}</p>
      <div className="w-full grid grid-cols-1 gap-x-4 gap-y-3">
        <SelectInput
          label="Contract type"
          name="rentingType"
          options={[
            { value: 'normal_rent', label: 'Normal Income' },
            // { value: 'down_payment', label: 'Down Payment' },
            // { value: 'deposit', label: 'Deposit' },
          ]}
          onChange={handleSelection}
        />
        <PaymentComp
          error={error}
          isNormalRent={isNormalRent}
          weeklyPayment={weeklyPayment}
          isDownPayment={isDownPayment}
          downProduct={downProduct}
        />
      </div>
      <div className="flex flex-col gap-3 ">
        {!initialValues.newCar ? (
          <>
            <CustomInput label="Total weeks to pay" name="totalPays" type="number" />
          </>
        ) : null}
      </div>
    </>
  );
}

const PaymentComp = (props: any) => {
  const { error, isNormalRent, weeklyPayment, isDownPayment, downProduct } = props;

  if (error) {
    return <span className="font-bold bg-red-500">{error}</span>;
  }

  if (isNormalRent) {
    return (
      <p>
        {'Weekly payment:'} <span className="font-bold">{weeklyPayment}</span>
      </p>
    );
  }

  if (isDownPayment) {
    return (
      <p>
        {'Deposit:'} <span className="font-bold">{downProduct.price || 0}</span>
      </p>
    );
  }
  return null;
};

function useFetchData({ rentingType, model, region }: any) {
  const [rentingProduct, setRentingProduct] = useState<Product | null>(null);
  const [error, setError] = useState('');
  const [downProduct, setDownProduct] = useState<Product | null>(null);

  useEffect(() => {
    if (rentingType.value === 'normal_rent' && model && region) {
      fetchRentingProduct({
        model: model,
        region: region,
      })
        .then((res: any) => {
          const { products } = res;
          setRentingProduct(products);
        })
        .catch((err: Error) => {
          setError(err.message);
        });
    }
  }, [model, region, rentingType.value]);

  useEffect(() => {
    if (rentingType.value === 'down_payment' && model) {
      fetchDownPaymentProduct({
        model: model,
      })
        .then((res: any) => {
          const { products } = res;
          setDownProduct(products);
        })
        .catch((err: Error) => {
          console.log('Error fetching down payment product: ', err);
        });
    }
  }, [model, rentingType.value]);
  return {
    rentingProduct,
    error,
    downProduct,
  };
}

const fetchRentingProduct = async (rentingProductData: { model: string; region?: string }) => {
  const { model, region: selectedRegion } = rentingProductData;
  const regionShortName =
    US_CITIES_NAMES_SHORT_CODES[selectedRegion as keyof typeof US_CITIES_NAMES_SHORT_CODES];

  const url = new URL('/products', PAYMENTS_API_URL);
  url.searchParams.append('selectOne', 'true');
  url.searchParams.append('name', `Renting ${model}`);
  if (regionShortName) url.searchParams.append('region', regionShortName);
  try {
    const res = await axios.get(url.toString(), {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
    const products = res.data.data;
    return {
      products: products[0],
    };
  } catch (err) {
    throw new Error('Error occured while fetching Renting product Info');
  }
};

const fetchDownPaymentProduct = async ({ model }: { model: string }) => {
  const url = new URL('/products', PAYMENTS_API_URL);
  url.searchParams.append('name', `Enganche ${model}`);
  url.searchParams.append('selectOne', 'true');
  try {
    const res = await axios.get(url.toString(), {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
    const products = res.data.data;
    return {
      products,
    };
  } catch (err) {
    throw new Error('Error occured while fetching Down payment product Info');
  }
};
