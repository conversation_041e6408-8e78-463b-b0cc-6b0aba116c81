'use client';
import React, { ReactNode, useEffect } from 'react';
import {
  IconButton,
  Avatar,
  Box,
  CloseButton,
  Flex,
  HStack,
  VStack,
  useColorModeValue,
  Drawer,
  DrawerContent,
  Text,
  useDisclosure,
  BoxProps,
  FlexProps,
  Menu,
  MenuButton,
  MenuDivider,
  MenuItem,
  MenuList,
  useToast,
  Show,
} from '@chakra-ui/react';
import { FiHome, FiMenu } from 'react-icons/fi';
import NavItem from './NavItem';
import Image from 'next/image';
import { redirect, usePathname, useRouter } from 'next/navigation';
import { signOut } from 'next-auth/react';
import axios from 'axios';
import FlotillaNavItems from './FlotillaNavItems';
import ClientesNavItems from './ClientesNavItems';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import PagosNavItems from './PagosNavItems';
import useHandleResize from '@/hooks/useHandleRezise';
import CalendarNavItems from './CalendarNavItems';
import UsersNavItems from './UsersNavItems';
import { Capabilities, Paths, Roles, Sections, Subsections, URL_API } from '@/constants';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerformAny } from '@/casl/canPerformAny';
import { canPerform } from '@/casl/canPerform';
import VehicleSearch from '../Search/VehicleSearch';

export default function SideNavbar({ children }: { children: ReactNode }) {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const isMobile = useHandleResize({ breakpoint: 768 });

  useEffect(() => {
    if (!isMobile) {
      onClose();
    }
  }, [isMobile, onClose]);

  const ability = usePermissions();
  const pathname = usePathname();
  let canView = true;
  const { user: currentUser } = useCurrentUser();
  if (currentUser?.role === 'auditor' && pathname === Paths.dashboard_default) {
    redirect(Paths.fleet_active);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const matchedwholeKey = Object.entries(Paths).find(([key, value]) => pathname === value)?.[0];

  if (matchedwholeKey) {
    const splitKey = matchedwholeKey.split('_');
    const section = splitKey[0];
    let subSection = splitKey[1];
    if (ability) {
      if (section === Sections.Calendar && subSection === Subsections.CalendarPreview) {
        canView =
          canPerform(ability, Capabilities.ViewMyCalendar, section, subSection) ||
          canPerform(ability, Capabilities.ViewAllCalendar, section, subSection);
      } else {
        canView = canPerform(ability, Capabilities.View, section, subSection);
      }
    } else canView = false;
  } else {
    const matchedSubKey = Object.entries(Paths).find(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      ([key, value]) => pathname.includes(value) && value !== Paths.dashboard_default
    )?.[0];
    if (matchedSubKey) {
      const splitKey = matchedSubKey.split('_');
      const section = splitKey[0];
      let subSection = splitKey[1];
      if (ability) {
        canView = canPerform(ability, Capabilities.View, section, subSection);
      } else canView = false;
    }
  }

  return (
    <>
      <Show>
        <Box minH="100vh" bg={useColorModeValue('gray.100', 'gray.900')} position="relative" w="100%">
          {/* eslint-disable-next-line @typescript-eslint/no-use-before-define */}
          <SidebarContent onClose={() => onClose} display={{ base: 'none', md: 'block' }} />
          <Drawer
            autoFocus={false}
            isOpen={isOpen}
            placement="left"
            onClose={onClose}
            returnFocusOnClose={false}
            onOverlayClick={onClose}
            // size="full"
          >
            <DrawerContent className="!w-[250px] lg:!w-[300px]">
              /{/* eslint-disable-next-line @typescript-eslint/no-use-before-define */}
              <SidebarContent onClose={onClose} />
            </DrawerContent>
          </Drawer>
          {/* mobilenav */}
          {/* eslint-disable-next-line @typescript-eslint/no-use-before-define */}
          <MobileNav onOpen={onOpen} />
          <Box
            as="main"
            ml={{ base: 0, md: '300px' }}
            bg="#FAFAFA"
            pt="30px"
            px="35px"
            // pl="35px"
            // pr="35px"
            // h="100%"
            w="auto"
            minH="100vh"
            overflowY="auto"
          >
            {canView ? children : <NoPermisos />}
          </Box>
        </Box>
      </Show>
      {/* <Show breakpoint="(max-width: 860px)">
        <Flex w="100%" h="100vh" alignItems="center" justifyContent="center">
          Contenido solo disponible en computadoras
        </Flex>
      </Show> */}
    </>
  );
}

const NoPermisos = () => {
  return (
    <div className="flex flex-1 h-full items-center justify-center">
      <h1 className="text-[24px] font-bold text-[#262D33] text-center">
        No tiene permiso para acceder a esta página
      </h1>
    </div>
  );
};

interface SidebarProps extends BoxProps {
  onClose: () => void;
}

const SidebarContent = ({ onClose, ...rest }: SidebarProps) => {
  const { user: currentUser } = useCurrentUser();
  const ability = usePermissions();
  return (
    <Box
      as="nav"
      transition=".5s ease"
      bg={useColorModeValue('white', 'gray.900')}
      borderRight="1px"
      position="sticky"
      left={0}
      borderRightColor={useColorModeValue('gray.200', 'gray.700')}
      w={{ base: '250px', md: '300px' }}
      // className="w-full"
      pos="fixed"
      h="full"
      overflowY="auto"
      style={{ scrollbarWidth: 'thin' }}
      {...rest}
    >
      <Flex
        h="60px"
        alignItems="center"
        px="8"
        mb="4"
        justifyContent="space-between"
        borderBottom="1px solid"
        borderColor="gray.200"
        flexShrink={0}
      >
        {/* <Text fontSize="2xl" fontFamily="monospace" fontWeight="bold">
          Logo
        </Text> */}
        <div className=" w-[70px] h-[70px] flex items-center ">
          <Image alt="logo" width="1000" height="1000" priority src="/images/Logo.png" />
        </div>
        <CloseButton display={{ base: 'flex', md: 'none' }} onClick={onClose} />
      </Flex>
      {canPerformAny(ability, Sections.Dashboard) && (
        <NavItem icon={FiHome} link={Paths.dashboard_default}>
          Dashboard
        </NavItem>
      )}
      {(canPerformAny(ability, Sections.UserManagement) || currentUser.role === Roles.Superadmin) && (
        <UsersNavItems />
      )}
      {canPerformAny(ability, Sections.Calendar) && <CalendarNavItems />}
      {canPerformAny(ability, Sections.Clients) && <ClientesNavItems />}
      {canPerformAny(ability, Sections.Fleet) && <FlotillaNavItems />}
      {canPerformAny(ability, Sections.Payments) && <PagosNavItems />}
    </Box>
  );
};

interface MobileProps extends FlexProps {
  onOpen: () => void;
}

const MobileNav = ({ onOpen, ...rest }: MobileProps) => {
  const router = useRouter();
  const toast = useToast();
  const { user } = useCurrentUser();

  const handleLogOut = async () => {
    try {
      const response = await axios.post(
        `${URL_API}/auth/logout`,
        {},
        {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        }
      );
      if (response.status === 200) {
        toast({
          status: 'info',
          title: 'Cerrando sesión...',
          isClosable: true,
          position: 'top',
          duration: 3000,
        });
        setTimeout(async () => {
          await signOut({
            redirect: true,
            callbackUrl: '/dashboard',
          });
        }, 3000);
      }
    } catch (error) {
      let errorMessage = 'Hubo un problema al cerrar sesión. Por favor, inténtalo de nuevo.';

      if (axios.isAxiosError(error) && error.response) {
        errorMessage = error?.response?.data?.message || errorMessage;
      }

      toast({
        status: 'error',
        title: 'Error al cerrar sesión',
        description: errorMessage,
        isClosable: true,
        position: 'top',
        duration: 5000,
      });
    }
  };

  const ability = usePermissions();
  const canSearch = canPerform(ability, Capabilities.Search, Sections.GlobalSearch, Subsections.Empty);

  return (
    <Flex
      as="nav"
      ml={{ base: 0, md: 60 }}
      px={{ base: 4, md: 4 }}
      height="60px"
      alignItems="center"
      position="sticky"
      zIndex={20}
      top={0}
      bg={useColorModeValue('white', 'gray.900')}
      borderBottomWidth="1px"
      borderBottomColor={useColorModeValue('gray.200', 'gray.700')}
      justifyContent={{ base: 'space-between', md: 'space-between' }}
      {...rest}
    >
      {canSearch && <VehicleSearch />}
      <IconButton
        display={{ base: 'flex', md: 'none' }}
        onClick={onOpen}
        variant="outline"
        aria-label="open menu"
        icon={<FiMenu />}
      />

      <Image alt="logo" src="/images/Logo.png" width="70" height="70" />

      <HStack spacing={{ base: '0', md: '6' }}>
        <Flex alignItems={'center'}>
          <Menu>
            <MenuButton py={3} pr={4} transition="all 0.3s" _focus={{ boxShadow: 'none' }}>
              <HStack>
                <VStack display={{ base: 'none', md: 'flex' }} alignItems="flex-start" spacing="1px" ml="2">
                  <Text fontSize="sm">{user?.name} </Text>
                </VStack>
                <Avatar
                  size={'sm'}
                  ml="10px"
                  src={
                    user.image.url ||
                    'https://w7.pngwing.com/pngs/81/570/png-transparent-profile-logo-computer-icons-user-user-blue-heroes-logo-thumbnail.png'
                  }
                />
              </HStack>
            </MenuButton>
            <MenuList
              bg={useColorModeValue('white', 'gray.900')}
              borderColor={useColorModeValue('gray.200', 'gray.700')}
            >
              <MenuItem onClick={() => router.push('/dashboard/perfil')}>Perfil</MenuItem>
              <MenuDivider />
              <MenuItem onClick={() => handleLogOut()}>Cerrar Sesión</MenuItem>
            </MenuList>
          </Menu>
        </Flex>
      </HStack>
    </Flex>
  );
};
