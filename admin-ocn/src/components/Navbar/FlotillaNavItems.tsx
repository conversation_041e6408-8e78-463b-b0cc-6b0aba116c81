import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import React from 'react';
import {
  FaCarSide,
  FaChevronDown,
  FaFileInvoice,
  FaRegCheckCircle,
  FaMoneyBill,
  FaQrcode,
} from 'react-icons/fa';
import { FiHome } from 'react-icons/fi';
import { RiArrowDownSFill } from 'react-icons/ri';
import InactivoNavItems from './InactivoNavItems';
import useSidebarStore from '@/zustand/sidebarStore';
import {
  StatusTranslations,
  StatusTranslationsMX,
} from '@/app/dashboard/flotilla/components/translations/statusTranslations';
import { Capabilities, Paths, Sections, Subsections } from '@/constants';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';

const FlotillaNavItems = () => {
  const ability = usePermissions();
  const { user: currentUser } = useCurrentUser();
  const vehicleStatusCounts = useSidebarStore((state) => state.vehicleStatusCounts);
  const pathname = usePathname();

  const search = useSearchParams();

  const country = search ? search.get('country') : '';

  const isMexico = country === 'Mexico';
  const statusTranslations = isMexico ? StatusTranslationsMX : StatusTranslations;
  const navButton = {
    icon: <FaCarSide size={18} />,
    name: 'Flotilla',
  };
  const navLinks = [
    {
      link: '/dashboard/flotilla/activos',
      icon: <FaRegCheckCircle size={16} />,
      name: 'Activos',
      key: '',
    },
    {
      link: '/dashboard/flotilla/invoiced',
      icon: <FaFileInvoice size={16} />,
      name: 'Facturados',
      key: '',
    },
    {
      link: '/dashboard/flotilla/stock',
      icon: <FiHome size={16} />,
      name: 'Stock',
      key: '',
    },
    {
      link: '/dashboard/flotilla/reingresos',
      icon: <FaCarSide size={16} />,
      name: 'Reingresos',
      key: '',
    },
    {
      link: '/dashboard/flotilla/bajas',
      // icon: <FaCarSide size={16} />,
      icon: <RiArrowDownSFill size={16} />,
      name: 'Bajas',
      key: '',
    },
    {
      link: Paths.fleet_active,
      icon: <FaRegCheckCircle size={16} />,
      name: `${statusTranslations.active}${
        currentUser?.role !== 'auditor' ? ` (${vehicleStatusCounts?.active ?? 0})` : ''
      }`,
      key: Subsections.Active,
    },
    {
      link: '/dashboard/flotilla/sold',
      icon: <FaMoneyBill size={16} />,
      name: 'Vendidos',
      key: '',
    },
    {
      link: '/dashboard/flotilla/codigos-qr-vehiculos',
      icon: <FaQrcode size={16} />,
      name: 'Códigos QR de vehículos',
      key: Subsections.QRScan,
    },
  ]
    .filter((item) => canPerform(ability, Capabilities.View, Sections.Fleet, item.key))
    .map((item) => ({
      ...item,
      link: country ? `${item.link}?country=${encodeURI(country)}` : item.link,
    }));

  return (
    <details
      open={pathname?.includes('/dashboard/flotilla')}
      className="group transition-all duration-150 ml-4 content-center h-auto open:h-auto overflow-visible "
    >
      {pathname?.includes('/dashboard/flotilla') ? (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2 bg-primaryPurple text-white">
          {navButton.icon}
          <span className="text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 text-white ">
            {navButton.name}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={'white'} />
          </span>
        </summary>
      ) : (
        <summary
          className={`group transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2`}
        >
          <FaCarSide size={18} />
          <span className="text-gray-600 text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 ">
            {' '}
            Flotilla{' '}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={pathname?.includes('/dashboard/flotilla') ? 'white' : 'black'} />
          </span>
        </summary>
      )}

      <nav className="mt-1.5 ml-8 flex flex-col transition-all duration-500 ">
        {navLinks.map((item, key) => {
          return (
            <Link href={item.link} key={key} prefetch={false}>
              <button
                className={
                  pathname?.includes(item.link?.split('?')[0])
                    ? 'flex items-center rounded-lg px-4 py-2 text-white bg-primaryPurple'
                    : 'flex items-center rounded-lg px-4 py-2 hover:bg-gray-100 hover:text-gray-700'
                }
                style={{ width: '96%' }}
                key={key}
              >
                {item.icon}
                <span className="ml-3 text-sm font-medium"> {item.name} </span>
              </button>
            </Link>
          );
        })}
        {canPerform(ability, Capabilities.View, Sections.Fleet, Subsections.Inactive) && <InactivoNavItems />}
      </nav>
    </details>
  );
};

export default FlotillaNavItems;
