import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';
import { Countries, teamLeads } from '@/constants';

export const createStockSchema = Yup.object().shape({
  vehicleState: createSelectInputValidator('Ciudad del vehículo requerido'),
  brand: createSelectInputValidator('Marca requerida'),
  model: createSelectInputValidator('Modelo requerido'),
  version: createSelectInputValidator('Versión requerido'),
  year: createSelectInputValidator('Año requerido'),
  // color: Yup.string().required('Color requerido'),
  color: createSelectInputValidator('Color requerido'),
  km: Yup.string().when('country', {
    is: (country: any) => country.value === Countries.Mexico,
    then: Yup.string().required('Kilometros requeridos'),
    otherwise: Yup.string().notRequired(),
  }),
  vin: Yup.string().required('Vin requerido'),
  owner: Yup.string().required('Dueño requerido'),
  billAmount: Yup.string().required('Valor de factura requerida'),
  billNumber: Yup.string().required('Número de factura requerido'),
  billDate: Yup.string()
    .max(10, 'Fecha inválida')
    .min(10, 'Fecha inválida')
    .matches(
      /^(?:19|20)\d\d-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\d|2\d|3[01])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)$/,
      'Fecha inválida'
    )
    .test('year', 'Fecha inválida', (value) => {
      if (!value) return true;
      const year = parseInt(value.split('-')[0], 10);
      return year >= 1900;
    })
    .required('Fecha de factura requerida'),

  receptionDate: Yup.string()
    .max(10, 'Fecha inválida')
    .min(10, 'Fecha inválida')
    .matches(
      /^(?:19|20)\d\d-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\d|2\d|3[01])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)$/,
      'Fecha inválida'
    )
    .test('year', 'Fecha inválida', (value) => {
      if (!value) return true;
      const year = parseInt(value.split('-')[0], 10);
      return year >= 1900; /* && year <= new Date().getFullYear(); */
    })
    .nullable(),

  mi: Yup.string().when('country', {
    is: (country: any) => country.value === Countries['United States'],
    then: Yup.string().required('Miles is required'),
    otherwise: Yup.string().notRequired(),
  }),
});

export const createStockSchemaUS = Yup.object().shape({
  vehicleState: createSelectInputValidator('Ciudad del vehículo requerido'),
  brand: Yup.string().required('Marca requerida'),
  model: Yup.string().required('Modelo requerido'),
  version: Yup.string().required('Versión requerido'),
  year: Yup.string().required('Año requerido'),
  // color: Yup.string().required('Color requerido'),
  color: createSelectInputValidator('Color requerido'),
  km: Yup.string().when('country', {
    is: (country: any) => country.value === Countries.Mexico,
    then: Yup.string().required('Kilometros requeridos'),
    otherwise: Yup.string().notRequired(),
  }),
  vin: Yup.string().required('Vin requerido'),
  owner: Yup.string().required('Dueño requerido'),
  billAmount: Yup.string().required('Valor de factura requerida'),
  billNumber: Yup.string().required('Número de factura requerido'),
  billDate: Yup.string()
    .max(10, 'Fecha inválida')
    .min(10, 'Fecha inválida')
    .matches(
      /^(?:19|20)\d\d-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\d|2\d|3[01])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)$/,
      'Fecha inválida'
    )
    .test('year', 'Fecha inválida', (value) => {
      if (!value) return true;
      const year = parseInt(value.split('-')[0], 10);
      return year >= 1900;
    })
    .required('Fecha de factura requerida'),

  receptionDate: Yup.string()
    .max(10, 'Fecha inválida')
    .min(10, 'Fecha inválida')
    .matches(
      /^(?:19|20)\d\d-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\d|2\d|3[01])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)$/,
      'Fecha inválida'
    )
    .test('year', 'Fecha inválida', (value) => {
      if (!value) return true;
      const year = parseInt(value.split('-')[0], 10);
      return year >= 1900; /* && year <= new Date().getFullYear(); */
    })
    .nullable(),

  mi: Yup.string().when('country', {
    is: (country: any) => country.value === Countries['United States'],
    then: Yup.string().required('Miles is required'),
    otherwise: Yup.string().notRequired(),
  }),
});

export const createFilterSchema = Yup.object().shape({
  region: createSelectInputValidator('Region requerida'),
});

export const receptionDateSchema = (initialReceptionDate: string, userEmail: string) =>
  Yup.object().shape({
    receptionDate: Yup.date()
      .nullable()
      .test(
        'reception-date-clear',
        'La fecha de recepción no se puede eliminar en este momento.',
        function (value) {
          if (initialReceptionDate && !value) {
            return false;
          }
          return true;
        }
      )
      .test('max-year', 'El año debe ser menor que 2100.', function (value) {
        if (!value) return true;
        const year = new Date(value).getFullYear();
        return year < 2100;
      })
      .test('user-permission', 'No tienes permiso para borrar la fecha de recepción.', function (value) {
        const isNotTeamLead = !teamLeads.includes(userEmail);
        if (!value && isNotTeamLead) {
          return false;
        }
        return true;
      })
      .test('min-date', 'La fecha de recepción debe ser posterior al 1 de enero de 2022.', function (value) {
        if (!value) return true;
        const minDate = new Date('2022-01-01');
        return value >= minDate;
      }),
  });
