import axios from 'axios';
import {
  GPS_API_TOKEN,
  GPS_API_URL,
  GPS_API_PASSWORD,
  GPS_API_USERNAME,
  GPS_API_EVENTS,
} from '@/constants';
import { ConsoleLogger, Injectable, Logger } from '@nestjs/common';

@Injectable()
export class GpsService {
  private readonly logger = new Logger(GpsService.name);
  constructor() {}

  async getGPSToken() {
    try {
      const response = await axios.post(
        GPS_API_TOKEN,
        {
          body: {
            user: GPS_API_USERNAME,
            password: GPS_API_PASSWORD,
          },
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      const parsedData = JSON.parse(response.data.body);
      if (parsedData?.token) {
        return parsedData.token;
      }

      return '';
    } catch (error: any) {
      this.logger.error(`[GpsService] getGPSToken error: ,  ${error}`);

      return '';
    }
  }

  async gpsStatus(gps: string) {
    try {
      const gpsToken = await this.getGPSToken();
      const headers = {
        'Content-Type': 'application/json',
        Authorization: gpsToken,
      };

      const data = {
        body: {
          reportType: 'engineStatus',
          name: gps,
        },
      };

      const response = await axios.post(GPS_API_URL, data, {headers});

      return this.extractParsedData(response);
    } catch (error: any) {
      this.logger.error(`[GpsService] gpsStatus - error geting the status of: ${gps} error: ${error}`);
      throw new Error(error);
    }
  }

  async setUnlockEvent(gps: string) {
    try {
      const gpsToken = await this.getGPSToken();
      const headers = {
        'Content-Type': 'application/json',
        Authorization: gpsToken,
      };
      const body = { name: gps, command: 'unlock' };

      const response = await axios.post(GPS_API_EVENTS, body, {headers});
      const message = response.data;

      return message;
    } catch (error: any) {
      this.logger.error(`[GpsService] setUnlockEvent - error setting events of: ${gps} error: ${error}`);
      return "Internal error";
    }
  }

  async setWakeupEvent(gps: string) {
    try {
      const gpsToken = await this.getGPSToken();
      const headers = {
        'Content-Type': 'application/json',
        Authorization: gpsToken,
      };
      const body = { name: gps, command: 'wakeup' };

      const response = await axios.post(GPS_API_EVENTS, body, {headers});
      const message = response.data;

      return message;
    } catch (error: any) {
      this.logger.error(`[GpsService] setWakeupEvent - error setting wakeup events of: ${gps} error: ${error}`);
      return "Internal error";
    }
  }

  async setBlockEvent(gps: string) {
    try {
      const gpsToken = await this.getGPSToken();
      const headers = {
        'Content-Type': 'application/json',
        Authorization: gpsToken,
      };
      const body = { name: gps, command: 'lock' };

      const response = await axios.post(GPS_API_EVENTS, body, {headers});
      const message = response.data;

      return message;
    } catch (error: any) {
      this.logger.error(`[GpsService] setBlockEvent - error setting block events of: ${gps} error: ${error}`);
      return "Internal error";
    }
  }

  async extractParsedData(response: any) {
    let body = response?.data?.body;

    // Handle case where body is a string
    if (typeof body === 'string') {
      try {
        body = JSON.parse(body);
      } catch (error: any) {
        this.logger.error(`[GpsService] extractParsedData - error: ${error}`);
        return null;
      }
    }

    // body should now be an object
    const parsedData = body?.parsedData;

    if (!parsedData || Object.keys(parsedData).length === 0) {
      console.warn('No parsedData found in response.');
      return null;
    }

    // If parsedData has keys, return the first value (or process as needed)
    const firstKey = Object.keys(parsedData)[0];
    return parsedData[firstKey]; // or return parsedData if you need full object
  }
}
