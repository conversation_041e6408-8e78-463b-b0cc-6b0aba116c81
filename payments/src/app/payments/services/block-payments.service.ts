import {
  NODE_ENV,
} from '@/constants';
import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual } from 'typeorm';
import {
  PaymentsEntity,
  PaymentStatusEnum,
} from '../entities/payment.entity';
import { StockVehicleService } from './stock.service';
import { GpsService } from './gps.service';
import { slackApi } from '@/logger.config';

const MX_CLIENTS_BLOCK_PAYMENTS_CRON_EVERY_TUESDAY_NINE_AM =
  NODE_ENV !== 'production' ? '0 9 * * 2' : '0 9 * * 2';
const timeZone =
  NODE_ENV !== 'production' ? 'Asia/Karachi' : 'America/Mexico_City';

@Injectable()
export class BlockPaymentsService {
  private readonly logger = new Logger(BlockPaymentsService.name);

  constructor(
    @InjectRepository(PaymentsEntity)
    private readonly paymentsRepository: Repository<PaymentsEntity>,
    private readonly stockVehicleService: StockVehicleService,
    private readonly gpsService: GpsService,
  ) {}

  @Cron(MX_CLIENTS_BLOCK_PAYMENTS_CRON_EVERY_TUESDAY_NINE_AM, {
    timeZone,
  })
  async handleBlockPaymentsTuesdayNineAM() {
    if (process.env.RUN_MX_CRON_JOB === 'true') {
      this.logger.log(
        '[BlockPaymentsService] handleBlockPaymentsTuesdayNineAM - Running MX Clients Block Payments Cron Job at 9:00am',
      );
      await this.processBlockPayments();
    }
  }

  async processBlockPayments() {
    try {
      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - cron job started at ${new Date()}`,
      );

      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - searching for payments with dateLimit before ${yesterday.toISOString()}`,
      );

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - executing database query for unpaid MX payments`,
      );

      const unpaidPayments = await this.paymentsRepository.find({
        where: {
          isPaid: false,
          status: PaymentStatusEnum.PENDING,
          dateLimit: LessThanOrEqual(yesterday),
          client: {
            country: 'MX',
          },
        },
        relations: ['client'],
        take: 30,
      });

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - database query completed, found ${unpaidPayments?.length || 0} records`,
      );

      if (!unpaidPayments || unpaidPayments.length === 0) {
        this.logger.log(
          `[BlockPaymentsService] processBlockPayments - no unpaid payments found`,
        );
        return;
      }

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - found ${unpaidPayments.length} unpaid payments for blocking (pilot test)`,
      );

      const blockResults = {
        success: [],
        failed: [],
      };

      // Preparar datos de vehículos para procesamiento en lote
      const vehiclesData = [];
      
      for (const payment of unpaidPayments) {
        try {
          const stockVehicle = await this.stockVehicleService.getStockVehicleByAssociateId(
            payment.client.associateId,
          );

          if (!stockVehicle?.data?.gpsNumber && !stockVehicle?.data?.carNumber) {
            this.logger.warn(
              `[BlockPaymentsService] processBlockPayments - GPS number and car number not found for associate ${payment.client.associateId}`,
            );
            blockResults.failed.push({
              paymentId: payment.id,
              associateId: payment.client.associateId,
              reason: 'GPS number and car number not found',
            });
            continue;
          }

          const gpsNumber = stockVehicle.data.gpsNumber || stockVehicle.data.carNumber;
          const contractNumber = stockVehicle.data.carNumber || 'N/A';
          const plates = stockVehicle.data.carPlates?.plates || 'N/A';

          vehiclesData.push({
            payment,
            gpsNumber,
            contractNumber,
            plates,
          });

        } catch (error: any) {
          this.logger.error(
            `[BlockPaymentsService] processBlockPayments - error getting vehicle data for payment ${payment.id}`,
            { error: error?.message || error }
          );
          blockResults.failed.push({
            paymentId: payment.id,
            associateId: payment.client.associateId,
            reason: error?.message || 'Error getting vehicle data',
          });
        }
      }

      if (vehiclesData.length === 0) {
        this.logger.log(
          `[BlockPaymentsService] processBlockPayments - no valid vehicles to process`,
        );
        return { data: blockResults, status: true };
      }

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - starting batch processing for ${vehiclesData.length} vehicles`,
      );

      // FASE 1: Enviar comandos wakeup en paralelo
      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - FASE 1: sending wakeup commands to ${vehiclesData.length} vehicles`,
      );
      
      const wakeupPromises = vehiclesData.map(async (vehicleData) => {
        try {
          const wakeupResponse = await this.gpsService.setWakeupEvent(vehicleData.gpsNumber);
          this.logger.log(
            `[BlockPaymentsService] processBlockPayments - wakeup sent to GPS ${vehicleData.gpsNumber}`,
          );
          return { success: true, gpsNumber: vehicleData.gpsNumber, response: wakeupResponse };
        } catch (error: any) {
          this.logger.error(
            `[BlockPaymentsService] processBlockPayments - wakeup failed for GPS ${vehicleData.gpsNumber}`,
            { error: error?.message || error }
          );
          return { success: false, gpsNumber: vehicleData.gpsNumber, error: error?.message };
        }
      });

      const wakeupResults = await Promise.all(wakeupPromises);
      const successfulWakeups = wakeupResults.filter(r => r.success);
      
      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - wakeup phase completed: ${successfulWakeups.length}/${vehiclesData.length} successful`,
      );

      // Esperar 1 minuto después de los wakeups
      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - waiting 1 minute after wakeup commands`,
      );
      await new Promise(resolve => setTimeout(resolve, 60000)); // 1 minuto

      // FASE 2: Enviar comandos de bloqueo en paralelo
      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - FASE 2: sending block commands to ${vehiclesData.length} vehicles`,
      );
      
      const blockPromises = vehiclesData.map(async (vehicleData) => {
        try {
          const blockResponse = await this.gpsService.setBlockEvent(vehicleData.gpsNumber);
          this.logger.log(
            `[BlockPaymentsService] processBlockPayments - block command sent to GPS ${vehicleData.gpsNumber}`,
          );
          return { success: true, gpsNumber: vehicleData.gpsNumber, response: blockResponse };
        } catch (error: any) {
          this.logger.error(
            `[BlockPaymentsService] processBlockPayments - block command failed for GPS ${vehicleData.gpsNumber}`,
            { error: error?.message || error }
          );
          return { success: false, gpsNumber: vehicleData.gpsNumber, error: error?.message };
        }
      });

      const blockCommandResults = await Promise.all(blockPromises);
      
      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - block commands completed, waiting 2 minutes for verification`,
      );

      // Esperar 2 minutos después de los comandos de bloqueo
      await new Promise(resolve => setTimeout(resolve, 120000)); // 2 minutos

      // FASE 3: Verificar estado de bloqueo en paralelo
      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - FASE 3: verifying block status for ${vehiclesData.length} vehicles`,
      );
      
      const verificationPromises = vehiclesData.map(async (vehicleData) => {
        try {
          const finalStatus = await this.gpsService.gpsStatus(vehicleData.gpsNumber);
          const isBlocked = finalStatus && (finalStatus.engineBlock === '1' || finalStatus.engineBlock === 1);
          
          this.logger.log(
            `[BlockPaymentsService] processBlockPayments - GPS ${vehicleData.gpsNumber} verification: ${isBlocked ? 'BLOCKED' : 'NOT BLOCKED'}`,
          );

          if (isBlocked) {
            blockResults.success.push({
              paymentId: vehicleData.payment.id,
              associateId: vehicleData.payment.client.associateId,
              gpsNumber: vehicleData.gpsNumber,
              contractNumber: vehicleData.contractNumber,
              plates: vehicleData.plates,
              status: 'blocked_successfully',
            });
          } else {
            blockResults.failed.push({
              paymentId: vehicleData.payment.id,
              associateId: vehicleData.payment.client.associateId,
              gpsNumber: vehicleData.gpsNumber,
              contractNumber: vehicleData.contractNumber,
              plates: vehicleData.plates,
              reason: 'Vehicle not blocked after commands',
            });
          }

        } catch (error: any) {
          this.logger.error(
            `[BlockPaymentsService] processBlockPayments - verification failed for GPS ${vehicleData.gpsNumber}`,
            { error: error?.message || error }
          );
          blockResults.failed.push({
            paymentId: vehicleData.payment.id,
            associateId: vehicleData.payment.client.associateId,
            gpsNumber: vehicleData.gpsNumber,
            contractNumber: vehicleData.contractNumber,
            plates: vehicleData.plates,
            reason: error?.message || 'Verification error',
          });
        }
      });

      await Promise.all(verificationPromises);

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - sending Slack report with ${blockResults.success.length} successful and ${blockResults.failed.length} failed results`,
      );

      await this.sendBlockingReport(blockResults);

      this.logger.log(
        `[BlockPaymentsService] processBlockPayments - completed. ${blockResults.success.length} successful, ${blockResults.failed.length} failed`,
      );

      return {
        data: blockResults,
        status: true,
      };

    } catch (error: any) {
      this.logger.error(
        `[BlockPaymentsService] processBlockPayments - internal server error`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
      return {
        message: 'Internal server error',
        status: false,
      };
    }
  }

  async blockVehicle(gpsNumber: string) {
    try {
      this.logger.log(
        `[BlockPaymentsService] blockVehicle - Starting block process for GPS ${gpsNumber}`,
      );

      // Enviar comando wakeup
      this.logger.log(
        `[BlockPaymentsService] blockVehicle - Sending wakeup command for GPS ${gpsNumber}`,
      );

      const wakeupResponse = await this.gpsService.setWakeupEvent(gpsNumber);

      if (wakeupResponse) {
        this.logger.log(
          `[BlockPaymentsService] blockVehicle - GPS ${gpsNumber} wakeup command response: ${JSON.stringify(wakeupResponse)}`,
        );
      }

      // Esperar 5 minutos después del wakeup
      this.logger.log(
        `[BlockPaymentsService] blockVehicle - Waiting 5 minutes after wakeup command for GPS ${gpsNumber}`,
      );
      await new Promise(resolve => setTimeout(resolve, 300000)); // 5 minutos = 300000ms

      // Enviar comando de bloqueo
      this.logger.log(
        `[BlockPaymentsService] blockVehicle - Sending block command for GPS ${gpsNumber}`,
      );

      const blockResponse = await this.gpsService.setBlockEvent(gpsNumber);

      if (blockResponse) {
        this.logger.log(
          `[BlockPaymentsService] blockVehicle - GPS ${gpsNumber} block command response: ${JSON.stringify(blockResponse)}`,
        );
      }

      // Esperar 5 segundos después del bloqueo
      await new Promise(resolve => setTimeout(resolve, 5000));

      const finalStatus = await this.gpsService.gpsStatus(gpsNumber);

      if (finalStatus) {
        this.logger.log(
          `[BlockPaymentsService] blockVehicle - GPS ${gpsNumber} final status: ${JSON.stringify(finalStatus)}`,
        );

        if (finalStatus.engineBlock === '1' || finalStatus.engineBlock === 1) {
          return {
            success: true,
            message: 'Vehicle blocked successfully',
          };
        }
      }

      return {
        success: false,
        reason: 'Block command failed - vehicle not blocked',
      };

    } catch (error: any) {
      this.logger.error(
        `[BlockPaymentsService] blockVehicle - error blocking GPS ${gpsNumber}`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
      return {
        success: false,
        reason: error?.message || 'Unknown error during blocking',
      };
    }
  }

  async sendBlockingReport(blockResults: any) {
    try {
      this.logger.log(
        `[BlockPaymentsService] sendBlockingReport - starting to send Slack report`,
      );

      const { success, failed } = blockResults;
      const totalProcessed = success.length + failed.length;

      this.logger.log(
        `[BlockPaymentsService] sendBlockingReport - preparing message for ${totalProcessed} total processed (${success.length} success, ${failed.length} failed)`,
      );

      let message = `*🚗 Block Payments Report - ${new Date().toLocaleDateString()}*\n\n`;
      message += `*Total Processed:* ${totalProcessed}\n`;
      message += `*Successfully Blocked:* ${success.length}\n`;
      message += `*Failed to Block:* ${failed.length}\n\n`;

      if (success.length > 0) {
        message += `*🚗 Vehicles Successfully Blocked:*\n`;
        success.forEach((item, index) => {
          message += `${index + 1}. Contract: ${item.contractNumber}, Plates: ${item.plates}, GPS: ${item.gpsNumber}\n`;
        });
        message += `\n`;
      }

      if (failed.length > 0) {
        message += `*❌ Failed to Block Vehicles:*\n`;
        failed.forEach((item, index) => {
          message += `${index + 1}. Contract: ${item.contractNumber || 'N/A'}, Plates: ${item.plates}, GPS: ${item.gpsNumber}, Reason: ${item.reason}\n`;
        });
      }

      this.logger.log(
        `[BlockPaymentsService] sendBlockingReport - sending message to Slack: ${message}`,
      );

      await this.sendMessageOnSlack(message);

      this.logger.log(
        `[BlockPaymentsService] sendBlockingReport - report sent to Slack successfully`,
      );

    } catch (error: any) {
      this.logger.error(
        `[BlockPaymentsService] sendBlockingReport - error sending report`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
    }
  }

  async sendMessageOnSlack(message: string) {
    this.logger.log(
      `[BlockPaymentsService] sendMessageOnSlack - preparing to send message to channel: ${process.env.ACTIVATION_PAYMENT_CHANNEL}`,
    );

    try {
      const response = await slackApi.chat.postMessage({
        text: message,
        channel: process.env.ACTIVATION_PAYMENT_CHANNEL!,
      });

      this.logger.log(
        `[BlockPaymentsService] sendMessageOnSlack - message sent successfully. Response: ${JSON.stringify(response)}`,
      );
    } catch (error: any) {
      this.logger.error(
        `[BlockPaymentsService] sendMessageOnSlack - error sending message to Slack`,
        {
          error: error?.message || error,
          stack: error?.stack,
        },
      );
      throw error;
    }
  }
}
