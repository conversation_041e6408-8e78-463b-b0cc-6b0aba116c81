import React, { useState, useEffect } from 'react';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Checkbox } from '../../../components/ui/checkbox';
import { Upload, CheckCircle, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import { actions } from 'astro:actions';
import { 
  calculateNextVerificationDate, 
  getHologramColor, 
  getVerificationMonthsByPlate,
  type HologramType 
} from '../../../utils/verification';
import { appointmentService } from '../../../actions/appointmentService';

interface VerificationFormProps {
  plate: string;
}

interface FileWithPreview extends File {
  preview?: string;
}

export default function VerificationForm({ plate }: VerificationFormProps) {
  const [certificateFile, setCertificateFile] = useState<FileWithPreview | null>(null);
  const [hologramFile, setHologramFile] = useState<FileWithPreview | null>(null);
  const [nextVerificationDate, setNextVerificationDate] = useState('');
  const [hologramType, setHologramType] = useState<HologramType>('00');
  const [isHybridElectric, setIsHybridElectric] = useState(false);
  const [registrationDate, setRegistrationDate] = useState('');
  const [calculatedNextDate, setCalculatedNextDate] = useState<Date | null>(null);
  const [isExempt, setIsExempt] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isCheckingElectric, setIsCheckingElectric] = useState(true);
  const [apiElectricStatus, setApiElectricStatus] = useState<boolean | null>(null);
  const [vehicleNotFound, setVehicleNotFound] = useState(false);

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>, 
    setFile: React.Dispatch<React.SetStateAction<FileWithPreview | null>>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const fileWithPreview = file as FileWithPreview;
      fileWithPreview.preview = URL.createObjectURL(file);
      setFile(fileWithPreview);
    }
  };

  // Check if vehicle is electric when component mounts
  useEffect(() => {
    const checkElectricStatus = async () => {
      if (plate) {
        try {
          const result = await appointmentService.checkIsElectric(plate);
          
          // Check for 404 response with vehicle not found message
          if (!result.success && result.data?.message === "Vehículo no encontrado") {
            setVehicleNotFound(true);
          } else if (result.success && result.data) {
            const isElectric = result.data.isElectric;
            setApiElectricStatus(isElectric);
            
            if (isElectric) {
              setIsHybridElectric(true);
              // Set today's date as registration date for electric vehicles
              const today = new Date().toISOString().split('T')[0];
              setRegistrationDate(today);
            }
          }
        } catch (error) {
          console.log('Error checking electric status:', error);
        } finally {
          setIsCheckingElectric(false);
        }
      } else {
        setIsCheckingElectric(false);
      }
    };

    checkElectricStatus();
  }, [plate]);

  // Calculate next verification date when hologram type or registration date changes
  useEffect(() => {
    if (plate) {
      const regDate = registrationDate ? new Date(registrationDate) : undefined;
      const schedule = calculateNextVerificationDate(plate, hologramType, new Date(), regDate, isHybridElectric);
      setCalculatedNextDate(schedule.nextVerificationDate);
      setIsExempt(schedule.isExempt);
      
      // Auto-fill the next verification date field
      if (schedule.nextVerificationDate && !isNaN(schedule.nextVerificationDate.getTime())) {
        const dateString = schedule.nextVerificationDate.toISOString().split('T')[0];
        setNextVerificationDate(dateString);
      }
    }
  }, [plate, hologramType, registrationDate, isHybridElectric]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!certificateFile) {
      toast.error('Por favor sube el certificado de verificación');
      return;
    }

    if (!hologramFile) {
      toast.error('Por favor sube la imagen del holograma');
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append('plate', plate);
      formData.append('certificate', certificateFile);
      formData.append('nextVerificationDate', nextVerificationDate);
      formData.append('hologramType', hologramType);
      formData.append('isHybridElectric', isHybridElectric.toString());
      formData.append('registrationDate', registrationDate || new Date().toISOString().split('T')[0]);
      
      if (hologramFile) {
        formData.append('hologram', hologramFile);
      }

      const result = await actions.submitVerification(formData);
      
      if (result.data?.success) {
        setIsCompleted(true);
        toast.success('Verificación completada exitosamente');
      } else {
        toast.error(result.data?.error || 'Error al enviar la verificación');
      }
    } catch (error) {
      toast.error('Error al enviar la verificación. Intenta nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isCompleted) {
    return (
      <div className="text-center py-8 space-y-4">
        <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
        <h2 className="text-xl font-semibold text-gray-900">
          ¡Verificación Completada!
        </h2>
        <p className="text-gray-600 text-sm">
          Hemos recibido tu documentación para el vehículo <br />
          <strong>{plate.toUpperCase()}</strong>
        </p>
        <p className="text-gray-500 text-xs">
          Te notificaremos cuando sea procesada.
        </p>
      </div>
    );
  }

  if (isCheckingElectric) {
    return (
      <div className="text-center py-8 space-y-4">
        <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mx-auto">
          <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">
          Verificando vehículo
        </h2>
        <p className="text-gray-600 text-sm">
          Consultando información para el vehículo <br />
          <strong>{plate.toUpperCase()}</strong>
        </p>
        <p className="text-gray-500 text-xs">
          Por favor espera un momento...
        </p>
      </div>
    );
  }

  if (vehicleNotFound) {
    return (
      <div className="text-center py-8 space-y-4">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
          <span className="text-2xl">❌</span>
        </div>
        <h2 className="text-xl font-semibold text-red-900">
          Vehículo no encontrado
        </h2>
        <p className="text-red-600 text-sm">
          No se pudo encontrar información para el vehículo <br />
          <strong>{plate.toUpperCase()}</strong>
        </p>
        <p className="text-red-500 text-xs">
          Verifica que las placas sean correctas o contacta soporte.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-xl font-bold text-gray-900">
          Confirmación de Verificación
        </h1>
        <p className="text-gray-600 text-sm">
          Vehículo: <span className="font-medium">{plate.toUpperCase()}</span>
        </p>
        <p className="text-gray-500 text-xs">
          Sube la documentación requerida
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Certificate Upload */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            📄 Certificado de Verificación *
          </Label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => handleFileChange(e, setCertificateFile)}
              className="hidden"
              id="certificate-upload"
            />
            <label htmlFor="certificate-upload" className="cursor-pointer block">
              {certificateFile ? (
                <div className="flex items-center justify-center space-x-2 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">{certificateFile.name}</span>
                </div>
              ) : (
                <div className="space-y-1">
                  <Upload className="w-6 h-6 text-gray-400 mx-auto" />
                  <p className="text-sm text-gray-600">Subir certificado</p>
                  <p className="text-xs text-gray-400">PDF, JPG, PNG</p>
                </div>
              )}
            </label>
          </div>
        </div>

        {/* Vehicle Type */}
        {apiElectricStatus === true && (
          <div className="flex items-center space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <Checkbox
              id="hybrid-electric"
              checked={true}
              disabled={true}
            />
            <Label htmlFor="hybrid-electric" className="text-sm text-gray-700">
              🔋 Vehículo Híbrido o Eléctrico (Confirmado por API)
            </Label>
          </div>
        )}
        
        {apiElectricStatus === false && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="text-sm text-gray-600">
              🚗 Vehículo de combustión interna (Confirmado por API)
            </div>
          </div>
        )}
        
        {apiElectricStatus === null && (
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <Checkbox
              id="hybrid-electric"
              checked={isHybridElectric}
              onCheckedChange={(checked: boolean) => {
                setIsHybridElectric(checked);
                if (checked) {
                  // Set today's date as registration date
                  const today = new Date().toISOString().split('T')[0];
                  setRegistrationDate(today);
                  
                  // Immediately calculate and update the next verification date
                  const todayDate = new Date();
                  const schedule = calculateNextVerificationDate(plate, hologramType, todayDate, todayDate, true);
                  setCalculatedNextDate(schedule.nextVerificationDate);
                  setIsExempt(schedule.isExempt);
                  
                  if (schedule.nextVerificationDate && !isNaN(schedule.nextVerificationDate.getTime())) {
                    const dateString = schedule.nextVerificationDate.toISOString().split('T')[0];
                    setNextVerificationDate(dateString);
                  }
                } else {
                  setRegistrationDate('');
                  
                  // Recalculate for non-hybrid vehicle
                  const schedule = calculateNextVerificationDate(plate, hologramType, new Date(), undefined, false);
                  setCalculatedNextDate(schedule.nextVerificationDate);
                  setIsExempt(schedule.isExempt);
                  
                  if (schedule.nextVerificationDate && !isNaN(schedule.nextVerificationDate.getTime())) {
                    const dateString = schedule.nextVerificationDate.toISOString().split('T')[0];
                    setNextVerificationDate(dateString);
                  }
                }
              }}
            />
            <Label htmlFor="hybrid-electric" className="text-sm text-gray-700">
              🔋 Vehículo Híbrido o Eléctrico
            </Label>
          </div>
        )}


        {/* Exemption Status */}
        {isExempt && isHybridElectric && registrationDate && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-green-600 font-medium">✅ Vehículo Exento</span>
            </div>
            <p className="text-sm text-green-700 mt-1">
              Tu vehículo híbrido/eléctrico está exento de verificación hasta 8 años después del registro.
            </p>
            <p className="text-xs text-green-600 mt-1">
              Solo necesitas subir el certificado para confirmar el estatus.
            </p>
          </div>
        )}


        {/* Hologram Type */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            🏷️ Tipo de Holograma
          </Label>
            <div className="grid grid-cols-2 gap-2">
              {(['00', '0', '1', '2'] as HologramType[]).map((type) => (
                <button
                  key={type}
                  type="button"
                  onClick={() => setHologramType(type)}
                  className={`p-3 rounded-lg border text-sm font-medium transition-colors ${
                    hologramType === type
                      ? 'bg-blue-50 border-blue-300 text-blue-700'
                      : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {type === '00' ? 'Holograma 00 (Exento 2 años)' : `Holograma ${type}`}
                </button>
              ))}
            </div>
            <div className="text-xs text-gray-500 space-y-1">
              <p>Color para placa {plate.toUpperCase()}: <strong>{getHologramColor(plate)}</strong></p>
              <p>Períodos de verificación: {(() => {
                const months = getVerificationMonthsByPlate(plate);
                return `${months[0]}-${months[1]} / ${months[2]}-${months[3]}`;
              })()}</p>
            </div>
        </div>

        {/* Next Verification Date */}
        <div className="space-y-2">
          <Label htmlFor="next-date" className="text-sm font-medium text-gray-700">
            📅 Verificar antes de
          </Label>
          <Input
            id="next-date"
            type="date"
            value={nextVerificationDate}
            onChange={(e) => setNextVerificationDate(e.target.value)}
            className="w-full"
            readOnly
          />
          <p className="text-xs text-gray-500">
            Calculada automáticamente según el tipo de holograma
          </p>
        </div>

        {/* Hologram Upload */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            📸 Imagen del Holograma *
          </Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
              <input
                type="file"
                accept=".jpg,.jpeg,.png"
                onChange={(e) => handleFileChange(e, setHologramFile)}
                className="hidden"
                id="hologram-upload"
              />
              <label htmlFor="hologram-upload" className="cursor-pointer block">
                {hologramFile ? (
                  <div className="space-y-2">
                    <div className="flex items-center justify-center space-x-2 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">{hologramFile.name}</span>
                    </div>
                    {hologramFile.preview && (
                      <img 
                        src={hologramFile.preview} 
                        alt="Preview" 
                        className="w-24 h-24 object-cover mx-auto rounded"
                      />
                    )}
                  </div>
                ) : (
                  <div className="space-y-1">
                    <Upload className="w-6 h-6 text-gray-400 mx-auto" />
                    <p className="text-sm text-gray-600">Subir imagen del holograma</p>
                    <p className="text-xs text-gray-400">JPG, PNG - Imagen clara</p>
                  </div>
                )}
              </label>
            </div>
        </div>

        {/* Submit Button */}
        <Button 
          type="submit" 
          className="w-full py-3"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Enviando...' : 'Completar Verificación'}
        </Button>

        <p className="text-xs text-gray-500 text-center">
          📱 Recibirás recordatorios diarios hasta completar la carga
        </p>
      </form>
    </div>
  );
}