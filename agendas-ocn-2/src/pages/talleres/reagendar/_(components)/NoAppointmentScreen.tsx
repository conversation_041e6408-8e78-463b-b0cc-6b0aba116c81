"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { AlertCircle, ArrowLeft, Phone } from "lucide-react"
import { useReschedule } from "./RescheduleContext"

export default function NoAppointmentScreen() {
  const { setCurrentStep, plates } = useReschedule()

  const goBack = () => {
    setCurrentStep(1)
  }

  return (
    <Card className="w-full">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 p-3 bg-orange-100 rounded-full w-fit">
          <AlertCircle className="h-8 w-8 text-orange-600" />
        </div>
        <CardTitle className="text-2xl font-bold text-gray-900">
          No hay cita agendada
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="text-center space-y-4">
          <p className="text-gray-600">
            No se encontró una cita activa para el vehículo con placas{" "}
            <span className="font-semibold text-gray-900">{plates}</span>
          </p>
          
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <p className="text-sm text-orange-800">
              Si crees que esto es un error o necesitas ayuda para reagendar tu cita, 
              por favor comunícate con nuestro equipo de soporte.
            </p>
          </div>
        </div>

        <div className="space-y-3">
          <Button 
            variant="outline"
            onClick={goBack}
            className="w-full"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Intentar con otras placas
          </Button>
          
          <Button 
            className="w-full bg-orange-600 hover:bg-orange-700"
            onClick={() => {
              // Aquí podrías agregar lógica para contactar soporte
              // Por ejemplo, abrir un modal de contacto o redirigir a WhatsApp
              window.open('tel:+525555555555', '_self')
            }}
          >
            <Phone className="mr-2 h-4 w-4" />
            Contactar Soporte
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
