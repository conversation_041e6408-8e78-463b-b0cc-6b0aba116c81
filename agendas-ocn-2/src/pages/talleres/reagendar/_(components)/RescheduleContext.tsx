"use client"

import React, { createContext, useContext, useState } from "react"

export interface Workshop {
  _id: string
  name: string
  location: {
    mapsLink: string
  }
  scheduleConfig: {
    weeklySchedule: {
      [key: string]: {
        start: string
        end: string
      }
    }
    timezone: string
    breakTime: {
      start: string
      end: string
    }
    capacity: {
      totalBays: number
      techniciansPerBay: number
    }
  }
}

export interface ServiceType {
  _id: string
  name: string
  description: string
  duration: number
  organization: string
  isActive: boolean
}

export interface Associate {
  _id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  city: string
  state: string
}

export interface StockVehicle {
  _id: string
  carNumber: string
  model: string
  brand: string
  version: string
  year: string
  vin: string
  vehicleState: string
  carPlates: {
    plates: string
  }
  km: number
  contractNumber: string
}

export interface AppointmentInfo {
  appointment?: {
    _id: string
    workshopId: string
    organizationId: string
    associateId: string
    stockId: string
    data: {
      registeredKm: number
      maintenanceNumber: number
      type: string
      urgencyLevel: string
    }
    startTime: string
    endTime: string
    status: string
    serviceTypeId: string
    duration: number
    statusHistory: any[]
    createdAt: string
    updatedAt: string
    __v: number
    organization: {
      _id: string
      globalScheduleConfig: any
    }
    workshop: Workshop
    serviceType: ServiceType
    associate: Associate
    stockVehicle: StockVehicle
    maintenanceKm: number
  }
}

interface RescheduleContextType {
  // Estado del flujo
  currentStep: number
  setCurrentStep: (step: number) => void

  // Datos del formulario
  plates: string
  setPlates: (plates: string) => void

  // Información de la cita
  appointmentInfo: AppointmentInfo | null
  setAppointmentInfo: (info: AppointmentInfo | null) => void

  // Selección de fecha y hora
  selectedDate: Date | null
  setSelectedDate: (date: Date | null) => void
  selectedSlot: string | null
  setSelectedSlot: (slot: string | null) => void

  // Slots disponibles
  availableSlots: string[]
  setAvailableSlots: (slots: string[]) => void

  // Estados de carga y error
  loading: boolean
  setLoading: (loading: boolean) => void
  error: string | null
  setError: (error: string | null) => void
}

const RescheduleContext = createContext<RescheduleContextType | undefined>(undefined)

export function RescheduleProvider({ children }: { children: React.ReactNode }) {
  const [currentStep, setCurrentStep] = useState(1)
  const [plates, setPlates] = useState("")
  const [appointmentInfo, setAppointmentInfo] = useState<AppointmentInfo | null>(null)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null)
  const [availableSlots, setAvailableSlots] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  return (
    <RescheduleContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        plates,
        setPlates,
        appointmentInfo,
        setAppointmentInfo,
        selectedDate,
        setSelectedDate,
        selectedSlot,
        setSelectedSlot,
        availableSlots,
        setAvailableSlots,
        loading,
        setLoading,
        error,
        setError,
      }}
    >
      {children}
    </RescheduleContext.Provider>
  )
}

export function useReschedule() {
  const context = useContext(RescheduleContext)
  if (context === undefined) {
    throw new Error("useReschedule must be used within a RescheduleProvider")
  }
  return context
}
