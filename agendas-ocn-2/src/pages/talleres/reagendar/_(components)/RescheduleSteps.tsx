"use client"

import { useState } from "react"
import { RescheduleProvider } from "./RescheduleContext"
import PlatesForm from "./PlatesForm"
import RescheduleCalendar from "./RescheduleCalendar"
import RescheduleTimeSlotSelector from "./RescheduleTimeSlotSelector"
import RescheduleSummary from "./RescheduleSummary"
import RescheduleSuccessScreen from "./RescheduleSuccessScreen"
import NoAppointmentScreen from "./NoAppointmentScreen"
import { useReschedule } from "./RescheduleContext"

export default function RescheduleSteps() {
  return (
    <RescheduleProvider>
      <StepsContainer />
    </RescheduleProvider>
  )
}

function StepsContainer() {
  const { currentStep, appointmentInfo } = useReschedule()

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <PlatesForm />
      case 2:
        // Si no hay appointment, mostrar pantalla de error
        if (!appointmentInfo?.appointment) {
          return <NoAppointmentScreen />
        }
        return <RescheduleCalendar />
      case 3:
        return <RescheduleTimeSlotSelector />
      case 4:
        return <RescheduleSummary />
      case 5:
        return <RescheduleSuccessScreen />
      default:
        return <PlatesForm />
    }
  }

  return (
    <div className="w-full max-w-md mx-auto">
      {renderStep()}
    </div>
  )
}
