"use client"

import { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Calendar, Clock, MapPin, Car, User, Gauge } from "lucide-react"
import { useAppointment } from "./AppointmentContext"
import { DateTime } from "luxon"
import { actions } from 'astro:actions'
import toast from "react-hot-toast"

export default function AppointmentSummary({ setError }: { setError: (error: string | null) => void }) {
  const {
    appointmentInfo,
    selectedWorkshop,
    selectedServiceType,
    selectedDate,
    selectedSlot,
    setCurrentStep,
    km,
    loading,
    setLoading
  } = useAppointment()

  // Reiniciar el error al montar el componente
  useEffect(() => {
    setError(null)
  }, [setError])

  if (!appointmentInfo || !selectedWorkshop || !selectedServiceType || !selectedDate || !selectedSlot) {
    return null
  }

  const goBack = () => {
    setCurrentStep(4)
  }

  const handleConfirmAppointment = async () => {
    try {
      setLoading(true)
      setError(null)

      const result = await actions.bookWorkshopAppointment({
        workshopId: selectedWorkshop._id,
        serviceTypeId: selectedServiceType._id,
        startTime: selectedSlot,
        associateId: appointmentInfo.associate._id,
        stockId: appointmentInfo.stockVehicle._id,
        km: parseInt(km)
      })

      if (!result.data?.success) {
        toast.error(result.data?.error?.message || "Error al agendar la cita")
        setError(result.data?.error?.message || "Error al agendar la cita")
        return
      }

      // Avanzar a la pantalla de éxito
      setCurrentStep(6)
    } catch (error: any) {
      toast.error(error.message || "Error al agendar la cita")
      setError(error.message || "Error al agendar la cita")
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('es-ES', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  const formatTime = (isoString: string) => {
    const time = DateTime.fromISO(isoString)
      .toFormat('hh:mm a')
      .toLowerCase()
    return time.toUpperCase()
  }
  return (
    <div className="space-y-4">
      <Button variant="ghost" onClick={goBack} className="mb-4">
        <ArrowLeft className="mr-2" /> Regresar
      </Button>

      <h2 className="text-xl font-bold text-center">Confirma tu cita</h2>

      <div className="bg-gray-50 p-4 rounded-lg space-y-4 mt-4 text-left">
        <div className="flex items-start gap-3">
          <Calendar className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium ">Fecha</p>
            <p className="text-gray-600">{formatDate(selectedDate)}</p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Clock className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Hora</p>
            <p className="text-gray-600">{formatTime(selectedSlot)}</p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <MapPin className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Taller</p>
            <p className="text-gray-600">{selectedWorkshop.name}</p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Car className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Vehículo</p>
            <p className="text-gray-600">
              {appointmentInfo.stockVehicle.brand} {appointmentInfo.stockVehicle.model} -
              Placas: {appointmentInfo.stockVehicle.carPlates.plates}
            </p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Gauge className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Kilometraje Registrado</p>
            <p className="text-gray-600">{km} km</p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <Gauge className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Kilometraje de mantenimiento</p>
            <p className="text-gray-600">{appointmentInfo.maintenanceKm} km</p>
          </div>
        </div>

        <div className="flex items-start gap-3">
          <User className="h-5 w-5 text-violet-700 mt-0.5" />
          <div>
            <p className="font-medium">Nombre del conductor</p>
            <p className="text-gray-600">
              {appointmentInfo.associate.firstName} {appointmentInfo.associate.lastName}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-violet-50 p-4 rounded-lg mt-4">
        <h3 className="font-medium text-violet-800">Detalles del servicio</h3>
        <p className="text-violet-700 mt-1">
          {selectedServiceType.name} - Mantenimiento #{appointmentInfo.maintenanceNumber}
        </p>
        <p className="text-sm text-violet-600 mt-1">
          Duración aproximada: {selectedServiceType.duration} minutos
        </p>
      </div>

      <Button
        className="w-full bg-violet-700 hover:bg-violet-800 text-white mt-6"
        onClick={handleConfirmAppointment}
        disabled={loading}
      >
        {loading ? "Agendando..." : "Confirmar cita"}
      </Button>
    </div>
  )
}