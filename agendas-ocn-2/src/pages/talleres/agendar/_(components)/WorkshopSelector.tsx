"use client"

import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft } from "lucide-react"
import { useAppointment } from "./AppointmentContext"
import { type ServiceType } from "./AppointmentContext"

export default function WorkshopSelector({ setError }: { setError: (error: string | null) => void }) {
  const {
    appointmentInfo,
    setCurrentStep,
    selectedWorkshop,
    setSelectedWorkshop,
    setSelectedServiceType
  } = useAppointment()


  // Reiniciar el error al montar el componente
  useEffect(() => {
    setError(null)
  }, [setError])

  if (!appointmentInfo) {
    return null
  }

  const handleWorkshopSelect = (workshopId: string) => {
    const workshop = appointmentInfo.workshops.find(w => w._id === workshopId)
    if (!workshop) return

    setSelectedWorkshop(workshop)

    // Encontrar el serviceType correspondiente
    // Asumimos que hay un serviceType por workshop
    const serviceType = appointmentInfo.serviceTypes[0]
    if (serviceType) {
      setSelectedServiceType(serviceType)
    }
  }

  const handleContinue = () => {
    if (!selectedWorkshop) return
    setCurrentStep(3)
  }

  const goBack = () => {
    setCurrentStep(1)
  }

  return (
    <div className="space-y-4">
      <Button variant="ghost" onClick={goBack} className="mb-4">
        <ArrowLeft className="mr-2" /> Regresar
      </Button>

      <h2 className="text-xl font-bold text-center">Selecciona el taller</h2>

      <div className="mt-4">
        <Select
          value={selectedWorkshop?._id || ""}
          onValueChange={handleWorkshopSelect}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Selecciona un taller" />
          </SelectTrigger>
          <SelectContent>
            {appointmentInfo.workshops.map(workshop => (
              <SelectItem key={workshop._id} value={workshop._id}>
                {workshop.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {selectedWorkshop && (
        <div className="mt-4 p-4 bg-gray-50 rounded-md">
          <h3 className="font-medium">Información del servicio:</h3>
          <p className="text-sm mt-2">Mantenimiento #{appointmentInfo.maintenanceNumber}</p>
          <p className="text-sm">Kilometraje de mantenimiento: {appointmentInfo.maintenanceKm} km</p>
          <p className="text-sm">Tipo de servicio: {appointmentInfo.serviceTypes[0]?.name}</p>
          <p className="text-sm">Duración: {appointmentInfo.serviceTypes[0]?.duration} minutos</p>
        </div>
      )}

      <Button
        className="w-full bg-violet-700 hover:bg-violet-800 text-white mt-4"
        onClick={handleContinue}
        disabled={!selectedWorkshop}
      >
        Continuar
      </Button>
    </div>
  )
}