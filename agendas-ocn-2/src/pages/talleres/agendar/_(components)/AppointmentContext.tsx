"use client"

import { create<PERSON>ontex<PERSON>, useContext, useState, type ReactNode } from "react"

// Definir los tipos
export interface Associate {
  _id: string
  firstName: string
  lastName: string
  email: string
  phone: string
}

export interface StockVehicle {
  _id: string
  brand: string
  model: string
  carPlates: {
    plates: string
  }
  carNumber: string
  contractNumber: string
}

export interface ServiceType {
  _id: string
  name: string
  duration: number
}

export interface Workshop {
  _id: string
  name: string
  address: string
  scheduleConfig: {
    weeklySchedule: {
      monday?: { start: string; end: string }
      tuesday?: { start: string; end: string }
      wednesday?: { start: string; end: string }
      thursday?: { start: string; end: string }
      friday?: { start: string; end: string }
      saturday?: { start: string; end: string }
      sunday?: { start: string; end: string }
    }
  }
}

export interface AppointmentInfo {
  associate: Associate
  stockVehicle: StockVehicle
  serviceTypes: ServiceType[]
  workshops: Workshop[]
  maintenanceNumber: number
  maintenanceKm: number
  requiredKm: number
  appointment?: {
    _id: string
    workshopId: string
    workshop: {
      _id: string
      name: string
      location: {
        mapsLink: string
      }
    }
    startTime: string
    endTime: string
    duration: number
    maintenanceKm: number
    data: {
      registeredKm: number
      maintenanceNumber: number
    }
    serviceType: ServiceType
    associate: Associate
    stockVehicle: StockVehicle
  }
}

interface AppointmentContextType {
  currentStep: number
  setCurrentStep: (step: number) => void
  plates: string
  setPlates: (plates: string) => void
  km: string
  setKm: (km: string) => void
  appointmentInfo: AppointmentInfo | null
  setAppointmentInfo: (info: AppointmentInfo | null) => void
  selectedWorkshop: Workshop | null
  setSelectedWorkshop: (workshop: Workshop | null) => void
  selectedServiceType: ServiceType | null
  setSelectedServiceType: (serviceType: ServiceType | null) => void
  selectedDate: Date | null
  setSelectedDate: (date: Date | null) => void
  availableSlots: string[]
  setAvailableSlots: (slots: string[]) => void
  selectedSlot: string | null
  setSelectedSlot: (slot: string | null) => void
  loading: boolean
  setLoading: (loading: boolean) => void
}

const AppointmentContext = createContext<AppointmentContextType | undefined>(undefined)

export function AppointmentProvider({ children }: { children: ReactNode }) {
  const [currentStep, setCurrentStep] = useState(1)
  const [plates, setPlates] = useState("")
  const [km, setKm] = useState("")
  const [appointmentInfo, setAppointmentInfo] = useState<AppointmentInfo | null>(null)
  const [selectedWorkshop, setSelectedWorkshop] = useState<Workshop | null>(null)
  const [selectedServiceType, setSelectedServiceType] = useState<ServiceType | null>(null)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [availableSlots, setAvailableSlots] = useState<string[]>([])
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  return (
    <AppointmentContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        plates,
        setPlates,
        km,
        setKm,
        appointmentInfo,
        setAppointmentInfo,
        selectedWorkshop,
        setSelectedWorkshop,
        selectedServiceType,
        setSelectedServiceType,
        selectedDate,
        setSelectedDate,
        availableSlots,
        setAvailableSlots,
        selectedSlot,
        setSelectedSlot,
        loading,
        setLoading,
      }}
    >
      {children}
    </AppointmentContext.Provider>
  )
}

export function useAppointment() {
  const context = useContext(AppointmentContext)
  if (context === undefined) {
    throw new Error("useAppointment must be used within an AppointmentProvider")
  }
  return context
}
