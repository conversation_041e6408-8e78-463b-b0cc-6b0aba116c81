import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';
import { appointmentService } from './appointmentService';
import { URL_API } from '@/constants';
import axios from 'axios';

export const server = {

  submitPlates: defineAction({

    accept: 'json',
    input: z.object({
      plates: z.string().nonempty(),
    }),

    async handler({ plates }) {
      return {
        plates,
      };
    },

  }),

  rescheduleAppointment: defineAction({

    accept: 'json',
    input: z.object({
      appointmentId: z.string().nonempty(),
      startTime: z.string().nonempty(),
      serviceTypeId: z.string().nonempty(),
    }),

    async handler({ appointmentId, startTime, serviceTypeId }) {
      try {
        // console.table({
        //   appointmentId,
        //   startTime,
        //   serviceTypeId,
        // })
        const response = await appointmentService.reescheduleAppointment(appointmentId, startTime, serviceTypeId);
        return {
          success: true,
          data: response,
        } as const;

      } catch (error: any) {
        return {
          success: false,
          data: null,
          error
        } as const;

      }
    },

  }),

  getAvailableSlots: defineAction({
    accept: 'json',
    input: z.object({
      workshopId: z.string().nonempty(),
      date: z.string().nonempty(),
      serviceTypeId: z.string().nonempty(),
    }),

    async handler({ workshopId, date, serviceTypeId }) {
      const response = await appointmentService.getAvailableSlots(workshopId, date, serviceTypeId);
      return response.data;
    },

  }),

  /* COMPANY ACTIONS ======================================================== */

  validatePlatesAndReturnData: defineAction({
    accept: 'json',
    input: z.object({
      plates: z.string().nonempty(),
    }),

    async handler({ plates }) {
      try {
        const result = await axios.post(`${URL_API}/vendor-platform/validate-plates/neighborhoods`, { plates });
        return result.data.data;

      } catch (error) {
        console.log('error', error);
        throw error;
      }
    },

  }),

  getAvailableAppointments: defineAction({
    accept: 'json',
    input: z.object({
      neighborhoodId: z.string().nonempty(),
      date: z.string().nonempty(),
    }),
    async handler({ neighborhoodId, date }) {

      const parsedDate = date.includes('T') ? date.split('T')[0] : date;
      const result = await axios.get(`${URL_API}/vendor-platform/installation-slots/${neighborhoodId}/${parsedDate}`);
      console.log('APPOINTMENTS DATA', result.data.data);
      return result.data.data;

    },

  }),

  bookAppointment: defineAction({
    accept: 'json',
    input: z.object({
      // appointmentRequest: z.object({
      stockId: z.string().nonempty(),
      associateId: z.string().nonempty(),
      neighborhoodId: z.string().nonempty(),
      startTime: z.string().nonempty(),
      address: z.object({
        street: z.string().nonempty(),
        exteriorNumber: z.string().nonempty(),
        interiorNumber: z.string().optional().default(''),
        colony: z.string().nonempty(),
        zipCode: z.string().nonempty(),
        references: z.string().optional(),
      }),
      mls: z.boolean().optional().default(false),
    }),

    async handler(input) {
      if (!input.mls) {
        throw new Error('Something went wrong');
      }
      try {
        const result = await axios.post(`${URL_API}/vendor-platform/public/installation-appointments`, input);
        return result.data.data;

      } catch (error: any) {
        console.log('error', error.response?.data || error.message);
        throw error;
      }
    },

  }),

  uploadPhotos: defineAction({
    // accept: 'json',
    // input: z.object({
    //   appointmentId: z.string().nonempty(),
    //   // photos: z.array(z.instanceof(File)),
    //   // photos is an array of any
    //   photos: z.array(z.any()),
    // }),
    accept: 'form',
    async handler(formData: any) {
      try {

        const appointmentId = formData.get('appointmentId');
        formData.set('source', 'driver');

        // From photos of formData, change it to proofImages key and remove photos key
        // formData.set('proofImages', formData.getAll('photos'));

        const photos = formData.getAll('photos');
        // console.log('photos', photos);
        // formData.set('proofImages', photos);
        photos.forEach((photo: any) => {
          formData.append('proofImages', photo);
        });

        formData.delete('photos');

        // log everything in form data
        for (const [key, value] of formData.entries()) {
          console.log(key, value);
        }
        // return;
        const result = await axios.patch(`${URL_API}/vendor-platform/public/installation-appointments/${appointmentId}/proof`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        console.log('result', result.data);
        // return result.data;
        return true;

      } catch (error: any) {
        console.log('Error message: ', error.message);
        console.log('error', error?.response?.data || error.message);
        throw error;
      }
    },

  }),

  rescheduleInstallationAppointment: defineAction({
    accept: 'json',
    input: z.object({
      appointmentId: z.string().nonempty(),
      startTime: z.string().nonempty(),
    }),

    async handler({ appointmentId, startTime }) {
      const response = await axios.patch(`${URL_API}/vendor-platform/public/installation-appointments/${appointmentId}/reschedule`, { startTime });

      return response.data;
    },
  }),

  // Nuevas acciones para el agendamiento de citas de mantenimiento
  getAppointmentInfo: defineAction({
    accept: 'json',
    input: z.object({
      plates: z.string().nonempty(),
      km: z.number().positive(),
    }),

    async handler({ plates, km }) {
      try {
        const url = `${URL_API}/vendor-platform/public/workshops/appointment-info?plates=${plates}&km=${km}`;
        const result = await axios.get(url);
        return {
          success: true,
          data: result.data.data,
        };
      } catch (error: any) {
        console.log('error', error.response?.data || error.message);
        return {
          success: false,
          error: {
            message: error.response?.data?.message || 'Error al obtener información de la cita',
          },
        };
      }
    },
  }),

  getAppointmentInfoForReschedule: defineAction({
    accept: 'json',
    input: z.object({
      plates: z.string().nonempty(),
    }),

    async handler({ plates }) {
      try {
        // Para reagendamiento, enviamos km=0 ya que no es relevante
        const url = `${URL_API}/vendor-platform/public/workshops/appointment-info?plates=${plates}&km=0&isReschedule=true`;
        const result = await axios.get(url);
        return {
          success: true,
          data: result.data.data,
        };
      } catch (error: any) {
        console.log('error', error.response?.data || error.message);
        return {
          success: false,
          error: {
            message: error.response?.data?.message || 'Error al obtener información de la cita',
          },
        };
      }
    },
  }),

  getAvailableWorkshopSlots: defineAction({
    accept: 'json',
    input: z.object({
      workshopId: z.string().nonempty(),
      date: z.string().nonempty(),
      serviceTypeId: z.string().nonempty(),
    }),

    async handler({ workshopId, date, serviceTypeId }) {
      try {
        const result = await axios.get(`${URL_API}/vendor-platform/public/workshops/${workshopId}/available-slots/${date}/${serviceTypeId}`);
        return {
          success: true,
          data: result.data.data,
        };
      } catch (error: any) {
        console.log('error', error.response?.data || error.message);
        return {
          success: false,
          error: {
            message: error.response?.data?.message || 'Error al obtener horarios disponibles',
          },
        };
      }
    },
  }),

  bookWorkshopAppointment: defineAction({
    accept: 'json',
    input: z.object({
      workshopId: z.string().nonempty(),
      serviceTypeId: z.string().nonempty(),
      startTime: z.string().nonempty(),
      associateId: z.string().nonempty(),
      stockId: z.string().nonempty(),
      km: z.number().positive(),
    }),

    async handler({ workshopId, serviceTypeId, startTime, associateId, stockId, km }) {
      try {
        const payload = {
          serviceTypeId,
          startTime,
          associateId,
          stockId,
          km
        };

        const result = await axios.post(`${URL_API}/vendor-platform/public/workshops/${workshopId}/appointments`, payload);

        return {
          success: true,
          data: result.data.data,
        };
      } catch (error: any) {
        console.log('error', error.response?.data || error.message);
        return {
          success: false,
          error: {
            message: error.response?.data?.message || 'Error al agendar la cita',
          },
        };
      }
    },
  }),

  submitVerification: defineAction({
    accept: 'form',
    async handler(formData: any) {
      try {
        const plate = formData.get('plate');
        const nextVerificationDate = formData.get('nextVerificationDate');
        const hologramType = formData.get('hologramType');
        const isHybridElectric = formData.get('isHybridElectric') === 'true';
        const registrationDate = formData.get('registrationDate');
        
        // TODO: Implement actual API endpoint for verification submission
        // For now, just simulate the upload and log the new verification data
        console.log('Verification submission:', {
          plate,
          nextVerificationDate,
          hologramType,
          isHybridElectric,
          registrationDate,
          certificate: formData.get('certificate')?.name,
          hologram: formData.get('hologram')?.name
        });

        // Here you would typically:
        // 1. Upload files to cloud storage
        // 2. Save verification data to database
        // 3. Schedule next verification reminder based on calculated date
        // 4. Send confirmation to customer

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return {
          success: true,
          message: 'Verificación enviada exitosamente',
          data: {
            plate,
            nextVerificationDate,
            hologramType,
            isExempt: hologramType === '00' || isHybridElectric
          }
        };

      } catch (error: any) {
        console.log('Error in verification submission:', error.message);
        return {
          success: false,
          error: error.message || 'Error al enviar la verificación'
        };
      }
    },
  }),

};
